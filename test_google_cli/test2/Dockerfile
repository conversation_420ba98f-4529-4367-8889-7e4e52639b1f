# Multi-stage Docker build for Enhanced Web Server

# Build stage
FROM ubuntu:22.04 AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    pkg-config \
    libsqlite3-dev \
    zlib1g-dev \
    libssl-dev \
    clang-format \
    cppcheck \
    doxygen \
    graphviz \
    lcov \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy source code
COPY . .

# Create build directory
RUN mkdir -p build

# Configure and build
WORKDIR /app/build
RUN cmake -DCMAKE_BUILD_TYPE=Release \
          -DCMAKE_INSTALL_PREFIX=/usr/local \
          .. && \
    make -j$(nproc) && \
    make install

# Run tests during build
RUN ./run_tests

# Production stage
FROM ubuntu:22.04 AS production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libsqlite3-0 \
    zlib1g \
    libssl3 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r webserver && useradd -r -g webserver webserver

# Create application directories
RUN mkdir -p /app/logs /app/data /app/static /app/config && \
    chown -R webserver:webserver /app

# Copy built application from builder stage
COPY --from=builder /usr/local/bin/webserver /app/webserver
COPY --from=builder /usr/local/share/webserver/static /app/static
COPY --from=builder /usr/local/etc/webserver /app/config

# Copy additional configuration files
COPY docker/server.conf /app/config/
COPY docker/entrypoint.sh /app/
RUN chmod +x /app/entrypoint.sh

# Set working directory
WORKDIR /app

# Switch to non-root user
USER webserver

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Set entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["./webserver"]

# Development stage
FROM builder AS development

# Install additional development tools
RUN apt-get update && apt-get install -y \
    gdb \
    valgrind \
    strace \
    curl \
    wget \
    vim \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Expose additional ports for debugging
EXPOSE 8080 9090

# Override entrypoint for development
ENTRYPOINT ["/bin/bash"]

# Testing stage
FROM builder AS testing

# Set working directory
WORKDIR /app/build

# Run comprehensive tests
RUN ./run_tests --verbose && \
    ./run_benchmarks && \
    make coverage || true

# Copy test results
RUN mkdir -p /test-results && \
    cp -r coverage /test-results/ || true && \
    cp test_results.xml /test-results/ || true

# Minimal stage for size-optimized production
FROM alpine:3.18 AS minimal

# Install minimal runtime dependencies
RUN apk add --no-cache \
    libstdc++ \
    sqlite \
    zlib \
    openssl \
    ca-certificates

# Create non-root user
RUN addgroup -S webserver && adduser -S webserver -G webserver

# Create application directories
RUN mkdir -p /app/logs /app/data /app/static /app/config && \
    chown -R webserver:webserver /app

# Copy built application (statically linked version would be needed)
COPY --from=builder /usr/local/bin/webserver /app/webserver
COPY --from=builder /usr/local/share/webserver/static /app/static
COPY --from=builder /usr/local/etc/webserver /app/config

# Copy configuration
COPY docker/server.conf /app/config/
COPY docker/entrypoint-minimal.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Set working directory
WORKDIR /app

# Switch to non-root user
USER webserver

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Set entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["./webserver"]

# Default to production stage
FROM production
