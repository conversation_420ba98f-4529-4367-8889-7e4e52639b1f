#ifndef TESTING_TEST_FRAMEWORK_H
#define TESTING_TEST_FRAMEWORK_H

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <chrono>
#include <unordered_map>
#include <sstream>
#include <exception>
#include <typeinfo>
#include <iostream>
#include <fstream>
#include "../utilities/Logger.h"
#include "../core/Application.h"

namespace testing {

/**
 * @brief Test assertion macros and utilities
 */
#define TEST_ASSERT(condition) \
    do { \
        if (!(condition)) { \
            throw testing::AssertionError("Assertion failed: " #condition, __FILE__, __LINE__); \
        } \
    } while(0)

#define TEST_ASSERT_EQ(expected, actual) \
    do { \
        if ((expected) != (actual)) { \
            std::ostringstream oss; \
            oss << "Expected: " << (expected) << ", Actual: " << (actual); \
            throw testing::AssertionError(oss.str(), __FILE__, __LINE__); \
        } \
    } while(0)

#define TEST_ASSERT_NE(expected, actual) \
    do { \
        if ((expected) == (actual)) { \
            std::ostringstream oss; \
            oss << "Expected not equal to: " << (expected) << ", but got: " << (actual); \
            throw testing::AssertionError(oss.str(), __FILE__, __LINE__); \
        } \
    } while(0)

#define TEST_ASSERT_TRUE(condition) TEST_ASSERT((condition) == true)
#define TEST_ASSERT_FALSE(condition) TEST_ASSERT((condition) == false)
#define TEST_ASSERT_NULL(ptr) TEST_ASSERT((ptr) == nullptr)
#define TEST_ASSERT_NOT_NULL(ptr) TEST_ASSERT((ptr) != nullptr)

#define TEST_ASSERT_THROWS(exception_type, code) \
    do { \
        bool threw = false; \
        try { \
            code; \
        } catch (const exception_type&) { \
            threw = true; \
        } catch (...) { \
            throw testing::AssertionError("Expected " #exception_type " but got different exception", __FILE__, __LINE__); \
        } \
        if (!threw) { \
            throw testing::AssertionError("Expected " #exception_type " but no exception was thrown", __FILE__, __LINE__); \
        } \
    } while(0)

#define TEST_ASSERT_NO_THROW(code) \
    do { \
        try { \
            code; \
        } catch (...) { \
            throw testing::AssertionError("Expected no exception but one was thrown", __FILE__, __LINE__); \
        } \
    } while(0)

/**
 * @brief Test case definition macro
 */
#define TEST_CASE(test_name) \
    void test_name(); \
    static testing::TestRegistrar test_name##_registrar(#test_name, test_name); \
    void test_name()

/**
 * @brief Test fixture definition macro
 */
#define TEST_FIXTURE(fixture_name, test_name) \
    class fixture_name##_##test_name##_Test : public fixture_name { \
    public: \
        void run(); \
    }; \
    static testing::FixtureTestRegistrar<fixture_name##_##test_name##_Test> \
        fixture_name##_##test_name##_registrar(#fixture_name "::" #test_name); \
    void fixture_name##_##test_name##_Test::run()

/**
 * @brief Benchmark definition macro
 */
#define BENCHMARK(benchmark_name) \
    void benchmark_name(testing::BenchmarkState& state); \
    static testing::BenchmarkRegistrar benchmark_name##_registrar(#benchmark_name, benchmark_name); \
    void benchmark_name(testing::BenchmarkState& state)

/**
 * @brief Test exceptions
 */
class AssertionError : public std::exception {
private:
    std::string message_;
    std::string file_;
    int line_;
    
public:
    AssertionError(const std::string& message, const std::string& file, int line)
        : message_(message), file_(file), line_(line) {}
    
    const char* what() const noexcept override { return message_.c_str(); }
    const std::string& getMessage() const { return message_; }
    const std::string& getFile() const { return file_; }
    int getLine() const { return line_; }
    
    std::string toString() const {
        std::ostringstream oss;
        oss << file_ << ":" << line_ << " - " << message_;
        return oss.str();
    }
};

/**
 * @brief Test result
 */
struct TestResult {
    std::string name;
    bool passed = false;
    std::string error_message;
    std::string file;
    int line = 0;
    std::chrono::milliseconds duration{0};
    
    std::string toString() const {
        std::ostringstream oss;
        oss << "[" << (passed ? "PASS" : "FAIL") << "] " << name;
        if (!passed && !error_message.empty()) {
            oss << " - " << error_message;
            if (!file.empty()) {
                oss << " (" << file << ":" << line << ")";
            }
        }
        oss << " (" << duration.count() << "ms)";
        return oss.str();
    }
};

/**
 * @brief Test suite
 */
class TestSuite {
private:
    std::string name_;
    std::vector<std::function<void()>> tests_;
    std::vector<std::string> test_names_;
    std::vector<TestResult> results_;
    
    std::function<void()> setup_func_;
    std::function<void()> teardown_func_;
    
public:
    explicit TestSuite(const std::string& name) : name_(name) {}
    
    void addTest(const std::string& test_name, std::function<void()> test_func) {
        test_names_.push_back(test_name);
        tests_.push_back(test_func);
    }
    
    void setSetup(std::function<void()> setup) { setup_func_ = setup; }
    void setTeardown(std::function<void()> teardown) { teardown_func_ = teardown; }
    
    void run() {
        results_.clear();
        results_.reserve(tests_.size());
        
        for (size_t i = 0; i < tests_.size(); ++i) {
            TestResult result;
            result.name = test_names_[i];
            
            auto start_time = std::chrono::high_resolution_clock::now();
            
            try {
                if (setup_func_) setup_func_();
                tests_[i]();
                if (teardown_func_) teardown_func_();
                
                result.passed = true;
            } catch (const AssertionError& e) {
                result.passed = false;
                result.error_message = e.getMessage();
                result.file = e.getFile();
                result.line = e.getLine();
            } catch (const std::exception& e) {
                result.passed = false;
                result.error_message = std::string("Unexpected exception: ") + e.what();
            } catch (...) {
                result.passed = false;
                result.error_message = "Unknown exception";
            }
            
            auto end_time = std::chrono::high_resolution_clock::now();
            result.duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            results_.push_back(result);
        }
    }
    
    const std::string& getName() const { return name_; }
    const std::vector<TestResult>& getResults() const { return results_; }
    
    size_t getPassedCount() const {
        return std::count_if(results_.begin(), results_.end(),
                           [](const TestResult& r) { return r.passed; });
    }
    
    size_t getFailedCount() const {
        return results_.size() - getPassedCount();
    }
    
    std::chrono::milliseconds getTotalDuration() const {
        std::chrono::milliseconds total{0};
        for (const auto& result : results_) {
            total += result.duration;
        }
        return total;
    }
};

/**
 * @brief Test fixture base class
 */
class TestFixture {
public:
    virtual ~TestFixture() = default;
    virtual void setUp() {}
    virtual void tearDown() {}
};

/**
 * @brief Benchmark state for performance testing
 */
class BenchmarkState {
private:
    size_t iterations_;
    std::chrono::high_resolution_clock::time_point start_time_;
    std::chrono::high_resolution_clock::time_point end_time_;
    bool timing_started_ = false;
    
public:
    explicit BenchmarkState(size_t iterations) : iterations_(iterations) {}
    
    void startTiming() {
        start_time_ = std::chrono::high_resolution_clock::now();
        timing_started_ = true;
    }
    
    void stopTiming() {
        end_time_ = std::chrono::high_resolution_clock::now();
    }
    
    size_t getIterations() const { return iterations_; }
    
    std::chrono::nanoseconds getDuration() const {
        if (!timing_started_) return std::chrono::nanoseconds{0};
        return std::chrono::duration_cast<std::chrono::nanoseconds>(end_time_ - start_time_);
    }
    
    double getAverageNanoseconds() const {
        return static_cast<double>(getDuration().count()) / iterations_;
    }
    
    double getOperationsPerSecond() const {
        auto duration_seconds = std::chrono::duration_cast<std::chrono::duration<double>>(getDuration());
        return iterations_ / duration_seconds.count();
    }
};

/**
 * @brief Benchmark result
 */
struct BenchmarkResult {
    std::string name;
    size_t iterations;
    std::chrono::nanoseconds total_time;
    double average_ns;
    double ops_per_second;
    
    std::string toString() const {
        std::ostringstream oss;
        oss << "[BENCH] " << name << " - " << iterations << " iterations, "
            << average_ns << " ns/op, " << ops_per_second << " ops/sec";
        return oss.str();
    }
};

/**
 * @brief Test runner
 */
class TestRunner {
private:
    std::vector<std::unique_ptr<TestSuite>> test_suites_;
    std::vector<std::function<void()>> standalone_tests_;
    std::vector<std::string> standalone_test_names_;
    std::vector<std::pair<std::string, std::function<void(BenchmarkState&)>>> benchmarks_;
    
    std::shared_ptr<utilities::Logger> logger_;
    
    bool verbose_ = false;
    std::string output_file_;
    
public:
    TestRunner();
    
    void addTestSuite(std::unique_ptr<TestSuite> suite);
    void addTest(const std::string& name, std::function<void()> test);
    void addBenchmark(const std::string& name, std::function<void(BenchmarkState&)> benchmark);
    
    void setVerbose(bool verbose) { verbose_ = verbose; }
    void setOutputFile(const std::string& file) { output_file_ = file; }
    
    struct RunResult {
        size_t total_tests = 0;
        size_t passed_tests = 0;
        size_t failed_tests = 0;
        std::chrono::milliseconds total_duration{0};
        std::vector<TestResult> failed_test_details;
        std::vector<BenchmarkResult> benchmark_results;
    };
    
    RunResult runTests();
    RunResult runBenchmarks(size_t iterations = 1000);
    RunResult runAll();
    
    void printResults(const RunResult& result);
    void saveResults(const RunResult& result, const std::string& filename);
    
    // Filtering
    void setTestFilter(const std::string& pattern);
    void setBenchmarkFilter(const std::string& pattern);
    
private:
    std::string test_filter_;
    std::string benchmark_filter_;
    
    bool matchesFilter(const std::string& name, const std::string& filter);
    void writeXmlReport(const RunResult& result, const std::string& filename);
    void writeJsonReport(const RunResult& result, const std::string& filename);
};

/**
 * @brief Test registrar for automatic test registration
 */
class TestRegistrar {
public:
    TestRegistrar(const std::string& name, std::function<void()> test);
};

/**
 * @brief Fixture test registrar
 */
template<typename FixtureType>
class FixtureTestRegistrar {
public:
    FixtureTestRegistrar(const std::string& name) {
        auto test_func = []() {
            FixtureType fixture;
            fixture.setUp();
            try {
                fixture.run();
                fixture.tearDown();
            } catch (...) {
                fixture.tearDown();
                throw;
            }
        };
        
        TestRegistry::getInstance().addTest(name, test_func);
    }
};

/**
 * @brief Benchmark registrar
 */
class BenchmarkRegistrar {
public:
    BenchmarkRegistrar(const std::string& name, std::function<void(BenchmarkState&)> benchmark);
};

/**
 * @brief Global test registry
 */
class TestRegistry {
private:
    std::vector<std::pair<std::string, std::function<void()>>> tests_;
    std::vector<std::pair<std::string, std::function<void(BenchmarkState&)>>> benchmarks_;
    
    TestRegistry() = default;
    
public:
    static TestRegistry& getInstance() {
        static TestRegistry instance;
        return instance;
    }
    
    void addTest(const std::string& name, std::function<void()> test) {
        tests_.emplace_back(name, test);
    }
    
    void addBenchmark(const std::string& name, std::function<void(BenchmarkState&)> benchmark) {
        benchmarks_.emplace_back(name, benchmark);
    }
    
    const std::vector<std::pair<std::string, std::function<void()>>>& getTests() const {
        return tests_;
    }
    
    const std::vector<std::pair<std::string, std::function<void(BenchmarkState&)>>>& getBenchmarks() const {
        return benchmarks_;
    }
    
    void clear() {
        tests_.clear();
        benchmarks_.clear();
    }
};

/**
 * @brief Mock framework for testing
 */
template<typename T>
class Mock {
private:
    std::unordered_map<std::string, std::function<void()>> expectations_;
    std::unordered_map<std::string, size_t> call_counts_;
    
public:
    template<typename ReturnType, typename... Args>
    void expect(const std::string& method_name, ReturnType return_value, Args... args) {
        expectations_[method_name] = [=]() {
            // Store expectation details
        };
    }
    
    void recordCall(const std::string& method_name) {
        call_counts_[method_name]++;
    }
    
    size_t getCallCount(const std::string& method_name) const {
        auto it = call_counts_.find(method_name);
        return it != call_counts_.end() ? it->second : 0;
    }
    
    void verify() {
        for (const auto& [method, expectation] : expectations_) {
            expectation();
        }
    }
    
    void reset() {
        expectations_.clear();
        call_counts_.clear();
    }
};

/**
 * @brief Test utilities
 */
class TestUtils {
public:
    // Temporary file management
    static std::string createTempFile(const std::string& content = "");
    static std::string createTempDir();
    static void removeTempFile(const std::string& path);
    static void removeTempDir(const std::string& path);
    
    // String utilities
    static std::vector<std::string> split(const std::string& str, char delimiter);
    static std::string trim(const std::string& str);
    static bool startsWith(const std::string& str, const std::string& prefix);
    static bool endsWith(const std::string& str, const std::string& suffix);
    
    // Time utilities
    static void sleep(std::chrono::milliseconds duration);
    static std::chrono::milliseconds measureTime(std::function<void()> func);
    
    // Random data generation
    static std::string generateRandomString(size_t length);
    static int generateRandomInt(int min, int max);
    static double generateRandomDouble(double min, double max);
};

} // namespace testing

#endif // TESTING_TEST_FRAMEWORK_H
