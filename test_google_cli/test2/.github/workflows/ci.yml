name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, develop ]

env:
  BUILD_TYPE: Release
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code quality checks
  lint:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y clang-format cppcheck
    
    - name: Check code formatting
      run: |
        find . -name "*.cpp" -o -name "*.h" | xargs clang-format --dry-run --Werror
    
    - name: Run static analysis
      run: |
        cppcheck --enable=all --std=c++17 --error-exitcode=1 \
          --suppress=missingIncludeSystem \
          --suppress=unusedFunction \
          .

  # Build and test on multiple platforms
  build-and-test:
    name: Build and Test
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        build_type: [Debug, Release]
        include:
          - os: ubuntu-latest
            generator: "Unix Makefiles"
          - os: windows-latest
            generator: "Visual Studio 17 2022"
          - os: macos-latest
            generator: "Unix Makefiles"
    
    runs-on: ${{ matrix.os }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential cmake pkg-config \
          libsqlite3-dev zlib1g-dev libssl-dev \
          lcov
    
    - name: Install dependencies (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        choco install cmake
        vcpkg install sqlite3 zlib openssl
    
    - name: Install dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install cmake sqlite3 zlib openssl
    
    - name: Configure CMake
      run: |
        cmake -B build -G "${{ matrix.generator }}" \
          -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
          -DCMAKE_INSTALL_PREFIX=install
    
    - name: Build
      run: cmake --build build --config ${{ matrix.build_type }} -j
    
    - name: Run tests
      working-directory: build
      run: ctest --output-on-failure -C ${{ matrix.build_type }}
    
    - name: Generate coverage report (Ubuntu Debug only)
      if: matrix.os == 'ubuntu-latest' && matrix.build_type == 'Debug'
      working-directory: build
      run: |
        make coverage || true
    
    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.build_type == 'Debug'
      uses: codecov/codecov-action@v3
      with:
        file: build/coverage.info
        flags: unittests
        name: codecov-umbrella
    
    - name: Install
      run: cmake --install build --config ${{ matrix.build_type }}
    
    - name: Package
      working-directory: build
      run: cpack -C ${{ matrix.build_type }}
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: packages-${{ matrix.os }}-${{ matrix.build_type }}
        path: build/enhanced-webserver-*

  # Security scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Docker build and push
  docker:
    name: Docker Build and Push
    runs-on: ubuntu-latest
    needs: [lint, build-and-test]
    permissions:
      contents: read
      packages: write
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        target: production
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build minimal image
      uses: docker/build-push-action@v5
      with:
        context: .
        target: minimal
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:minimal
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Performance benchmarks
  benchmark:
    name: Performance Benchmarks
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential cmake pkg-config \
          libsqlite3-dev zlib1g-dev libssl-dev
    
    - name: Configure and build
      run: |
        cmake -B build -DCMAKE_BUILD_TYPE=Release
        cmake --build build -j
    
    - name: Run benchmarks
      working-directory: build
      run: ./run_benchmarks --output=benchmark_results.json
    
    - name: Store benchmark result
      uses: benchmark-action/github-action-benchmark@v1
      with:
        tool: 'customSmallerIsBetter'
        output-file-path: build/benchmark_results.json
        github-token: ${{ secrets.GITHUB_TOKEN }}
        auto-push: true
        comment-on-alert: true
        alert-threshold: '200%'
        fail-on-alert: true

  # Documentation generation
  docs:
    name: Generate Documentation
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y doxygen graphviz
    
    - name: Generate documentation
      run: |
        mkdir -p build
        cd build
        make docs
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./build/docs/html

  # Release creation
  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [lint, build-and-test, security, docker]
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Download artifacts
      uses: actions/download-artifact@v3
      with:
        path: artifacts
    
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: artifacts/**/*
        generate_release_notes: true
        draft: false
        prerelease: ${{ contains(github.ref, '-') }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Deployment to staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: docker
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add actual deployment commands here
        # kubectl apply -f k8s/staging/
        # helm upgrade --install webserver-staging ./helm-chart

  # Deployment to production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: docker
    if: startsWith(github.ref, 'refs/tags/v')
    environment: production
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add actual deployment commands here
        # kubectl apply -f k8s/production/
        # helm upgrade --install webserver-prod ./helm-chart
