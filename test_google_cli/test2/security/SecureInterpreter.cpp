#include "SecureInterpreter.h"
#include <algorithm>
#include <sstream>
#include <chrono>

namespace security {

SecureInterpreter::SecureInterpreter(const SecurityConfig& security_config)
    : security_enabled_(false), enterprise_mode_active_(false) {
    
    try {
        // Initialize logging and error handling
        logger_ = utilities::Logger::getLogger("SecureInterpreter");
        error_handler_ = std::make_shared<utilities::ErrorHandler>(logger_);
        
        // Create base interpreter instance
        base_interpreter_ = std::make_unique<Interpreter>();
        
        // Initialize security components
        security_manager_ = std::make_shared<SecurityManager>(security_config);
        enterprise_handler_ = std::make_shared<EnterpriseContentHandler>(security_manager_);

        // Enable basic security for standard mode execution
        // Full security initialization is done via initializeSecurity() for enterprise features
        security_enabled_ = true;

        logger_->info("SecureInterpreter created", "SecureInterpreter", "constructor");
        logSecureExecution("SECURE_INTERPRETER_CREATED", "Instance initialized");
        
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Failed to create SecureInterpreter: " + std::string(e.what()));
        }
        security_enabled_ = false;
    }
}

SecureInterpreter::~SecureInterpreter() {
    try {
        logSecureExecution("SECURE_INTERPRETER_DESTROYED", "Instance destroyed");
        
        // Clear sensitive data
        current_session_id_.clear();
        enterprise_variables_.clear();
        clearSecurityContext();
        
        if (logger_) {
            logger_->info("SecureInterpreter destroyed", "SecureInterpreter", "destructor");
        }
    } catch (...) {
        // Suppress exceptions in destructor
    }
}

bool SecureInterpreter::initializeSecurity(const std::string& secret_key, 
                                          const std::string& enterprise_token,
                                          const std::string& organization_id) {
    try {
        logger_->info("Initializing security", "SecureInterpreter", "initializeSecurity");
        
        // Initialize security manager
        if (!security_manager_->initialize(secret_key, enterprise_token)) {
            logger_->error("Failed to initialize SecurityManager");
            return false;
        }
        
        // Initialize enterprise handler
        if (!enterprise_handler_->initialize(organization_id)) {
            logger_->error("Failed to initialize EnterpriseContentHandler");
            return false;
        }
        
        security_enabled_ = true;
        current_context_.organization_id = organization_id;
        
        logSecureExecution("SECURITY_INITIALIZED", "Organization: " + organization_id);
        onSecurityInitialized();
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Security initialization failed: " + std::string(e.what()));
        security_enabled_ = false;
        return false;
    }
}

AuthResult SecureInterpreter::authenticate(const std::string& secret_key,
                                          const std::string& enterprise_token,
                                          const std::string& client_id) {
    try {
        // If no credentials provided, allow standard mode execution
        if (secret_key.empty() && enterprise_token.empty()) {
            std::string session_id = "standard_session_" + client_id + "_" + std::to_string(std::chrono::system_clock::now().time_since_epoch().count());
            return AuthResult(true, SecurityLevel::STANDARD, session_id);
        }

        // For enterprise/hybrid mode, auto-initialize security if needed
        if (!security_manager_->isInitialized()) {
            if (!security_manager_->initialize(secret_key, enterprise_token)) {
                return AuthResult(false, SecurityLevel::INVALID, "Failed to initialize security");
            }
        }

        AuthResult result = security_manager_->authenticate(secret_key, enterprise_token, client_id);
        
        if (result.success) {
            current_session_id_ = result.message; // Session ID is returned in message field
            current_context_.session_id = current_session_id_;
            current_context_.security_level = result.level;
            
            // Set execution mode based on security level
            if (result.level == SecurityLevel::ENTERPRISE) {
                current_context_.mode = ExecutionMode::ENTERPRISE;
                enterprise_mode_active_ = true;
                onEnterpriseAccessGranted(current_session_id_);
            } else {
                current_context_.mode = ExecutionMode::STANDARD;
                enterprise_mode_active_ = false;
            }
            
            logSecureExecution("AUTHENTICATION_SUCCESS", 
                             "Level: " + std::to_string(static_cast<int>(result.level)) + 
                             ", Session: " + current_session_id_);
            onSessionCreated(current_session_id_);
        } else {
            logSecureExecution("AUTHENTICATION_FAILED", "Reason: " + result.message);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        logger_->error("Authentication error: " + std::string(e.what()));
        return AuthResult(false, SecurityLevel::INVALID, "Authentication error");
    }
}

SecureInterpretationResult SecureInterpreter::interpretSecure(const std::string& code,
                                                            const std::string& secret_key,
                                                            const std::string& enterprise_token,
                                                            std::function<void(const std::string&)> print_callback,
                                                            const std::string& client_id) {
    SecureInterpretationResult result;
    
    try {
        logger_->info("Starting secure interpretation", "SecureInterpreter", "interpretSecure");
        
        // Authenticate first
        AuthResult auth_result = authenticate(secret_key, enterprise_token, client_id);
        if (!auth_result.success) {
            result.success = false;
            result.error_message = "Authentication failed: " + auth_result.message;
            result.security_level = SecurityLevel::INVALID;
            return result;
        }
        
        // Analyze code and determine execution requirements
        ExecutionMode required_mode = analyzeExecutionMode(code);
        SecurityLevel required_level = determineRequiredSecurityLevel(code);
        
        // Check if current security level is sufficient
        if (required_level == SecurityLevel::ENTERPRISE && 
            current_context_.security_level != SecurityLevel::ENTERPRISE) {
            result.success = false;
            result.error_message = "Insufficient security level for enterprise content";
            result.security_level = current_context_.security_level;
            onSecurityViolation("INSUFFICIENT_PRIVILEGES", "Enterprise content access denied");
            return result;
        }
        
        // Validate code execution permissions
        if (!canExecuteCode(code)) {
            result.success = false;
            result.error_message = "Code execution not permitted";
            result.security_level = current_context_.security_level;
            onSecurityViolation("CODE_EXECUTION_DENIED", "Code validation failed");
            return result;
        }
        
        // Audit the execution
        auditCodeExecution(code, current_context_);
        
        // Preprocess code if enterprise features are needed
        std::string processed_code = code;
        if (required_mode == ExecutionMode::ENTERPRISE || required_mode == ExecutionMode::HYBRID) {
            processed_code = preprocessEnterpriseCode(code);
        }
        
        // Execute the code using base interpreter
        std::string output;
        std::string captured_output;
        
        auto capture_callback = [&captured_output, &print_callback](const std::string& text) {
            captured_output += text;
            if (print_callback) {
                print_callback(text);
            }
        };
        
        // Set DOM mode if requested
        if (current_context_.enable_real_dom) {
            base_interpreter_->enable_real_dom(true);
        }
        
        // Execute the processed code
        std::string error_output = base_interpreter_->interpret(processed_code, capture_callback);
        
        // Collect results
        result.output = captured_output;
        result.dom_operations = base_interpreter_->get_dom_operations();
        result.security_level = current_context_.security_level;
        result.execution_mode = required_mode;
        result.session_id = current_session_id_;
        result.success = error_output.empty();
        result.error_message = error_output;
        
        if (result.success) {
            logSecureExecution("INTERPRETATION_SUCCESS", 
                             "Mode: " + std::to_string(static_cast<int>(required_mode)) + 
                             ", Output length: " + std::to_string(captured_output.length()));
        } else {
            logSecureExecution("INTERPRETATION_ERROR", "Error: " + error_output);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        logger_->error("Secure interpretation error: " + std::string(e.what()));
        result.success = false;
        result.error_message = "Interpretation error: " + std::string(e.what());
        result.security_level = current_context_.security_level;
        return result;
    }
}

SecureInterpretationResult SecureInterpreter::interpretWithSession(const std::string& code,
                                                                  const std::string& session_id,
                                                                  std::function<void(const std::string&)> print_callback) {
    SecureInterpretationResult result;
    
    try {
        // Validate session
        if (!validateSession(session_id)) {
            result.success = false;
            result.error_message = "Invalid or expired session";
            result.security_level = SecurityLevel::INVALID;
            return result;
        }
        
        // Update current context
        current_session_id_ = session_id;
        current_context_.session_id = session_id;
        current_context_.security_level = security_manager_->getSecurityLevel(session_id);
        
        // Set execution mode based on security level
        if (current_context_.security_level == SecurityLevel::ENTERPRISE) {
            current_context_.mode = ExecutionMode::ENTERPRISE;
            enterprise_mode_active_ = true;
        } else {
            current_context_.mode = ExecutionMode::STANDARD;
            enterprise_mode_active_ = false;
        }
        
        // Analyze and execute code (similar to interpretSecure but without authentication)
        ExecutionMode required_mode = analyzeExecutionMode(code);
        SecurityLevel required_level = determineRequiredSecurityLevel(code);
        
        if (required_level == SecurityLevel::ENTERPRISE && 
            current_context_.security_level != SecurityLevel::ENTERPRISE) {
            result.success = false;
            result.error_message = "Insufficient security level for enterprise content";
            result.security_level = current_context_.security_level;
            onSecurityViolation("INSUFFICIENT_PRIVILEGES", "Enterprise content access denied");
            return result;
        }
        
        if (!canExecuteCode(code)) {
            result.success = false;
            result.error_message = "Code execution not permitted";
            result.security_level = current_context_.security_level;
            onSecurityViolation("CODE_EXECUTION_DENIED", "Code validation failed");
            return result;
        }
        
        auditCodeExecution(code, current_context_);
        
        std::string processed_code = code;
        if (required_mode == ExecutionMode::ENTERPRISE || required_mode == ExecutionMode::HYBRID) {
            processed_code = preprocessEnterpriseCode(code);
        }
        
        std::string captured_output;
        auto capture_callback = [&captured_output, &print_callback](const std::string& text) {
            captured_output += text;
            if (print_callback) {
                print_callback(text);
            }
        };
        
        if (current_context_.enable_real_dom) {
            base_interpreter_->enable_real_dom(true);
        }
        
        std::string error_output = base_interpreter_->interpret(processed_code, capture_callback);
        
        result.output = captured_output;
        result.dom_operations = base_interpreter_->get_dom_operations();
        result.security_level = current_context_.security_level;
        result.execution_mode = required_mode;
        result.session_id = current_session_id_;
        result.success = error_output.empty();
        result.error_message = error_output;
        
        if (result.success) {
            logSecureExecution("SESSION_INTERPRETATION_SUCCESS", 
                             "Session: " + session_id + ", Mode: " + std::to_string(static_cast<int>(required_mode)));
        } else {
            logSecureExecution("SESSION_INTERPRETATION_ERROR", "Session: " + session_id + ", Error: " + error_output);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        logger_->error("Session interpretation error: " + std::string(e.what()));
        result.success = false;
        result.error_message = "Interpretation error: " + std::string(e.what());
        result.security_level = current_context_.security_level;
        return result;
    }
}

std::string SecureInterpreter::interpret(const std::string& code, 
                                        std::function<void(const std::string&)> print_callback) {
    // Backward compatibility - execute in standard mode without security
    try {
        logSecureExecution("STANDARD_INTERPRETATION", "Backward compatibility mode");
        return base_interpreter_->interpret(code, print_callback);
    } catch (const std::exception& e) {
        logger_->error("Standard interpretation error: " + std::string(e.what()));
        return "Error: " + std::string(e.what()) + "\n";
    }
}

bool SecureInterpreter::isSecurityInitialized() const {
    return security_enabled_ && security_manager_ && security_manager_->isInitialized();
}

bool SecureInterpreter::validateSession(const std::string& session_id) {
    if (!security_enabled_ || !security_manager_) {
        return false;
    }
    return security_manager_->validateSession(session_id);
}

void SecureInterpreter::invalidateSession(const std::string& session_id) {
    if (security_manager_) {
        security_manager_->invalidateSession(session_id);
        if (current_session_id_ == session_id) {
            clearSecurityContext();
        }
        logSecureExecution("SESSION_INVALIDATED", "Session: " + session_id);
        onSessionDestroyed(session_id);
    }
}

SecurityLevel SecureInterpreter::getCurrentSecurityLevel() const {
    return current_context_.security_level;
}

ExecutionMode SecureInterpreter::analyzeExecutionMode(const std::string& code) const {
    if (!enterprise_handler_) {
        return ExecutionMode::STANDARD;
    }

    if (enterprise_handler_->requiresEnterpriseAccess(code)) {
        return ExecutionMode::ENTERPRISE;
    }

    // Check for mixed content
    if (isEnterpriseContent(code) && code.find("document.") != std::string::npos) {
        return ExecutionMode::HYBRID;
    }

    return ExecutionMode::STANDARD;
}

std::vector<std::string> SecureInterpreter::extractSecurityRequirements(const std::string& code) const {
    std::vector<std::string> requirements;

    if (!enterprise_handler_) {
        return requirements;
    }

    return enterprise_handler_->extractEnterpriseElements(code);
}

bool SecureInterpreter::isEnterpriseContent(const std::string& code) const {
    if (!enterprise_handler_) {
        return false;
    }
    return enterprise_handler_->requiresEnterpriseAccess(code);
}

bool SecureInterpreter::registerEnterpriseFunction(const std::string& name,
                                                  std::function<Value(const std::vector<Value>&, const std::string&)> func) {
    if (!enterprise_handler_) {
        return false;
    }

    enterprise_handler_->registerEnterpriseFunction(name, func);
    logSecureExecution("ENTERPRISE_FUNCTION_REGISTERED", "Function: " + name);
    return true;
}

bool SecureInterpreter::setEnterpriseVariable(const std::string& name, const Value& value,
                                            const std::string& access_level) {
    if (!enterprise_handler_ || current_session_id_.empty()) {
        return false;
    }

    bool success = enterprise_handler_->setSecureVariable(name, value, current_session_id_, access_level, false);
    if (success) {
        logSecureExecution("ENTERPRISE_VARIABLE_SET", "Variable: " + name + ", Level: " + access_level);
    }
    return success;
}

Value SecureInterpreter::getEnterpriseVariable(const std::string& name) {
    if (!enterprise_handler_ || current_session_id_.empty()) {
        return Value();
    }

    try {
        Value result = enterprise_handler_->getSecureVariable(name, current_session_id_);
        logSecureExecution("ENTERPRISE_VARIABLE_GET", "Variable: " + name);
        return result;
    } catch (const std::exception& e) {
        logger_->error("Failed to get enterprise variable: " + std::string(e.what()));
        return Value();
    }
}

void SecureInterpreter::setExecutionMode(ExecutionMode mode) {
    current_context_.mode = mode;
    enterprise_mode_active_ = (mode == ExecutionMode::ENTERPRISE || mode == ExecutionMode::HYBRID);
    logSecureExecution("EXECUTION_MODE_SET", "Mode: " + std::to_string(static_cast<int>(mode)));
}

ExecutionMode SecureInterpreter::getExecutionMode() const {
    return current_context_.mode;
}

void SecureInterpreter::setOrganizationId(const std::string& org_id) {
    current_context_.organization_id = org_id;
    if (enterprise_handler_) {
        enterprise_handler_->setCurrentOrganization(org_id);
    }
    logSecureExecution("ORGANIZATION_SET", "Organization: " + org_id);
}

std::string SecureInterpreter::getOrganizationId() const {
    return current_context_.organization_id;
}

std::string SecureInterpreter::getSecurityStatus() const {
    std::stringstream status;
    status << "SecureInterpreter Status:\n";
    status << "- Security Enabled: " << (security_enabled_ ? "Yes" : "No") << "\n";
    status << "- Current Session: " << current_session_id_ << "\n";
    status << "- Security Level: " << static_cast<int>(current_context_.security_level) << "\n";
    status << "- Execution Mode: " << static_cast<int>(current_context_.mode) << "\n";
    status << "- Organization: " << current_context_.organization_id << "\n";
    status << "- Enterprise Mode Active: " << (enterprise_mode_active_ ? "Yes" : "No") << "\n";

    if (security_manager_) {
        status << "- Active Sessions: " << security_manager_->getActiveSessionCount() << "\n";
    }

    if (enterprise_handler_) {
        status << enterprise_handler_->getEnterpriseStatus();
    }

    return status.str();
}

std::vector<SessionInfo> SecureInterpreter::getActiveSessions() const {
    if (!security_manager_) {
        return {};
    }
    return security_manager_->getActiveSessions();
}

bool SecureInterpreter::isUnderSecurityThreat() const {
    if (!security_manager_) {
        return false;
    }
    return security_manager_->isUnderAttack();
}

std::shared_ptr<EnterpriseContentHandler> SecureInterpreter::getEnterpriseHandler() const {
    return enterprise_handler_;
}

std::shared_ptr<SecurityManager> SecureInterpreter::getSecurityManager() const {
    return security_manager_;
}

Interpreter& SecureInterpreter::getBaseInterpreter() const {
    return *base_interpreter_;
}

// Private helper methods

bool SecureInterpreter::validateSecurityContext(const std::string& code) const {
    if (!security_enabled_) {
        return true; // Allow standard execution without security
    }

    SecurityLevel required_level = determineRequiredSecurityLevel(code);
    return current_context_.security_level >= required_level;
}

bool SecureInterpreter::requiresEnterpriseAccess(const std::string& code) const {
    return enterprise_handler_ && enterprise_handler_->requiresEnterpriseAccess(code);
}

SecurityLevel SecureInterpreter::determineRequiredSecurityLevel(const std::string& code) const {
    if (requiresEnterpriseAccess(code)) {
        return SecurityLevel::ENTERPRISE;
    }
    return SecurityLevel::STANDARD;
}

std::string SecureInterpreter::preprocessEnterpriseCode(const std::string& code) const {
    if (!enterprise_handler_ || current_session_id_.empty()) {
        return code;
    }

    EnterpriseContext context;
    context.session_id = current_session_id_;
    context.organization_id = current_context_.organization_id;
    context.enable_advanced_features = (current_context_.security_level == SecurityLevel::ENTERPRISE);

    return enterprise_handler_->preprocessEnterpriseCode(code, context);
}

bool SecureInterpreter::canExecuteCode(const std::string& code) const {
    // Basic validation
    if (code.empty()) {
        return false;
    }

    // Size limits based on security level
    size_t max_size = (current_context_.security_level == SecurityLevel::ENTERPRISE) ?
                      MAX_CODE_SIZE_ENTERPRISE : MAX_CODE_SIZE_STANDARD;

    if (code.size() > max_size) {
        return false;
    }

    // Security context validation
    return validateSecurityContext(code);
}

bool SecureInterpreter::canAccessVariable(const std::string& var_name) const {
    // Check if it's an enterprise variable
    if (enterprise_handler_ && enterprise_handler_->canAccessSecureVariable(var_name, current_session_id_)) {
        return true;
    }

    // Standard variables are always accessible
    return true;
}

bool SecureInterpreter::canCallFunction(const std::string& func_name) const {
    // Check if it's an enterprise function
    if (enterprise_handler_ && enterprise_handler_->hasEnterpriseFunction(func_name)) {
        return current_context_.security_level == SecurityLevel::ENTERPRISE;
    }

    // Standard functions are always accessible
    return true;
}

void SecureInterpreter::logSecureExecution(const std::string& operation, const std::string& details) const {
    try {
        std::string log_message = "SECURE_" + operation + ": " + details;
        if (!current_session_id_.empty()) {
            log_message += " [Session: " + current_session_id_ + "]";
        }

        logger_->info(log_message, "SecureInterpreter", "audit");

    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Failed to log secure execution: " + std::string(e.what()));
        }
    }
}

void SecureInterpreter::auditCodeExecution(const std::string& code, const SecureExecutionContext& context) const {
    std::string audit_details = "Code length: " + std::to_string(code.length()) +
                               ", Security level: " + std::to_string(static_cast<int>(context.security_level)) +
                               ", Mode: " + std::to_string(static_cast<int>(context.mode));

    logSecureExecution("CODE_EXECUTION_AUDIT", audit_details);
}

void SecureInterpreter::updateSecurityContext(const SecureExecutionContext& context) {
    current_context_ = context;
    enterprise_mode_active_ = (context.mode == ExecutionMode::ENTERPRISE || context.mode == ExecutionMode::HYBRID);
}

void SecureInterpreter::clearSecurityContext() {
    current_context_ = SecureExecutionContext();
    current_session_id_.clear();
    enterprise_mode_active_ = false;
}

bool SecureInterpreter::validateExecutionContext() const {
    return security_enabled_ && !current_session_id_.empty() &&
           current_context_.security_level != SecurityLevel::INVALID;
}

// Protected virtual methods for derived classes

void SecureInterpreter::onSecurityInitialized() {
    logSecureExecution("SECURITY_INITIALIZED_EVENT", "Security system ready");
}

void SecureInterpreter::onSessionCreated(const std::string& session_id) {
    logSecureExecution("SESSION_CREATED_EVENT", "Session: " + session_id);
}

void SecureInterpreter::onSessionDestroyed(const std::string& session_id) {
    logSecureExecution("SESSION_DESTROYED_EVENT", "Session: " + session_id);
}

void SecureInterpreter::onEnterpriseAccessGranted(const std::string& session_id) {
    logSecureExecution("ENTERPRISE_ACCESS_GRANTED_EVENT", "Session: " + session_id);
}

void SecureInterpreter::onSecurityViolation(const std::string& violation_type, const std::string& details) {
    logSecureExecution("SECURITY_VIOLATION", "Type: " + violation_type + ", Details: " + details);

    // Could implement additional security measures here:
    // - Rate limiting
    // - IP blocking
    // - Alert notifications
    // - Session termination
}

} // namespace security
