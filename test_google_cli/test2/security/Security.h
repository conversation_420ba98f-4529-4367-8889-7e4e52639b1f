#ifndef SECURITY_SECURITY_H
#define SECURITY_SECURITY_H

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <chrono>
#include <functional>
#include <mutex>
#include <random>
#include <regex>
#include "../utilities/Logger.h"
#include "../core/Application.h"

namespace security {

/**
 * @brief Cryptographic utilities
 */
class CryptoUtils {
private:
    static std::random_device rd_;
    static std::mt19937 gen_;
    
public:
    // Hash functions
    static std::string sha256(const std::string& input);
    static std::string sha512(const std::string& input);
    static std::string md5(const std::string& input);
    
    // Password hashing (using bcrypt-like algorithm)
    static std::string hashPassword(const std::string& password, int rounds = 12);
    static bool verifyPassword(const std::string& password, const std::string& hash);
    
    // Random generation
    static std::string generateRandomString(size_t length, const std::string& charset = "");
    static std::string generateSecureToken(size_t length = 32);
    static std::string generateSalt(size_t length = 16);
    static std::vector<uint8_t> generateRandomBytes(size_t length);
    
    // Base64 encoding/decoding
    static std::string base64Encode(const std::vector<uint8_t>& data);
    static std::string base64Encode(const std::string& data);
    static std::vector<uint8_t> base64Decode(const std::string& encoded);
    
    // URL encoding/decoding
    static std::string urlEncode(const std::string& input);
    static std::string urlDecode(const std::string& input);
    
    // Hex encoding/decoding
    static std::string hexEncode(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> hexDecode(const std::string& hex);
    
    // Timing-safe comparison
    static bool constantTimeCompare(const std::string& a, const std::string& b);
    static bool constantTimeCompare(const std::vector<uint8_t>& a, const std::vector<uint8_t>& b);
};

/**
 * @brief JWT (JSON Web Token) implementation
 */
class JWT {
public:
    struct Header {
        std::string algorithm = "HS256";
        std::string type = "JWT";
        std::unordered_map<std::string, std::string> extra_fields;
    };
    
    struct Payload {
        std::string issuer;
        std::string subject;
        std::string audience;
        std::chrono::system_clock::time_point expiration;
        std::chrono::system_clock::time_point not_before;
        std::chrono::system_clock::time_point issued_at;
        std::string jwt_id;
        std::unordered_map<std::string, std::string> custom_claims;
    };
    
private:
    Header header_;
    Payload payload_;
    std::string secret_;
    
    std::string encodeHeader() const;
    std::string encodePayload() const;
    std::string generateSignature(const std::string& header, const std::string& payload) const;
    
public:
    explicit JWT(const std::string& secret);
    
    // Token creation
    JWT& setIssuer(const std::string& issuer);
    JWT& setSubject(const std::string& subject);
    JWT& setAudience(const std::string& audience);
    JWT& setExpiration(std::chrono::system_clock::time_point exp);
    JWT& setExpirationFromNow(std::chrono::seconds duration);
    JWT& setNotBefore(std::chrono::system_clock::time_point nbf);
    JWT& setIssuedAt(std::chrono::system_clock::time_point iat);
    JWT& setJwtId(const std::string& jti);
    JWT& setClaim(const std::string& key, const std::string& value);
    
    std::string encode() const;
    
    // Token verification
    static bool verify(const std::string& token, const std::string& secret);
    static std::optional<Payload> decode(const std::string& token, const std::string& secret);
    
    // Utility methods
    static bool isExpired(const Payload& payload);
    static bool isValidTime(const Payload& payload);
};

/**
 * @brief User authentication and session management
 */
class User {
public:
    std::string id;
    std::string username;
    std::string email;
    std::string password_hash;
    std::unordered_set<std::string> roles;
    std::unordered_set<std::string> permissions;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point last_login;
    bool is_active = true;
    bool is_verified = false;
    std::unordered_map<std::string, std::string> metadata;
    
    bool hasRole(const std::string& role) const;
    bool hasPermission(const std::string& permission) const;
    bool hasAnyRole(const std::vector<std::string>& roles) const;
    bool hasAllRoles(const std::vector<std::string>& roles) const;
    bool hasAnyPermission(const std::vector<std::string>& permissions) const;
    bool hasAllPermissions(const std::vector<std::string>& permissions) const;
    
    std::string toJson() const;
    static User fromJson(const std::string& json);
};

/**
 * @brief Session management
 */
class Session {
public:
    std::string session_id;
    std::string user_id;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point expires_at;
    std::chrono::system_clock::time_point last_accessed;
    std::string ip_address;
    std::string user_agent;
    std::unordered_map<std::string, std::string> data;
    bool is_active = true;
    
    bool isExpired() const;
    bool isValid() const;
    void updateLastAccessed();
    void extend(std::chrono::seconds duration);
    
    std::string toJson() const;
    static Session fromJson(const std::string& json);
};

/**
 * @brief Authentication service
 */
class AuthenticationService : public core::BaseService {
private:
    std::string jwt_secret_;
    std::chrono::seconds token_expiration_;
    std::chrono::seconds session_expiration_;
    
    // User storage (in a real implementation, this would be a database)
    std::unordered_map<std::string, User> users_;
    std::unordered_map<std::string, std::string> username_to_id_;
    std::unordered_map<std::string, std::string> email_to_id_;
    
    // Session storage
    std::unordered_map<std::string, Session> sessions_;
    std::unordered_map<std::string, std::unordered_set<std::string>> user_sessions_;
    
    mutable std::mutex users_mutex_;
    mutable std::mutex sessions_mutex_;
    
    std::shared_ptr<utilities::Logger> logger_;
    
    void cleanupExpiredSessions();
    std::string generateUserId();
    std::string generateSessionId();
    
protected:
    void doInitialize() override;
    void doShutdown() override;
    
public:
    explicit AuthenticationService(const std::string& jwt_secret = "",
                                  std::chrono::seconds token_expiration = std::chrono::seconds(3600),
                                  std::chrono::seconds session_expiration = std::chrono::seconds(86400));
    
    // User management
    std::string registerUser(const std::string& username, const std::string& email, const std::string& password);
    bool authenticateUser(const std::string& username, const std::string& password);
    std::optional<User> getUser(const std::string& user_id);
    std::optional<User> getUserByUsername(const std::string& username);
    std::optional<User> getUserByEmail(const std::string& email);
    bool updateUser(const User& user);
    bool deleteUser(const std::string& user_id);
    
    // Password management
    bool changePassword(const std::string& user_id, const std::string& old_password, const std::string& new_password);
    std::string generatePasswordResetToken(const std::string& user_id);
    bool resetPassword(const std::string& token, const std::string& new_password);
    
    // Session management
    std::string createSession(const std::string& user_id, const std::string& ip_address = "", const std::string& user_agent = "");
    std::optional<Session> getSession(const std::string& session_id);
    bool validateSession(const std::string& session_id);
    void destroySession(const std::string& session_id);
    void destroyAllUserSessions(const std::string& user_id);
    
    // JWT token management
    std::string generateToken(const std::string& user_id);
    std::optional<std::string> validateToken(const std::string& token);
    
    // Role and permission management
    bool addUserRole(const std::string& user_id, const std::string& role);
    bool removeUserRole(const std::string& user_id, const std::string& role);
    bool addUserPermission(const std::string& user_id, const std::string& permission);
    bool removeUserPermission(const std::string& user_id, const std::string& permission);
    
    // Configuration
    void setJwtSecret(const std::string& secret);
    void setTokenExpiration(std::chrono::seconds expiration);
    void setSessionExpiration(std::chrono::seconds expiration);
};

/**
 * @brief Input validation utilities
 */
class InputValidator {
public:
    // Email validation
    static bool isValidEmail(const std::string& email);
    
    // Password strength validation
    struct PasswordPolicy {
        size_t min_length = 8;
        size_t max_length = 128;
        bool require_uppercase = true;
        bool require_lowercase = true;
        bool require_digits = true;
        bool require_special_chars = true;
        std::string allowed_special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
        std::vector<std::string> forbidden_passwords;
    };
    
    static bool isValidPassword(const std::string& password, const PasswordPolicy& policy = PasswordPolicy{});
    static std::vector<std::string> getPasswordErrors(const std::string& password, const PasswordPolicy& policy = PasswordPolicy{});
    
    // Username validation
    static bool isValidUsername(const std::string& username, size_t min_length = 3, size_t max_length = 32);
    
    // SQL injection prevention
    static std::string sanitizeSqlInput(const std::string& input);
    static bool containsSqlInjection(const std::string& input);
    
    // XSS prevention
    static std::string sanitizeHtml(const std::string& input);
    static std::string escapeHtml(const std::string& input);
    
    // Path traversal prevention
    static bool isValidPath(const std::string& path);
    static std::string sanitizePath(const std::string& path);
    
    // General input sanitization
    static std::string sanitizeInput(const std::string& input, const std::string& allowed_chars = "");
    static bool isAlphanumeric(const std::string& input);
    static bool isNumeric(const std::string& input);
    static bool matchesPattern(const std::string& input, const std::string& pattern);
};

/**
 * @brief CSRF (Cross-Site Request Forgery) protection
 */
class CSRFProtection {
private:
    std::string secret_;
    std::chrono::seconds token_expiration_;
    std::unordered_map<std::string, std::chrono::system_clock::time_point> tokens_;
    mutable std::mutex tokens_mutex_;
    
    void cleanupExpiredTokens();
    
public:
    explicit CSRFProtection(const std::string& secret = "", std::chrono::seconds expiration = std::chrono::seconds(3600));
    
    std::string generateToken(const std::string& session_id = "");
    bool validateToken(const std::string& token, const std::string& session_id = "");
    void invalidateToken(const std::string& token);
    void clearExpiredTokens();
};

/**
 * @brief Rate limiting for API endpoints
 */
class RateLimiter {
private:
    struct ClientInfo {
        size_t request_count;
        std::chrono::steady_clock::time_point window_start;
        std::chrono::steady_clock::time_point last_request;
    };
    
    std::unordered_map<std::string, ClientInfo> clients_;
    mutable std::mutex clients_mutex_;
    
    size_t max_requests_;
    std::chrono::seconds window_duration_;
    std::chrono::seconds cleanup_interval_;
    std::chrono::steady_clock::time_point last_cleanup_;
    
    void cleanupExpiredClients();
    
public:
    explicit RateLimiter(size_t max_requests = 100, std::chrono::seconds window = std::chrono::seconds(60));
    
    bool isAllowed(const std::string& client_id);
    size_t getRemainingRequests(const std::string& client_id);
    std::chrono::seconds getResetTime(const std::string& client_id);
    void reset(const std::string& client_id);
    void clearAll();
    
    // Configuration
    void setMaxRequests(size_t max_requests);
    void setWindowDuration(std::chrono::seconds duration);
};

/**
 * @brief Security service that integrates all security components
 */
class SecurityService : public core::BaseService {
private:
    std::unique_ptr<AuthenticationService> auth_service_;
    std::unique_ptr<CSRFProtection> csrf_protection_;
    std::unique_ptr<RateLimiter> rate_limiter_;
    
    std::shared_ptr<utilities::Logger> logger_;
    
protected:
    void doInitialize() override;
    void doShutdown() override;
    
public:
    SecurityService();
    
    // Component access
    AuthenticationService& getAuthService() { return *auth_service_; }
    CSRFProtection& getCSRFProtection() { return *csrf_protection_; }
    RateLimiter& getRateLimiter() { return *rate_limiter_; }
    
    // Convenience methods
    bool authenticateRequest(const std::string& token_or_session);
    bool authorizeRequest(const std::string& user_id, const std::string& permission);
    bool validateCSRFToken(const std::string& token, const std::string& session_id = "");
    bool checkRateLimit(const std::string& client_id);
    
    // Security headers
    std::unordered_map<std::string, std::string> getSecurityHeaders();
    
    // Audit logging
    void logSecurityEvent(const std::string& event_type, const std::string& user_id, const std::string& details);
};

} // namespace security

#endif // SECURITY_SECURITY_H
