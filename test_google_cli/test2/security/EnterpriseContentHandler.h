#ifndef ENTERPRISE_CONTENT_HANDLER_H
#define ENTERPRISE_CONTENT_HANDLER_H

#include <string>
#include <map>
#include <vector>
#include <functional>
#include <memory>
#include <unordered_set>
#include "../js_engine/Interpreter.h"
#include "../utilities/Logger.h"
#include "../utilities/ErrorHandler.h"
#include "SecurityManager.h"

namespace security {

/**
 * @brief Enterprise content types
 */
enum class EnterpriseContentType {
    STANDARD,           // Regular web content
    ENTERPRISE_DATA,    // Secure enterprise data
    ENTERPRISE_FUNCTION,// Enterprise-specific functions
    SECURE_VARIABLE,    // Protected variables
    ORGANIZATION_CONFIG // Organization-specific configuration
};

/**
 * @brief Enterprise function signature
 */
using EnterpriseFunction = std::function<Value(const std::vector<Value>&, const std::string& session_id)>;

/**
 * @brief Enterprise variable with access control
 */
struct SecureVariable {
    Value value;
    std::string access_level;
    std::chrono::system_clock::time_point created;
    std::chrono::system_clock::time_point last_accessed;
    std::string owner_session;
    bool read_only;
    
    SecureVariable(const Value& v = Value(), const std::string& level = "enterprise",
                  const std::string& owner = "", bool readonly = false)
        : value(v), access_level(level), owner_session(owner), read_only(readonly) {
        created = std::chrono::system_clock::now();
        last_accessed = created;
    }
};

/**
 * @brief Enterprise parsing context
 */
struct EnterpriseContext {
    std::string session_id;
    SecurityLevel security_level;
    std::string organization_id;
    std::map<std::string, std::string> custom_properties;
    bool enable_advanced_features;
    
    EnterpriseContext(const std::string& sid = "", SecurityLevel level = SecurityLevel::STANDARD,
                     const std::string& org = "")
        : session_id(sid), security_level(level), organization_id(org), enable_advanced_features(false) {}
};

/**
 * @brief Handles enterprise-specific content parsing and interpretation
 * 
 * This class provides specialized parsing and interpretation logic for enterprise content
 * that only activates when valid security credentials are present.
 */
class EnterpriseContentHandler {
private:
    // Enterprise functions registry
    std::map<std::string, EnterpriseFunction> enterprise_functions_;
    
    // Secure variables storage
    std::map<std::string, SecureVariable> secure_variables_;
    
    // Organization-specific configurations
    std::map<std::string, std::map<std::string, Value>> organization_configs_;
    
    // Access control
    std::shared_ptr<SecurityManager> security_manager_;
    
    // Logging and error handling
    std::shared_ptr<utilities::Logger> logger_;
    std::shared_ptr<utilities::ErrorHandler> error_handler_;
    
    // Enterprise content detection patterns
    std::unordered_set<std::string> enterprise_keywords_;
    std::unordered_set<std::string> secure_function_names_;
    
    // Thread safety
    mutable std::mutex handler_mutex_;
    
    // Internal state
    bool initialized_;
    std::string current_organization_;
    
    // Helper methods
    bool isEnterpriseContent(const std::string& code) const;
    bool hasEnterpriseKeywords(const std::string& code) const;
    bool canExecuteEnterpriseFunction(const std::string& func_name, const std::string& session_id) const;
    void logEnterpriseAccess(const std::string& operation, const std::string& details,
                           const std::string& session_id) const;
    
public:
    /**
     * @brief Constructor
     * @param security_manager Shared security manager instance
     */
    explicit EnterpriseContentHandler(std::shared_ptr<SecurityManager> security_manager);
    
    /**
     * @brief Destructor
     */
    ~EnterpriseContentHandler();
    
    // Initialization
    bool initialize(const std::string& organization_id);
    bool isInitialized() const;
    void reset();
    
    // Content analysis
    EnterpriseContentType analyzeContent(const std::string& code) const;
    bool requiresEnterpriseAccess(const std::string& code) const;
    std::vector<std::string> extractEnterpriseElements(const std::string& code) const;
    
    // Enterprise parsing
    Value parseEnterpriseExpression(const std::string& expression, const EnterpriseContext& context);
    bool canParseExpression(const std::string& expression, const std::string& session_id) const;
    std::string preprocessEnterpriseCode(const std::string& code, const EnterpriseContext& context);
    
    // Enterprise function management
    void registerEnterpriseFunction(const std::string& name, EnterpriseFunction func);
    void unregisterEnterpriseFunction(const std::string& name);
    bool hasEnterpriseFunction(const std::string& name) const;
    Value callEnterpriseFunction(const std::string& name, const std::vector<Value>& args, 
                               const std::string& session_id);
    std::vector<std::string> getAvailableEnterpriseFunctions(const std::string& session_id) const;
    
    // Secure variable management
    bool setSecureVariable(const std::string& name, const Value& value, const std::string& session_id,
                          const std::string& access_level = "enterprise", bool read_only = false);
    Value getSecureVariable(const std::string& name, const std::string& session_id);
    bool hasSecureVariable(const std::string& name) const;
    void removeSecureVariable(const std::string& name, const std::string& session_id);
    std::vector<std::string> getAccessibleSecureVariables(const std::string& session_id) const;
    
    // Organization configuration
    void setOrganizationConfig(const std::string& org_id, const std::string& key, const Value& value);
    Value getOrganizationConfig(const std::string& org_id, const std::string& key) const;
    bool hasOrganizationConfig(const std::string& org_id, const std::string& key) const;
    std::map<std::string, Value> getAllOrganizationConfig(const std::string& org_id) const;
    
    // Access control
    bool canAccessContent(const std::string& content_type, const std::string& session_id) const;
    SecurityLevel getRequiredSecurityLevel(const std::string& content_type) const;
    
    // Enterprise-specific built-in functions
    void registerBuiltinEnterpriseFunctions();
    
    // Utility methods
    std::string getEnterpriseStatus() const;
    std::vector<std::string> getEnterpriseKeywords() const;
    void addEnterpriseKeyword(const std::string& keyword);
    void removeEnterpriseKeyword(const std::string& keyword);
    
    // Organization management
    void setCurrentOrganization(const std::string& org_id);
    std::string getCurrentOrganization() const;

    // Security access validation
    bool canAccessSecureVariable(const std::string& var_name, const std::string& session_id) const;
    
private:
    // Built-in enterprise functions
    Value enterpriseFunction_getSecureData(const std::vector<Value>& args, const std::string& session_id);
    Value enterpriseFunction_setSecureData(const std::vector<Value>& args, const std::string& session_id);
    Value enterpriseFunction_getOrgConfig(const std::vector<Value>& args, const std::string& session_id);
    Value enterpriseFunction_getCurrentUser(const std::vector<Value>& args, const std::string& session_id);
    Value enterpriseFunction_logSecureEvent(const std::vector<Value>& args, const std::string& session_id);
    Value enterpriseFunction_encryptData(const std::vector<Value>& args, const std::string& session_id);
    Value enterpriseFunction_decryptData(const std::vector<Value>& args, const std::string& session_id);
    Value enterpriseFunction_validateAccess(const std::vector<Value>& args, const std::string& session_id);
    
    // Default enterprise keywords
    void initializeEnterpriseKeywords();
    
    // Security validation helpers
    bool validateEnterpriseAccess(const std::string& operation, const std::string& session_id) const;
    void auditEnterpriseOperation(const std::string& operation, const std::string& details,
                                const std::string& session_id) const;
};

} // namespace security

#endif // ENTERPRISE_CONTENT_HANDLER_H
