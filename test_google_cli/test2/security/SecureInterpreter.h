#ifndef SECURE_INTERPRETER_H
#define SECURE_INTERPRETER_H

#include "../js_engine/Interpreter.h"
#include "SecurityManager.h"
#include "EnterpriseContentHandler.h"
#include <memory>
#include <string>
#include <functional>

namespace security {

/**
 * @brief Execution mode for the secure interpreter
 */
enum class ExecutionMode {
    STANDARD,           // Standard web content interpretation
    ENTERPRISE,         // Enterprise mode with security features
    HYBRID             // Both modes available based on content analysis
};

/**
 * @brief Execution context for secure interpretation
 */
struct SecureExecutionContext {
    std::string session_id;
    SecurityLevel security_level;
    ExecutionMode mode;
    std::string organization_id;
    bool enable_real_dom;
    std::map<std::string, std::string> custom_headers;
    
    SecureExecutionContext()
        : security_level(SecurityLevel::STANDARD), 
          mode(ExecutionMode::STANDARD), 
          enable_real_dom(false) {}
};

/**
 * @brief Result of secure interpretation
 */
struct SecureInterpretationResult {
    std::string output;
    std::vector<std::string> dom_operations;
    SecurityLevel security_level;
    ExecutionMode execution_mode;
    bool success;
    std::string error_message;
    std::string session_id;
    
    SecureInterpretationResult()
        : security_level(SecurityLevel::INVALID),
          execution_mode(ExecutionMode::STANDARD),
          success(false) {}
};

/**
 * @brief Secure JavaScript interpreter with enterprise features
 *
 * Uses composition with the base Interpreter class to add multi-layer security,
 * enterprise content handling, and dual functionality (standard + enterprise modes).
 */
class SecureInterpreter {
private:
    // Core interpreter instance
    std::unique_ptr<Interpreter> base_interpreter_;

    // Security components
    std::shared_ptr<SecurityManager> security_manager_;
    std::shared_ptr<EnterpriseContentHandler> enterprise_handler_;

    // Current execution context
    SecureExecutionContext current_context_;

    // Security state
    bool security_enabled_;
    std::string current_session_id_;

    // Logging and error handling
    std::shared_ptr<utilities::Logger> logger_;
    std::shared_ptr<utilities::ErrorHandler> error_handler_;

    // Enterprise parsing state
    bool enterprise_mode_active_;
    std::map<std::string, Value> enterprise_variables_;

    // Security validation helpers
    bool validateSecurityContext(const std::string& code) const;
    bool requiresEnterpriseAccess(const std::string& code) const;
    SecurityLevel determineRequiredSecurityLevel(const std::string& code) const;

    // Enterprise code processing
    std::string preprocessEnterpriseCode(const std::string& code) const;
    bool isEnterpriseKeyword(const std::string& keyword) const;

    // Access control
    bool canExecuteCode(const std::string& code) const;
    bool canAccessVariable(const std::string& var_name) const;
    bool canCallFunction(const std::string& func_name) const;

    // Audit and logging
    void logSecureExecution(const std::string& operation, const std::string& details) const;
    void auditCodeExecution(const std::string& code, const SecureExecutionContext& context) const;
    
public:
    /**
     * @brief Constructor
     * @param security_config Security configuration
     */
    explicit SecureInterpreter(const SecurityConfig& security_config = SecurityConfig{});
    
    /**
     * @brief Destructor
     */
    virtual ~SecureInterpreter();
    
    // Security initialization
    bool initializeSecurity(const std::string& secret_key, const std::string& enterprise_token,
                           const std::string& organization_id = "DEFAULT_ORG");
    bool isSecurityInitialized() const;
    void resetSecurity();
    
    // Authentication and session management
    AuthResult authenticate(const std::string& secret_key, const std::string& enterprise_token,
                          const std::string& client_id = "");
    bool validateSession(const std::string& session_id);
    void invalidateSession(const std::string& session_id);
    SecurityLevel getCurrentSecurityLevel() const;
    
    // Secure interpretation methods
    SecureInterpretationResult interpretSecure(const std::string& code,
                                             const std::string& secret_key,
                                             const std::string& enterprise_token,
                                             std::function<void(const std::string&)> print_callback,
                                             const std::string& client_id = "");
    
    SecureInterpretationResult interpretWithSession(const std::string& code,
                                                   const std::string& session_id,
                                                   std::function<void(const std::string&)> print_callback);
    
    // Standard interpretation (backward compatibility)
    std::string interpret(const std::string& code,
                         std::function<void(const std::string&)> print_callback);

    // Base interpreter access for standard operations
    Interpreter& getBaseInterpreter() const;
    
    // Content analysis
    ExecutionMode analyzeExecutionMode(const std::string& code) const;
    std::vector<std::string> extractSecurityRequirements(const std::string& code) const;
    bool isEnterpriseContent(const std::string& code) const;
    
    // Enterprise features
    bool registerEnterpriseFunction(const std::string& name, 
                                   std::function<Value(const std::vector<Value>&, const std::string&)> func);
    bool setEnterpriseVariable(const std::string& name, const Value& value, const std::string& access_level = "enterprise");
    Value getEnterpriseVariable(const std::string& name);
    
    // Configuration and management
    void setExecutionMode(ExecutionMode mode);
    ExecutionMode getExecutionMode() const;
    void setOrganizationId(const std::string& org_id);
    std::string getOrganizationId() const;
    
    // Security status and monitoring
    std::string getSecurityStatus() const;
    std::vector<SessionInfo> getActiveSessions() const;
    bool isUnderSecurityThreat() const;
    
    // Enterprise content handler access
    std::shared_ptr<EnterpriseContentHandler> getEnterpriseHandler() const;
    std::shared_ptr<SecurityManager> getSecurityManager() const;
    
    // Utility methods
    void enableSecurityLogging(bool enable);
    std::vector<std::string> getSecurityAuditLog() const;
    void clearSecurityAuditLog();
    
protected:
    // Protected methods for derived classes
    virtual void onSecurityInitialized();
    virtual void onSessionCreated(const std::string& session_id);
    virtual void onSessionDestroyed(const std::string& session_id);
    virtual void onEnterpriseAccessGranted(const std::string& session_id);
    virtual void onSecurityViolation(const std::string& violation_type, const std::string& details);
    
    // Security-aware DOM operations
    void initializeSecureDOM();
    bool canAccessDOMElement(const std::string& element_id) const;
    void auditDOMOperation(const std::string& operation, const std::string& element_id) const;
    
private:
    // Internal security state management
    void updateSecurityContext(const SecureExecutionContext& context);
    void clearSecurityContext();
    bool validateExecutionContext() const;
    
    // Enterprise parsing helpers
    std::string preprocessCodeForEnterprise(const std::string& code) const;
    Value executeEnterpriseFunction(const std::string& func_name, const std::vector<Value>& args);
    
    // Security constants
    static const size_t MAX_CODE_SIZE_STANDARD = 1000000;    // 1MB for standard mode
    static const size_t MAX_CODE_SIZE_ENTERPRISE = 5000000;  // 5MB for enterprise mode
    static const size_t MAX_EXECUTION_TIME_MS = 30000;       // 30 seconds max execution
};

} // namespace security

#endif // SECURE_INTERPRETER_H
