#ifndef SECURITY_MANAGER_H
#define SECURITY_MANAGER_H

#include <string>
#include <chrono>
#include <memory>
#include <unordered_set>
#include <mutex>
#include "../utilities/Logger.h"
#include "../utilities/ErrorHandler.h"

namespace security {

/**
 * @brief Security levels for the interpreter
 */
enum class SecurityLevel {
    STANDARD,      // Normal web content only
    ENTERPRISE,    // Full enterprise access
    EXPIRED,       // Session expired, enterprise access revoked
    INVALID        // Invalid credentials provided
};

/**
 * @brief Authentication result structure
 */
struct AuthResult {
    bool success;
    SecurityLevel level;
    std::string message;
    std::chrono::system_clock::time_point expiry;
    
    AuthResult(bool s = false, SecurityLevel l = SecurityLevel::INVALID, 
               const std::string& m = "", 
               std::chrono::system_clock::time_point e = std::chrono::system_clock::now())
        : success(s), level(l), message(m), expiry(e) {}
};

/**
 * @brief Session information
 */
struct SessionInfo {
    std::string session_id;
    SecurityLevel level;
    std::chrono::system_clock::time_point created;
    std::chrono::system_clock::time_point last_access;
    std::chrono::system_clock::time_point expiry;
    std::string client_identifier;
    size_t access_count;
    
    bool isValid() const {
        return std::chrono::system_clock::now() < expiry && level != SecurityLevel::INVALID;
    }
    
    bool isExpired() const {
        return std::chrono::system_clock::now() >= expiry;
    }
};

/**
 * @brief Security configuration
 */
struct SecurityConfig {
    std::chrono::minutes session_timeout{30};
    size_t max_failed_attempts{5};
    std::chrono::minutes lockout_duration{15};
    bool enable_audit_logging{true};
    bool require_both_credentials{true};
    size_t max_concurrent_sessions{3};
    
    // Enterprise-specific settings
    std::string organization_id{"DEFAULT_ORG"};
    bool enable_enterprise_features{true};
    std::chrono::minutes enterprise_session_timeout{60};
};

/**
 * @brief Multi-layer security manager for enterprise JavaScript interpreter
 * 
 * Handles dual authentication (secret key + enterprise token), session management,
 * and access control for protected enterprise content.
 */
class SecurityManager {
private:
    // Core security state
    std::string secret_key_hash_;
    std::string enterprise_token_hash_;
    SecurityConfig config_;
    
    // Session management
    std::unordered_map<std::string, SessionInfo> active_sessions_;
    std::unordered_map<std::string, size_t> failed_attempts_;
    std::unordered_map<std::string, std::chrono::system_clock::time_point> lockout_times_;
    
    // Thread safety
    mutable std::mutex security_mutex_;
    
    // Logging and error handling
    std::shared_ptr<utilities::Logger> logger_;
    std::shared_ptr<utilities::ErrorHandler> error_handler_;
    
    // Security utilities
    std::string generateSessionId() const;
    std::string hashCredential(const std::string& credential, const std::string& salt = "") const;
    std::string generateSalt() const;
    bool isLockedOut(const std::string& client_id) const;
    void recordFailedAttempt(const std::string& client_id);
    void clearFailedAttempts(const std::string& client_id);
    void cleanupExpiredSessions();
    void logSecurityEvent(const std::string& event, const std::string& details, 
                         const std::string& client_id = "") const;
    
    // Validation helpers
    bool validateSecretKey(const std::string& provided_key) const;
    bool validateEnterpriseToken(const std::string& provided_token) const;
    bool isValidCredentialFormat(const std::string& credential) const;
    
public:
    /**
     * @brief Constructor
     * @param config Security configuration
     */
    explicit SecurityManager(const SecurityConfig& config = SecurityConfig{});
    
    /**
     * @brief Destructor - ensures secure cleanup
     */
    ~SecurityManager();
    
    // Initialization and configuration
    bool initialize(const std::string& secret_key, const std::string& enterprise_token);
    void updateConfiguration(const SecurityConfig& config);
    SecurityConfig getConfiguration() const;
    
    // Authentication methods
    AuthResult authenticate(const std::string& secret_key, 
                           const std::string& enterprise_token,
                           const std::string& client_id = "");
    
    bool validateSession(const std::string& session_id);
    void invalidateSession(const std::string& session_id);
    void invalidateAllSessions();
    
    // Access control
    bool hasEnterpriseAccess(const std::string& session_id) const;
    bool canAccessContent(const std::string& session_id, const std::string& content_type) const;
    SecurityLevel getSecurityLevel(const std::string& session_id) const;
    
    // Session management
    SessionInfo getSessionInfo(const std::string& session_id) const;
    std::vector<SessionInfo> getActiveSessions() const;
    size_t getActiveSessionCount() const;
    void extendSession(const std::string& session_id);
    
    // Security monitoring
    bool isUnderAttack() const;
    size_t getFailedAttemptCount(const std::string& client_id) const;
    std::vector<std::string> getLockedOutClients() const;
    void unlockClient(const std::string& client_id);
    
    // Utility methods
    bool isInitialized() const;
    void reset();
    std::string getSecurityStatus() const;
    
    // Enterprise-specific methods
    bool isEnterpriseMode() const;
    std::string getOrganizationId() const;
    void setOrganizationId(const std::string& org_id);
    
    // Audit and logging
    void enableAuditLogging(bool enable);
    std::vector<std::string> getAuditLog() const;
    void clearAuditLog();
    
private:
    // Internal state management
    bool initialized_;
    std::string organization_salt_;
    mutable std::vector<std::string> audit_log_;
    
    // Security constants
    static const size_t MIN_KEY_LENGTH = 8;
    static const size_t MIN_TOKEN_LENGTH = 16;
    static const size_t MAX_CREDENTIAL_LENGTH = 256;
    static const size_t MAX_AUDIT_LOG_SIZE = 1000;
    
    // Secure memory management
    void secureMemoryClear(std::string& sensitive_data);
    void secureMemoryClear(char* data, size_t length);
};

} // namespace security

#endif // SECURITY_MANAGER_H
