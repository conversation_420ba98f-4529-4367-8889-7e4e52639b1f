#include "EnterpriseContentHandler.h"
#include <algorithm>
#include <sstream>
#include <regex>

namespace security {

EnterpriseContentHandler::EnterpriseContentHandler(std::shared_ptr<SecurityManager> security_manager)
    : security_manager_(security_manager), initialized_(false) {
    
    try {
        // Initialize logging and error handling
        logger_ = utilities::Logger::getLogger("EnterpriseContentHandler");
        error_handler_ = std::make_shared<utilities::ErrorHandler>(logger_);
        
        // Initialize enterprise keywords
        initializeEnterpriseKeywords();
        
        logger_->info("EnterpriseContentHandler created", "EnterpriseContentHandler", "constructor");
        
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Failed to create EnterpriseContentHandler: " + std::string(e.what()));
        }
        initialized_ = false;
    }
}

EnterpriseContentHandler::~EnterpriseContentHandler() {
    try {
        logEnterpriseAccess("HANDLER_DESTROYED", "Enterprise content handler destroyed", "");
        
        // Clear sensitive data
        secure_variables_.clear();
        enterprise_functions_.clear();
        organization_configs_.clear();
        
        if (logger_) {
            logger_->info("EnterpriseContentHandler destroyed", "EnterpriseContentHandler", "destructor");
        }
    } catch (...) {
        // Suppress exceptions in destructor
    }
}

bool EnterpriseContentHandler::initialize(const std::string& organization_id) {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    
    try {
        logger_->info("Initializing EnterpriseContentHandler", "EnterpriseContentHandler", "initialize");
        
        if (!security_manager_ || !security_manager_->isInitialized()) {
            logger_->error("SecurityManager not available or not initialized");
            return false;
        }
        
        current_organization_ = organization_id;
        
        // Register built-in enterprise functions
        registerBuiltinEnterpriseFunctions();
        
        initialized_ = true;
        
        logEnterpriseAccess("HANDLER_INITIALIZED", "Organization: " + organization_id, "");
        logger_->info("EnterpriseContentHandler initialized successfully", "EnterpriseContentHandler", "initialize");
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize EnterpriseContentHandler: " + std::string(e.what()));
        initialized_ = false;
        return false;
    }
}

EnterpriseContentType EnterpriseContentHandler::analyzeContent(const std::string& code) const {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    
    try {
        // Check for enterprise keywords
        if (hasEnterpriseKeywords(code)) {
            return EnterpriseContentType::ENTERPRISE_DATA;
        }
        
        // Check for secure function calls
        for (const auto& func_name : secure_function_names_) {
            if (code.find(func_name + "(") != std::string::npos) {
                return EnterpriseContentType::ENTERPRISE_FUNCTION;
            }
        }
        
        // Check for secure variable patterns
        std::regex secure_var_pattern(R"(\$secure\.|\.secure\.|secureVar\.)");
        if (std::regex_search(code, secure_var_pattern)) {
            return EnterpriseContentType::SECURE_VARIABLE;
        }
        
        // Check for organization config patterns
        std::regex org_config_pattern(R"(orgConfig\.|getOrgConfig\(|setOrgConfig\()");
        if (std::regex_search(code, org_config_pattern)) {
            return EnterpriseContentType::ORGANIZATION_CONFIG;
        }
        
        return EnterpriseContentType::STANDARD;
        
    } catch (const std::exception& e) {
        logger_->error("Error analyzing content: " + std::string(e.what()));
        return EnterpriseContentType::STANDARD;
    }
}

bool EnterpriseContentHandler::requiresEnterpriseAccess(const std::string& code) const {
    EnterpriseContentType type = analyzeContent(code);
    return type != EnterpriseContentType::STANDARD;
}

std::vector<std::string> EnterpriseContentHandler::extractEnterpriseElements(const std::string& code) const {
    std::vector<std::string> elements;
    
    try {
        // Extract enterprise function calls
        for (const auto& func_name : secure_function_names_) {
            std::string pattern = func_name + "(";
            size_t pos = 0;
            while ((pos = code.find(pattern, pos)) != std::string::npos) {
                elements.push_back("function:" + func_name);
                pos += pattern.length();
            }
        }
        
        // Extract secure variable references
        std::regex secure_var_regex(R"((\$secure\.[\w]+|\.secure\.[\w]+|secureVar\.[\w]+))");
        std::sregex_iterator iter(code.begin(), code.end(), secure_var_regex);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            elements.push_back("variable:" + iter->str());
        }
        
        // Extract enterprise keywords
        for (const auto& keyword : enterprise_keywords_) {
            if (code.find(keyword) != std::string::npos) {
                elements.push_back("keyword:" + keyword);
            }
        }
        
    } catch (const std::exception& e) {
        logger_->error("Error extracting enterprise elements: " + std::string(e.what()));
    }
    
    return elements;
}

Value EnterpriseContentHandler::parseEnterpriseExpression(const std::string& expression, 
                                                        const EnterpriseContext& context) {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    
    try {
        if (!initialized_) {
            throw std::runtime_error("EnterpriseContentHandler not initialized");
        }
        
        // Validate access
        if (!validateEnterpriseAccess("PARSE_EXPRESSION", context.session_id)) {
            throw std::runtime_error("Access denied for enterprise expression");
        }
        
        logEnterpriseAccess("PARSE_EXPRESSION", "Expression: " + expression, context.session_id);
        
        // Handle different types of enterprise expressions
        if (expression.find("getSecureData(") != std::string::npos) {
            // Extract function call and arguments
            std::regex func_regex(R"(getSecureData\(['"]([^'"]+)['"](?:,\s*['"]([^'"]+)['"])?\))");
            std::smatch match;
            
            if (std::regex_search(expression, match, func_regex)) {
                std::vector<Value> args;
                args.push_back(Value(match[1].str()));
                if (match[2].matched) {
                    args.push_back(Value(match[2].str()));
                }
                return enterpriseFunction_getSecureData(args, context.session_id);
            }
        }
        
        if (expression.find("getOrgConfig(") != std::string::npos) {
            std::regex func_regex(R"(getOrgConfig\(['"]([^'"]+)['"](?:,\s*['"]([^'"]+)['"])?\))");
            std::smatch match;
            
            if (std::regex_search(expression, match, func_regex)) {
                std::vector<Value> args;
                args.push_back(Value(match[1].str()));
                if (match[2].matched) {
                    args.push_back(Value(match[2].str()));
                }
                return enterpriseFunction_getOrgConfig(args, context.session_id);
            }
        }
        
        // Handle secure variable access
        std::regex secure_var_regex(R"(secureVar\.(\w+))");
        std::smatch var_match;
        if (std::regex_search(expression, var_match, secure_var_regex)) {
            return getSecureVariable(var_match[1].str(), context.session_id);
        }
        
        logger_->warning("Unknown enterprise expression: " + expression);
        return Value(); // Return void/null for unknown expressions
        
    } catch (const std::exception& e) {
        logger_->error("Error parsing enterprise expression: " + std::string(e.what()));
        auditEnterpriseOperation("PARSE_ERROR", "Expression: " + expression + ", Error: " + e.what(), 
                                context.session_id);
        throw;
    }
}

bool EnterpriseContentHandler::canParseExpression(const std::string& expression, 
                                                const std::string& session_id) const {
    return validateEnterpriseAccess("PARSE_CHECK", session_id) && 
           requiresEnterpriseAccess(expression);
}

std::string EnterpriseContentHandler::preprocessEnterpriseCode(const std::string& code, 
                                                             const EnterpriseContext& context) {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    
    try {
        if (!validateEnterpriseAccess("PREPROCESS", context.session_id)) {
            return code; // Return original code if no enterprise access
        }
        
        std::string processed_code = code;
        
        // Replace enterprise function calls with secure implementations
        for (const auto& pair : enterprise_functions_) {
            std::string pattern = pair.first + "(";
            std::string replacement = "__enterprise_" + pair.first + "(";
            
            size_t pos = 0;
            while ((pos = processed_code.find(pattern, pos)) != std::string::npos) {
                processed_code.replace(pos, pattern.length(), replacement);
                pos += replacement.length();
            }
        }
        
        // Replace secure variable references
        std::regex secure_var_regex(R"(secureVar\.(\w+))");
        processed_code = std::regex_replace(processed_code, secure_var_regex, "__getSecureVar('$1')");
        
        // Add organization context
        if (context.enable_advanced_features) {
            std::string context_injection = "let __orgContext = '" + context.organization_id + "'; ";
            processed_code = context_injection + processed_code;
        }
        
        logEnterpriseAccess("CODE_PREPROCESSED", "Original length: " + std::to_string(code.length()) + 
                          ", Processed length: " + std::to_string(processed_code.length()), context.session_id);
        
        return processed_code;
        
    } catch (const std::exception& e) {
        logger_->error("Error preprocessing enterprise code: " + std::string(e.what()));
        return code; // Return original code on error
    }
}

void EnterpriseContentHandler::registerEnterpriseFunction(const std::string& name, EnterpriseFunction func) {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    
    enterprise_functions_[name] = func;
    secure_function_names_.insert(name);
    
    logEnterpriseAccess("FUNCTION_REGISTERED", "Function: " + name, "");
    logger_->info("Enterprise function registered: " + name, "EnterpriseContentHandler", "registerEnterpriseFunction");
}

void EnterpriseContentHandler::unregisterEnterpriseFunction(const std::string& name) {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    
    enterprise_functions_.erase(name);
    secure_function_names_.erase(name);
    
    logEnterpriseAccess("FUNCTION_UNREGISTERED", "Function: " + name, "");
    logger_->info("Enterprise function unregistered: " + name, "EnterpriseContentHandler", "unregisterEnterpriseFunction");
}

bool EnterpriseContentHandler::hasEnterpriseFunction(const std::string& name) const {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    return enterprise_functions_.find(name) != enterprise_functions_.end();
}

Value EnterpriseContentHandler::callEnterpriseFunction(const std::string& name, 
                                                     const std::vector<Value>& args, 
                                                     const std::string& session_id) {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    
    try {
        if (!validateEnterpriseAccess("CALL_FUNCTION", session_id)) {
            throw std::runtime_error("Access denied for enterprise function: " + name);
        }
        
        auto it = enterprise_functions_.find(name);
        if (it == enterprise_functions_.end()) {
            throw std::runtime_error("Enterprise function not found: " + name);
        }
        
        logEnterpriseAccess("FUNCTION_CALLED", "Function: " + name + ", Args: " + std::to_string(args.size()), session_id);
        
        Value result = it->second(args, session_id);
        
        auditEnterpriseOperation("FUNCTION_SUCCESS", "Function: " + name, session_id);
        
        return result;
        
    } catch (const std::exception& e) {
        logger_->error("Error calling enterprise function " + name + ": " + std::string(e.what()));
        auditEnterpriseOperation("FUNCTION_ERROR", "Function: " + name + ", Error: " + e.what(), session_id);
        throw;
    }
}

// Private helper methods

bool EnterpriseContentHandler::isEnterpriseContent(const std::string& code) const {
    return analyzeContent(code) != EnterpriseContentType::STANDARD;
}

bool EnterpriseContentHandler::hasEnterpriseKeywords(const std::string& code) const {
    for (const auto& keyword : enterprise_keywords_) {
        if (code.find(keyword) != std::string::npos) {
            return true;
        }
    }
    return false;
}

bool EnterpriseContentHandler::validateEnterpriseAccess(const std::string& operation, 
                                                       const std::string& session_id) const {
    if (!security_manager_) {
        return false;
    }
    
    return security_manager_->hasEnterpriseAccess(session_id);
}

void EnterpriseContentHandler::logEnterpriseAccess(const std::string& operation, 
                                                  const std::string& details,
                                                  const std::string& session_id) const {
    try {
        std::string log_message = "ENTERPRISE_" + operation + ": " + details;
        if (!session_id.empty()) {
            log_message += " [Session: " + session_id + "]";
        }
        
        logger_->info(log_message, "EnterpriseContentHandler", "audit");
        
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Failed to log enterprise access: " + std::string(e.what()));
        }
    }
}

void EnterpriseContentHandler::auditEnterpriseOperation(const std::string& operation, 
                                                       const std::string& details,
                                                       const std::string& session_id) const {
    logEnterpriseAccess(operation, details, session_id);
}

void EnterpriseContentHandler::initializeEnterpriseKeywords() {
    // Add default enterprise keywords
    enterprise_keywords_.insert("enterprise");
    enterprise_keywords_.insert("secure");
    enterprise_keywords_.insert("confidential");
    enterprise_keywords_.insert("restricted");
    enterprise_keywords_.insert("internal");
    enterprise_keywords_.insert("proprietary");
    enterprise_keywords_.insert("classified");
    enterprise_keywords_.insert("orgData");
    enterprise_keywords_.insert("secureVar");
    enterprise_keywords_.insert("enterpriseAPI");
    
    // Add secure function names
    secure_function_names_.insert("getSecureData");
    secure_function_names_.insert("setSecureData");
    secure_function_names_.insert("getOrgConfig");
    secure_function_names_.insert("getCurrentUser");
    secure_function_names_.insert("logSecureEvent");
    secure_function_names_.insert("encryptData");
    secure_function_names_.insert("decryptData");
    secure_function_names_.insert("validateAccess");
}

bool EnterpriseContentHandler::setSecureVariable(const std::string& name, const Value& value,
                                               const std::string& session_id,
                                               const std::string& access_level, bool read_only) {
    std::lock_guard<std::mutex> lock(handler_mutex_);

    try {
        if (!validateEnterpriseAccess("SET_SECURE_VAR", session_id)) {
            return false;
        }

        SecureVariable secure_var(value, access_level, session_id, read_only);
        secure_variables_[name] = secure_var;

        logEnterpriseAccess("SECURE_VAR_SET", "Variable: " + name + ", Level: " + access_level, session_id);
        return true;

    } catch (const std::exception& e) {
        logger_->error("Error setting secure variable: " + std::string(e.what()));
        return false;
    }
}

Value EnterpriseContentHandler::getSecureVariable(const std::string& name, const std::string& session_id) {
    std::lock_guard<std::mutex> lock(handler_mutex_);

    try {
        if (!canAccessSecureVariable(name, session_id)) {
            throw std::runtime_error("Access denied for secure variable: " + name);
        }

        auto it = secure_variables_.find(name);
        if (it == secure_variables_.end()) {
            throw std::runtime_error("Secure variable not found: " + name);
        }

        // Update last accessed time
        it->second.last_accessed = std::chrono::system_clock::now();

        logEnterpriseAccess("SECURE_VAR_GET", "Variable: " + name, session_id);
        return it->second.value;

    } catch (const std::exception& e) {
        logger_->error("Error getting secure variable: " + std::string(e.what()));
        auditEnterpriseOperation("SECURE_VAR_ERROR", "Variable: " + name + ", Error: " + e.what(), session_id);
        throw;
    }
}

bool EnterpriseContentHandler::canAccessSecureVariable(const std::string& var_name,
                                                     const std::string& session_id) const {
    if (!validateEnterpriseAccess("ACCESS_CHECK", session_id)) {
        return false;
    }

    auto it = secure_variables_.find(var_name);
    if (it == secure_variables_.end()) {
        return false;
    }

    // Check if session owns the variable or has appropriate access level
    const SecureVariable& var = it->second;
    return var.owner_session == session_id ||
           security_manager_->hasEnterpriseAccess(session_id);
}

void EnterpriseContentHandler::registerBuiltinEnterpriseFunctions() {
    // Register built-in enterprise functions
    registerEnterpriseFunction("getSecureData",
        [this](const std::vector<Value>& args, const std::string& session_id) {
            return enterpriseFunction_getSecureData(args, session_id);
        });

    registerEnterpriseFunction("setSecureData",
        [this](const std::vector<Value>& args, const std::string& session_id) {
            return enterpriseFunction_setSecureData(args, session_id);
        });

    registerEnterpriseFunction("getOrgConfig",
        [this](const std::vector<Value>& args, const std::string& session_id) {
            return enterpriseFunction_getOrgConfig(args, session_id);
        });

    registerEnterpriseFunction("getCurrentUser",
        [this](const std::vector<Value>& args, const std::string& session_id) {
            return enterpriseFunction_getCurrentUser(args, session_id);
        });

    registerEnterpriseFunction("logSecureEvent",
        [this](const std::vector<Value>& args, const std::string& session_id) {
            return enterpriseFunction_logSecureEvent(args, session_id);
        });

    registerEnterpriseFunction("validateAccess",
        [this](const std::vector<Value>& args, const std::string& session_id) {
            return enterpriseFunction_validateAccess(args, session_id);
        });
}

// Built-in enterprise function implementations

Value EnterpriseContentHandler::enterpriseFunction_getSecureData(const std::vector<Value>& args,
                                                               const std::string& session_id) {
    if (args.empty()) {
        throw std::runtime_error("getSecureData requires at least one argument");
    }

    std::string data_key = args[0].as_string();
    std::string access_level = (args.size() > 1) ? args[1].as_string() : "enterprise";

    // This would typically fetch from a secure database or encrypted storage
    // For demonstration, we'll use secure variables
    try {
        return getSecureVariable(data_key, session_id);
    } catch (...) {
        // Return default secure data structure
        return Value("SECURE_DATA_" + data_key + "_FOR_SESSION_" + session_id);
    }
}

Value EnterpriseContentHandler::enterpriseFunction_setSecureData(const std::vector<Value>& args,
                                                               const std::string& session_id) {
    if (args.size() < 2) {
        throw std::runtime_error("setSecureData requires key and value arguments");
    }

    std::string data_key = args[0].as_string();
    Value data_value = args[1];
    std::string access_level = (args.size() > 2) ? args[2].as_string() : "enterprise";

    bool success = setSecureVariable(data_key, data_value, session_id, access_level, false);
    return Value(success ? 1 : 0);
}

Value EnterpriseContentHandler::enterpriseFunction_getOrgConfig(const std::vector<Value>& args,
                                                              const std::string& session_id) {
    if (args.empty()) {
        throw std::runtime_error("getOrgConfig requires config key argument");
    }

    std::string config_key = args[0].as_string();
    std::string org_id = (args.size() > 1) ? args[1].as_string() : current_organization_;

    return getOrganizationConfig(org_id, config_key);
}

Value EnterpriseContentHandler::enterpriseFunction_getCurrentUser(const std::vector<Value>& args,
                                                                const std::string& session_id) {
    // Return session-based user information
    SessionInfo session = security_manager_->getSessionInfo(session_id);
    return Value("USER_" + session.client_identifier + "_ORG_" + current_organization_);
}

Value EnterpriseContentHandler::enterpriseFunction_logSecureEvent(const std::vector<Value>& args,
                                                                const std::string& session_id) {
    if (args.empty()) {
        throw std::runtime_error("logSecureEvent requires event description");
    }

    std::string event = args[0].as_string();
    std::string details = (args.size() > 1) ? args[1].as_string() : "";

    auditEnterpriseOperation("USER_EVENT", event + ": " + details, session_id);
    return Value(1); // Success
}

Value EnterpriseContentHandler::enterpriseFunction_validateAccess(const std::vector<Value>& args,
                                                                const std::string& session_id) {
    if (args.empty()) {
        throw std::runtime_error("validateAccess requires resource name");
    }

    std::string resource = args[0].as_string();
    std::string operation = (args.size() > 1) ? args[1].as_string() : "read";

    bool has_access = canAccessContent(resource, session_id);
    auditEnterpriseOperation("ACCESS_CHECK", "Resource: " + resource + ", Operation: " + operation +
                           ", Result: " + (has_access ? "GRANTED" : "DENIED"), session_id);

    return Value(has_access ? 1 : 0);
}

void EnterpriseContentHandler::setOrganizationConfig(const std::string& org_id,
                                                    const std::string& key, const Value& value) {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    organization_configs_[org_id][key] = value;
    logEnterpriseAccess("ORG_CONFIG_SET", "Org: " + org_id + ", Key: " + key, "");
}

Value EnterpriseContentHandler::getOrganizationConfig(const std::string& org_id,
                                                     const std::string& key) const {
    std::lock_guard<std::mutex> lock(handler_mutex_);

    auto org_it = organization_configs_.find(org_id);
    if (org_it == organization_configs_.end()) {
        return Value(); // Return void for non-existent org
    }

    auto config_it = org_it->second.find(key);
    if (config_it == org_it->second.end()) {
        return Value(); // Return void for non-existent config
    }

    return config_it->second;
}

bool EnterpriseContentHandler::canAccessContent(const std::string& content_type,
                                              const std::string& session_id) const {
    return security_manager_->canAccessContent(session_id, content_type);
}

std::string EnterpriseContentHandler::getEnterpriseStatus() const {
    std::lock_guard<std::mutex> lock(handler_mutex_);

    std::stringstream status;
    status << "EnterpriseContentHandler Status:\n";
    status << "- Initialized: " << (initialized_ ? "Yes" : "No") << "\n";
    status << "- Organization: " << current_organization_ << "\n";
    status << "- Enterprise Functions: " << enterprise_functions_.size() << "\n";
    status << "- Secure Variables: " << secure_variables_.size() << "\n";
    status << "- Enterprise Keywords: " << enterprise_keywords_.size() << "\n";

    return status.str();
}

bool EnterpriseContentHandler::isInitialized() const {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    return initialized_;
}

void EnterpriseContentHandler::setCurrentOrganization(const std::string& org_id) {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    current_organization_ = org_id;
    logEnterpriseAccess("ORG_CHANGED", "New organization: " + org_id, "");
}

std::string EnterpriseContentHandler::getCurrentOrganization() const {
    std::lock_guard<std::mutex> lock(handler_mutex_);
    return current_organization_;
}

} // namespace security
