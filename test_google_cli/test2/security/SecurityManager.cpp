#include "SecurityManager.h"
#include <random>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <openssl/sha.h>
#include <openssl/rand.h>
#include <cstring>

namespace security {

SecurityManager::SecurityManager(const SecurityConfig& config)
    : config_(config), initialized_(false) {
    
    try {
        // Initialize logging and error handling
        logger_ = utilities::Logger::getLogger("SecurityManager");
        error_handler_ = std::make_shared<utilities::ErrorHandler>(logger_);
        
        // Generate organization salt
        organization_salt_ = generateSalt();
        
        logger_->info("SecurityManager initialized", "SecurityManager", "constructor");
        logSecurityEvent("SECURITY_MANAGER_CREATED", "Security manager instance created");
        
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Failed to initialize SecurityManager: " + std::string(e.what()));
        }
        initialized_ = false;
    }
}

SecurityManager::~SecurityManager() {
    try {
        logSecurityEvent("SECURITY_MANAGER_DESTROYED", "Security manager instance destroyed");
        
        // Secure cleanup
        secureMemoryClear(secret_key_hash_);
        secureMemoryClear(enterprise_token_hash_);
        secureMemoryClear(organization_salt_);
        
        // Clear all sessions
        invalidateAllSessions();
        
        if (logger_) {
            logger_->info("SecurityManager destroyed securely", "SecurityManager", "destructor");
        }
    } catch (...) {
        // Suppress exceptions in destructor
    }
}

bool SecurityManager::initialize(const std::string& secret_key, const std::string& enterprise_token) {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    try {
        logger_->info("Initializing SecurityManager with credentials", "SecurityManager", "initialize");
        
        // Validate credential formats
        if (!isValidCredentialFormat(secret_key)) {
            logSecurityEvent("INIT_FAILED", "Invalid secret key format");
            return false;
        }
        
        if (!isValidCredentialFormat(enterprise_token)) {
            logSecurityEvent("INIT_FAILED", "Invalid enterprise token format");
            return false;
        }
        
        // Hash and store credentials
        secret_key_hash_ = hashCredential(secret_key, organization_salt_);
        enterprise_token_hash_ = hashCredential(enterprise_token, organization_salt_);
        
        initialized_ = true;
        
        logger_->info("SecurityManager initialized successfully", "SecurityManager", "initialize");
        logSecurityEvent("SECURITY_MANAGER_INITIALIZED", "Credentials set and system ready");
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize SecurityManager: " + std::string(e.what()));
        logSecurityEvent("INIT_ERROR", "Exception during initialization: " + std::string(e.what()));
        initialized_ = false;
        return false;
    }
}

AuthResult SecurityManager::authenticate(const std::string& secret_key, 
                                       const std::string& enterprise_token,
                                       const std::string& client_id) {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    try {
        logger_->debug("Authentication attempt", "SecurityManager", "authenticate");
        
        // Check if system is initialized
        if (!initialized_) {
            logSecurityEvent("AUTH_FAILED", "System not initialized", client_id);
            return AuthResult(false, SecurityLevel::INVALID, "System not ready");
        }
        
        // Check for lockout
        if (isLockedOut(client_id)) {
            logSecurityEvent("AUTH_BLOCKED", "Client locked out", client_id);
            return AuthResult(false, SecurityLevel::INVALID, "Account temporarily locked");
        }
        
        // Validate credential formats
        if (!isValidCredentialFormat(secret_key) || !isValidCredentialFormat(enterprise_token)) {
            recordFailedAttempt(client_id);
            logSecurityEvent("AUTH_FAILED", "Invalid credential format", client_id);
            return AuthResult(false, SecurityLevel::INVALID, "Invalid credentials");
        }
        
        // Validate credentials
        bool secret_valid = validateSecretKey(secret_key);
        bool token_valid = validateEnterpriseToken(enterprise_token);
        
        if (config_.require_both_credentials && (!secret_valid || !token_valid)) {
            recordFailedAttempt(client_id);
            logSecurityEvent("AUTH_FAILED", "Invalid credentials provided", client_id);
            return AuthResult(false, SecurityLevel::INVALID, "Authentication failed");
        }
        
        // Determine security level
        SecurityLevel level = SecurityLevel::STANDARD;
        if (secret_valid && token_valid) {
            level = SecurityLevel::ENTERPRISE;
        } else if (secret_valid) {
            level = SecurityLevel::STANDARD;
        }
        
        // Check session limits
        if (getActiveSessionCount() >= config_.max_concurrent_sessions) {
            logSecurityEvent("AUTH_FAILED", "Maximum concurrent sessions reached", client_id);
            return AuthResult(false, SecurityLevel::INVALID, "Too many active sessions");
        }
        
        // Create session
        std::string session_id = generateSessionId();
        auto expiry = std::chrono::system_clock::now() + 
                     (level == SecurityLevel::ENTERPRISE ? config_.enterprise_session_timeout : config_.session_timeout);
        
        SessionInfo session;
        session.session_id = session_id;
        session.level = level;
        session.created = std::chrono::system_clock::now();
        session.last_access = session.created;
        session.expiry = expiry;
        session.client_identifier = client_id;
        session.access_count = 1;
        
        active_sessions_[session_id] = session;
        
        // Clear failed attempts on successful auth
        clearFailedAttempts(client_id);
        
        logger_->info("Authentication successful", "SecurityManager", "authenticate");
        logSecurityEvent("AUTH_SUCCESS", "Level: " + std::to_string(static_cast<int>(level)), client_id);
        
        return AuthResult(true, level, session_id, expiry);
        
    } catch (const std::exception& e) {
        logger_->error("Authentication error: " + std::string(e.what()));
        logSecurityEvent("AUTH_ERROR", "Exception: " + std::string(e.what()), client_id);
        return AuthResult(false, SecurityLevel::INVALID, "Authentication error");
    }
}

bool SecurityManager::validateSession(const std::string& session_id) {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    try {
        cleanupExpiredSessions();
        
        auto it = active_sessions_.find(session_id);
        if (it == active_sessions_.end()) {
            return false;
        }
        
        SessionInfo& session = it->second;
        if (!session.isValid()) {
            active_sessions_.erase(it);
            logSecurityEvent("SESSION_EXPIRED", "Session removed", session.client_identifier);
            return false;
        }
        
        // Update last access
        session.last_access = std::chrono::system_clock::now();
        session.access_count++;
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Session validation error: " + std::string(e.what()));
        return false;
    }
}

void SecurityManager::invalidateSession(const std::string& session_id) {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    auto it = active_sessions_.find(session_id);
    if (it != active_sessions_.end()) {
        logSecurityEvent("SESSION_INVALIDATED", "Manual invalidation", it->second.client_identifier);
        active_sessions_.erase(it);
    }
}

void SecurityManager::invalidateAllSessions() {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    size_t count = active_sessions_.size();
    active_sessions_.clear();
    
    logSecurityEvent("ALL_SESSIONS_INVALIDATED", "Count: " + std::to_string(count));
    logger_->info("All sessions invalidated", "SecurityManager", "invalidateAllSessions");
}

bool SecurityManager::hasEnterpriseAccess(const std::string& session_id) const {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    auto it = active_sessions_.find(session_id);
    return it != active_sessions_.end() && 
           it->second.isValid() && 
           it->second.level == SecurityLevel::ENTERPRISE;
}

SecurityLevel SecurityManager::getSecurityLevel(const std::string& session_id) const {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    auto it = active_sessions_.find(session_id);
    if (it == active_sessions_.end() || !it->second.isValid()) {
        return SecurityLevel::INVALID;
    }
    
    return it->second.level;
}

// Private helper methods

std::string SecurityManager::generateSessionId() const {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);
    
    std::stringstream ss;
    for (int i = 0; i < 32; ++i) {
        ss << std::hex << dis(gen);
    }
    
    return ss.str();
}

std::string SecurityManager::hashCredential(const std::string& credential, const std::string& salt) const {
    std::string salted = credential + salt;
    
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, salted.c_str(), salted.length());
    SHA256_Final(hash, &sha256);
    
    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }
    
    return ss.str();
}

std::string SecurityManager::generateSalt() const {
    unsigned char salt[16];
    if (RAND_bytes(salt, sizeof(salt)) != 1) {
        // Fallback to pseudo-random if OpenSSL fails
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);
        
        for (size_t i = 0; i < sizeof(salt); ++i) {
            salt[i] = static_cast<unsigned char>(dis(gen));
        }
    }
    
    std::stringstream ss;
    for (size_t i = 0; i < sizeof(salt); ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(salt[i]);
    }
    
    return ss.str();
}

bool SecurityManager::validateSecretKey(const std::string& provided_key) const {
    std::string provided_hash = hashCredential(provided_key, organization_salt_);
    return provided_hash == secret_key_hash_;
}

bool SecurityManager::validateEnterpriseToken(const std::string& provided_token) const {
    std::string provided_hash = hashCredential(provided_token, organization_salt_);
    return provided_hash == enterprise_token_hash_;
}

bool SecurityManager::isValidCredentialFormat(const std::string& credential) const {
    return credential.length() >= MIN_KEY_LENGTH && 
           credential.length() <= MAX_CREDENTIAL_LENGTH &&
           !credential.empty();
}

void SecurityManager::secureMemoryClear(std::string& sensitive_data) {
    if (!sensitive_data.empty()) {
        std::fill(sensitive_data.begin(), sensitive_data.end(), '\0');
        sensitive_data.clear();
    }
}

void SecurityManager::logSecurityEvent(const std::string& event, const std::string& details, 
                                     const std::string& client_id) const {
    if (!config_.enable_audit_logging) return;
    
    try {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::stringstream log_entry;
        log_entry << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "] "
                  << event << " | " << details;
        
        if (!client_id.empty()) {
            log_entry << " | Client: " << client_id;
        }
        
        // Add to audit log (thread-safe)
        if (audit_log_.size() >= MAX_AUDIT_LOG_SIZE) {
            audit_log_.erase(audit_log_.begin());
        }
        audit_log_.push_back(log_entry.str());
        
        // Also log to main logger
        logger_->info("SECURITY: " + log_entry.str(), "SecurityManager", "audit");
        
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Failed to log security event: " + std::string(e.what()));
        }
    }
}

bool SecurityManager::isLockedOut(const std::string& client_id) const {
    auto it = lockout_times_.find(client_id);
    if (it == lockout_times_.end()) {
        return false;
    }

    auto now = std::chrono::system_clock::now();
    return now < (it->second + config_.lockout_duration);
}

void SecurityManager::recordFailedAttempt(const std::string& client_id) {
    failed_attempts_[client_id]++;

    if (failed_attempts_[client_id] >= config_.max_failed_attempts) {
        lockout_times_[client_id] = std::chrono::system_clock::now();
        logSecurityEvent("CLIENT_LOCKED", "Max attempts exceeded", client_id);
    }
}

void SecurityManager::clearFailedAttempts(const std::string& client_id) {
    failed_attempts_.erase(client_id);
    lockout_times_.erase(client_id);
}

void SecurityManager::cleanupExpiredSessions() {
    auto now = std::chrono::system_clock::now();

    for (auto it = active_sessions_.begin(); it != active_sessions_.end();) {
        if (it->second.isExpired()) {
            logSecurityEvent("SESSION_EXPIRED", "Automatic cleanup", it->second.client_identifier);
            it = active_sessions_.erase(it);
        } else {
            ++it;
        }
    }
}

bool SecurityManager::canAccessContent(const std::string& session_id, const std::string& content_type) const {
    std::lock_guard<std::mutex> lock(security_mutex_);

    auto it = active_sessions_.find(session_id);
    if (it == active_sessions_.end() || !it->second.isValid()) {
        return false;
    }

    // Standard content is always accessible
    if (content_type == "standard" || content_type == "public") {
        return true;
    }

    // Enterprise content requires enterprise access
    if (content_type == "enterprise" || content_type == "secure") {
        return it->second.level == SecurityLevel::ENTERPRISE;
    }

    return false;
}

SessionInfo SecurityManager::getSessionInfo(const std::string& session_id) const {
    std::lock_guard<std::mutex> lock(security_mutex_);

    auto it = active_sessions_.find(session_id);
    if (it != active_sessions_.end()) {
        return it->second;
    }

    return SessionInfo{}; // Return empty session info
}

std::vector<SessionInfo> SecurityManager::getActiveSessions() const {
    std::lock_guard<std::mutex> lock(security_mutex_);

    std::vector<SessionInfo> sessions;
    for (const auto& pair : active_sessions_) {
        if (pair.second.isValid()) {
            sessions.push_back(pair.second);
        }
    }

    return sessions;
}

size_t SecurityManager::getActiveSessionCount() const {
    std::lock_guard<std::mutex> lock(security_mutex_);

    size_t count = 0;
    for (const auto& pair : active_sessions_) {
        if (pair.second.isValid()) {
            count++;
        }
    }

    return count;
}

void SecurityManager::extendSession(const std::string& session_id) {
    std::lock_guard<std::mutex> lock(security_mutex_);

    auto it = active_sessions_.find(session_id);
    if (it != active_sessions_.end() && it->second.isValid()) {
        auto extension = (it->second.level == SecurityLevel::ENTERPRISE) ?
                        config_.enterprise_session_timeout : config_.session_timeout;
        it->second.expiry = std::chrono::system_clock::now() + extension;

        logSecurityEvent("SESSION_EXTENDED", "New expiry set", it->second.client_identifier);
    }
}

bool SecurityManager::isUnderAttack() const {
    std::lock_guard<std::mutex> lock(security_mutex_);

    // Consider system under attack if multiple clients are locked out
    return lockout_times_.size() >= 3;
}

size_t SecurityManager::getFailedAttemptCount(const std::string& client_id) const {
    std::lock_guard<std::mutex> lock(security_mutex_);

    auto it = failed_attempts_.find(client_id);
    return (it != failed_attempts_.end()) ? it->second : 0;
}

std::vector<std::string> SecurityManager::getLockedOutClients() const {
    std::lock_guard<std::mutex> lock(security_mutex_);

    std::vector<std::string> locked_clients;
    auto now = std::chrono::system_clock::now();

    for (const auto& pair : lockout_times_) {
        if (now < (pair.second + config_.lockout_duration)) {
            locked_clients.push_back(pair.first);
        }
    }

    return locked_clients;
}

void SecurityManager::unlockClient(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(security_mutex_);

    failed_attempts_.erase(client_id);
    lockout_times_.erase(client_id);

    logSecurityEvent("CLIENT_UNLOCKED", "Manual unlock", client_id);
}

bool SecurityManager::isInitialized() const {
    std::lock_guard<std::mutex> lock(security_mutex_);
    return initialized_;
}

void SecurityManager::reset() {
    std::lock_guard<std::mutex> lock(security_mutex_);

    // Secure cleanup
    secureMemoryClear(secret_key_hash_);
    secureMemoryClear(enterprise_token_hash_);

    // Clear all state
    active_sessions_.clear();
    failed_attempts_.clear();
    lockout_times_.clear();
    audit_log_.clear();

    initialized_ = false;

    logSecurityEvent("SYSTEM_RESET", "All security state cleared");
}

std::string SecurityManager::getSecurityStatus() const {
    std::lock_guard<std::mutex> lock(security_mutex_);

    std::stringstream status;
    status << "SecurityManager Status:\n";
    status << "- Initialized: " << (initialized_ ? "Yes" : "No") << "\n";
    status << "- Active Sessions: " << getActiveSessionCount() << "\n";
    status << "- Locked Clients: " << lockout_times_.size() << "\n";
    status << "- Under Attack: " << (isUnderAttack() ? "Yes" : "No") << "\n";
    status << "- Enterprise Mode: " << (isEnterpriseMode() ? "Enabled" : "Disabled") << "\n";
    status << "- Organization: " << config_.organization_id << "\n";

    return status.str();
}

bool SecurityManager::isEnterpriseMode() const {
    return config_.enable_enterprise_features && initialized_;
}

std::string SecurityManager::getOrganizationId() const {
    return config_.organization_id;
}

void SecurityManager::setOrganizationId(const std::string& org_id) {
    std::lock_guard<std::mutex> lock(security_mutex_);
    config_.organization_id = org_id;
    logSecurityEvent("ORG_ID_CHANGED", "New ID: " + org_id);
}

void SecurityManager::enableAuditLogging(bool enable) {
    std::lock_guard<std::mutex> lock(security_mutex_);
    config_.enable_audit_logging = enable;
    logSecurityEvent("AUDIT_LOGGING", enable ? "Enabled" : "Disabled");
}

std::vector<std::string> SecurityManager::getAuditLog() const {
    std::lock_guard<std::mutex> lock(security_mutex_);
    return audit_log_;
}

void SecurityManager::clearAuditLog() {
    std::lock_guard<std::mutex> lock(security_mutex_);
    audit_log_.clear();
    logSecurityEvent("AUDIT_LOG_CLEARED", "Manual clear operation");
}

void SecurityManager::updateConfiguration(const SecurityConfig& config) {
    std::lock_guard<std::mutex> lock(security_mutex_);
    config_ = config;
    logSecurityEvent("CONFIG_UPDATED", "Security configuration updated");
}

SecurityConfig SecurityManager::getConfiguration() const {
    std::lock_guard<std::mutex> lock(security_mutex_);
    return config_;
}

void SecurityManager::secureMemoryClear(char* data, size_t length) {
    if (data && length > 0) {
        std::memset(data, 0, length);
    }
}

} // namespace security
