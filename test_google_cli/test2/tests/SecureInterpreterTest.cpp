#include <gtest/gtest.h>
#include <memory>
#include <sstream>
#include "../security/SecureInterpreter.h"

using namespace security;

class SecureInterpreterTest : public ::testing::Test {
protected:
    void SetUp() override {
        SecurityConfig config;
        config.session_timeout = std::chrono::minutes(5);
        config.max_failed_attempts = 3;
        config.lockout_duration = std::chrono::minutes(5);
        config.enable_audit_logging = true;
        config.require_both_credentials = true;
        
        secure_interpreter_ = std::make_unique<SecureInterpreter>(config);
        
        // Capture output for testing
        output_stream_.str("");
        output_stream_.clear();
    }

    void TearDown() override {
        secure_interpreter_.reset();
    }

    std::unique_ptr<SecureInterpreter> secure_interpreter_;
    std::ostringstream output_stream_;
    
    const std::string test_secret_key_ = "test_secret_key_123";
    const std::string test_enterprise_token_ = "test_enterprise_token_456";
    const std::string test_client_id_ = "test_client";
    
    // Helper function to capture print output
    std::function<void(const std::string&)> getPrintCallback() {
        return [this](const std::string& output) {
            output_stream_ << output;
        };
    }
};

// Test standard JavaScript execution without credentials
TEST_F(SecureInterpreterTest, StandardJavaScriptExecution) {
    std::string js_code = "var x = 5; var y = 10; print(x + y);";
    
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        js_code, "", "", getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.execution_mode, ExecutionMode::STANDARD);
    EXPECT_FALSE(result.session_id.empty());
    EXPECT_TRUE(result.error_message.empty());
    EXPECT_EQ(output_stream_.str(), "15");
}

// Test enterprise JavaScript execution with both credentials
TEST_F(SecureInterpreterTest, EnterpriseJavaScriptExecution) {
    std::string js_code = R"(
        var data = getSecureData('user_profile');
        print('Enterprise data accessed');
    )";
    
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        js_code, test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.execution_mode, ExecutionMode::ENTERPRISE);
    EXPECT_FALSE(result.session_id.empty());
    EXPECT_TRUE(result.error_message.empty());
}

// Test hybrid execution mode
TEST_F(SecureInterpreterTest, HybridJavaScriptExecution) {
    std::string js_code = R"(
        var x = 5;
        var y = 10;
        print(x + y);
        // This should work in hybrid mode
        var config = getOrgConfig();
    )";
    
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        js_code, test_secret_key_, "", getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.execution_mode, ExecutionMode::HYBRID);
    EXPECT_FALSE(result.session_id.empty());
}

// Test access control for enterprise functions
TEST_F(SecureInterpreterTest, EnterpriseAccessControl) {
    std::string js_code = "var data = getSecureData('sensitive_info');";
    
    // Without proper credentials, should fail or be restricted
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        js_code, "", "", getPrintCallback(), test_client_id_);
    
    // Should either fail or execute in standard mode without enterprise functions
    if (result.success) {
        EXPECT_NE(result.execution_mode, ExecutionMode::ENTERPRISE);
    }
}

// Test session-based execution
TEST_F(SecureInterpreterTest, SessionBasedExecution) {
    // First, create a session
    SecureInterpretationResult first_result = secure_interpreter_->interpretSecure(
        "var x = 5;", test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    ASSERT_TRUE(first_result.success);
    std::string session_id = first_result.session_id;
    
    // Use the session for subsequent execution
    SecureInterpretationResult session_result = secure_interpreter_->interpretWithSession(
        "print(x + 10);", session_id, getPrintCallback());
    
    EXPECT_TRUE(session_result.success);
    EXPECT_EQ(session_result.session_id, session_id);
}

// Test invalid session handling
TEST_F(SecureInterpreterTest, InvalidSessionHandling) {
    SecureInterpretationResult result = secure_interpreter_->interpretWithSession(
        "var x = 5;", "invalid_session_id", getPrintCallback());
    
    EXPECT_FALSE(result.success);
    EXPECT_FALSE(result.error_message.empty());
}

// Test enterprise content detection
TEST_F(SecureInterpreterTest, EnterpriseContentDetection) {
    std::string enterprise_code = R"(
        var userData = getSecureData('user_profile');
        var orgConfig = getOrgConfig();
        setSecureData('last_login', new Date());
    )";
    
    std::string standard_code = R"(
        var x = 5;
        var y = 10;
        print(x + y);
    )";
    
    // Enterprise code with proper credentials
    SecureInterpretationResult enterprise_result = secure_interpreter_->interpretSecure(
        enterprise_code, test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(enterprise_result.success);
    EXPECT_EQ(enterprise_result.execution_mode, ExecutionMode::ENTERPRISE);
    
    // Standard code should work in any mode
    SecureInterpretationResult standard_result = secure_interpreter_->interpretSecure(
        standard_code, "", "", getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(standard_result.success);
    EXPECT_EQ(standard_result.execution_mode, ExecutionMode::STANDARD);
}

// Test security context validation
TEST_F(SecureInterpreterTest, SecurityContextValidation) {
    // Create a session with enterprise access
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        "var x = 5;", test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    ASSERT_TRUE(result.success);
    EXPECT_EQ(result.execution_mode, ExecutionMode::ENTERPRISE);
    
    // Verify security context
    EXPECT_FALSE(result.session_id.empty());
    EXPECT_TRUE(result.success);
}

// Test backward compatibility
TEST_F(SecureInterpreterTest, BackwardCompatibility) {
    // Standard JavaScript should work exactly as before
    std::string standard_js = R"(
        function fibonacci(n) {
            if (n <= 1) return n;
            return fibonacci(n-1) + fibonacci(n-2);
        }
        print(fibonacci(10));
    )";
    
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        standard_js, "", "", getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.execution_mode, ExecutionMode::STANDARD);
    EXPECT_EQ(output_stream_.str(), "55");
}

// Test error handling and logging
TEST_F(SecureInterpreterTest, ErrorHandlingAndLogging) {
    std::string invalid_js = "var x = ; // syntax error";
    
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        invalid_js, test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_FALSE(result.success);
    EXPECT_FALSE(result.error_message.empty());
    EXPECT_FALSE(result.session_id.empty()); // Session should still be created for logging
}

// Test concurrent secure execution
TEST_F(SecureInterpreterTest, ConcurrentSecureExecution) {
    const int num_threads = 5;
    std::vector<std::thread> threads;
    std::vector<SecureInterpretationResult> results(num_threads);
    
    // Launch multiple threads executing secure JavaScript
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, &results]() {
            std::string js_code = "var x = " + std::to_string(i) + "; print(x * 2);";
            results[i] = secure_interpreter_->interpretSecure(
                js_code, test_secret_key_, test_enterprise_token_, 
                [](const std::string&){}, "client_" + std::to_string(i));
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // All executions should succeed
    for (const auto& result : results) {
        EXPECT_TRUE(result.success);
        EXPECT_EQ(result.execution_mode, ExecutionMode::ENTERPRISE);
    }
}

// Test enterprise function availability
TEST_F(SecureInterpreterTest, EnterpriseFunctionAvailability) {
    // Test with enterprise credentials
    SecureInterpretationResult enterprise_result = secure_interpreter_->interpretSecure(
        "var funcs = getAvailableEnterpriseFunctions(); print(funcs.length);",
        test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(enterprise_result.success);
    EXPECT_EQ(enterprise_result.execution_mode, ExecutionMode::ENTERPRISE);
    
    // Test without enterprise credentials
    SecureInterpretationResult standard_result = secure_interpreter_->interpretSecure(
        "var funcs = getAvailableEnterpriseFunctions(); print(funcs.length);",
        "", "", getPrintCallback(), "client2");
    
    // Should either fail or return empty list
    if (standard_result.success) {
        EXPECT_NE(standard_result.execution_mode, ExecutionMode::ENTERPRISE);
    }
}

// Test secure variable access
TEST_F(SecureInterpreterTest, SecureVariableAccess) {
    std::string setup_code = R"(
        setSecureData('test_var', 'secret_value');
        print('Variable set');
    )";
    
    std::string access_code = R"(
        var value = getSecureData('test_var');
        print(value);
    )";
    
    // Set up secure variable with enterprise access
    SecureInterpretationResult setup_result = secure_interpreter_->interpretSecure(
        setup_code, test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(setup_result.success);
    
    // Access the variable with same session
    SecureInterpretationResult access_result = secure_interpreter_->interpretWithSession(
        access_code, setup_result.session_id, getPrintCallback());
    
    EXPECT_TRUE(access_result.success);
}

// Test organization context
TEST_F(SecureInterpreterTest, OrganizationContext) {
    std::string js_code = R"(
        var org = getCurrentOrganization();
        print('Organization: ' + org);
    )";
    
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        js_code, test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.execution_mode, ExecutionMode::ENTERPRISE);
}

// Test security audit trail
TEST_F(SecureInterpreterTest, SecurityAuditTrail) {
    // Execute enterprise code to generate audit events
    std::string audit_code = R"(
        var data = getSecureData('audit_test');
        setSecureData('audit_log', 'test_entry');
        var user = getCurrentUser();
    )";
    
    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        audit_code, test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.execution_mode, ExecutionMode::ENTERPRISE);
    // Audit events should be logged (verified through logging system)
}

// Test DOM manipulation security
TEST_F(SecureInterpreterTest, DOMManipulationSecurity) {
    std::string dom_code = R"(
        document.getElementById('test').innerHTML = 'Updated';
        print('DOM updated');
    )";

    SecureInterpretationResult result = secure_interpreter_->interpretSecure(
        dom_code, test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);

    EXPECT_TRUE(result.success);
    // DOM operations should be allowed in secure context
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
