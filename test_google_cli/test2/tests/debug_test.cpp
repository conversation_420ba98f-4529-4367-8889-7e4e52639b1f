#include <iostream>
#include <memory>
#include <sstream>
#include <openssl/sha.h>
#include "../security/SecureInterpreter.h"

using namespace security;

int main() {
    try {
        std::cout << "Creating SecurityConfig..." << std::endl;
        SecurityConfig config;
        config.session_timeout = std::chrono::minutes(5);
        config.max_failed_attempts = 3;
        config.lockout_duration = std::chrono::minutes(5);
        config.enable_audit_logging = true;
        config.require_both_credentials = true;
        
        std::cout << "Creating SecureInterpreter..." << std::endl;
        auto secure_interpreter = std::make_unique<SecureInterpreter>(config);
        
        std::ostringstream output_stream;
        auto print_callback = [&output_stream](const std::string& output) {
            std::cout << "Print callback called with: '" << output << "'" << std::endl;
            output_stream << output;
        };
        
        std::cout << "Testing basic JavaScript execution..." << std::endl;
        SecureInterpretationResult result = secure_interpreter->interpretSecure(
            "let x = 5; print('Hello: ' + x);", "", "", print_callback, "debug_client");

        std::cout << "Result success: " << (result.success ? "true" : "false") << std::endl;
        std::cout << "Result error: '" << result.error_message << "'" << std::endl;
        std::cout << "Result execution mode: " << static_cast<int>(result.execution_mode) << std::endl;
        std::cout << "Result session ID: '" << result.session_id << "'" << std::endl;
        std::cout << "Output stream content: '" << output_stream.str() << "'" << std::endl;

        std::cout << "\nTesting SecurityManager creation..." << std::endl;
        try {
            SecurityConfig test_config;
            test_config.session_timeout = std::chrono::minutes(30);
            test_config.enterprise_session_timeout = std::chrono::hours(8);
            test_config.max_concurrent_sessions = 100;
            test_config.max_failed_attempts = 5;
            test_config.lockout_duration = std::chrono::minutes(15);
            test_config.require_both_credentials = false;

            std::cout << "Creating SecurityManager..." << std::endl;
            auto security_manager = std::make_shared<SecurityManager>(test_config);
            std::cout << "SecurityManager created successfully!" << std::endl;

            std::cout << "Testing isInitialized..." << std::endl;
            bool is_init = security_manager->isInitialized();
            std::cout << "SecurityManager isInitialized: " << (is_init ? "true" : "false") << std::endl;

            std::cout << "Testing initialize method..." << std::endl;
            bool init_result = security_manager->initialize("test_secret_key", "test_enterprise_token");
            std::cout << "Initialize result: " << (init_result ? "true" : "false") << std::endl;

            std::cout << "Testing isInitialized after initialize..." << std::endl;
            bool is_init_after = security_manager->isInitialized();
            std::cout << "SecurityManager isInitialized after: " << (is_init_after ? "true" : "false") << std::endl;

            std::cout << "Testing simple hash..." << std::endl;
            // Test OpenSSL directly
            unsigned char hash[SHA256_DIGEST_LENGTH];
            SHA256_CTX sha256;
            std::string test_input = "test_string";

            std::cout << "Calling SHA256_Init..." << std::endl;
            SHA256_Init(&sha256);
            std::cout << "Calling SHA256_Update..." << std::endl;
            SHA256_Update(&sha256, test_input.c_str(), test_input.length());
            std::cout << "Calling SHA256_Final..." << std::endl;
            SHA256_Final(hash, &sha256);
            std::cout << "Hash completed successfully!" << std::endl;

            std::cout << "Testing credential validation..." << std::endl;
            bool secret_format_valid = (std::string("test_secret_key").length() >= 8);
            bool token_format_valid = (std::string("test_enterprise_token").length() >= 8);
            std::cout << "Secret format valid: " << (secret_format_valid ? "true" : "false") << std::endl;
            std::cout << "Token format valid: " << (token_format_valid ? "true" : "false") << std::endl;

            std::cout << "Testing step-by-step authentication..." << std::endl;
            std::cout << "Step 1: Check if initialized..." << std::endl;
            bool is_initialized = security_manager->isInitialized();
            std::cout << "Is initialized: " << (is_initialized ? "true" : "false") << std::endl;

            if (is_initialized) {
                std::cout << "Step 2: Check credential formats..." << std::endl;
                // We'll manually check the format validation logic
                std::string secret = "test_secret_key";
                std::string token = "test_enterprise_token";
                bool secret_len_ok = secret.length() >= 8 && secret.length() <= 256 && !secret.empty();
                bool token_len_ok = token.length() >= 8 && token.length() <= 256 && !token.empty();
                std::cout << "Secret length check: " << (secret_len_ok ? "true" : "false") << std::endl;
                std::cout << "Token length check: " << (token_len_ok ? "true" : "false") << std::endl;

                std::cout << "Step 3: Check security status..." << std::endl;
                std::string status = security_manager->getSecurityStatus();
                std::cout << "Security status length: " << status.length() << std::endl;
                if (status.length() < 1000) {
                    std::cout << "Security status: " << status << std::endl;
                } else {
                    std::cout << "Security status too long to display" << std::endl;
                }
            }

        } catch (const std::exception& e) {
            std::cout << "Exception creating SecurityManager: " << e.what() << std::endl;
        }
        
        return result.success ? 0 : 1;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception caught: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception caught" << std::endl;
        return 1;
    }
}
