#include <iostream>
#include <memory>
#include <sstream>
#include "../security/SecureInterpreter.h"

using namespace security;

int main() {
    try {
        std::cout << "Creating SecurityConfig..." << std::endl;
        SecurityConfig config;
        config.session_timeout = std::chrono::minutes(5);
        config.max_failed_attempts = 3;
        config.lockout_duration = std::chrono::minutes(5);
        config.enable_audit_logging = true;
        config.require_both_credentials = true;
        
        std::cout << "Creating SecureInterpreter..." << std::endl;
        auto secure_interpreter = std::make_unique<SecureInterpreter>(config);
        
        std::ostringstream output_stream;
        auto print_callback = [&output_stream](const std::string& output) {
            std::cout << "Print callback called with: '" << output << "'" << std::endl;
            output_stream << output;
        };
        
        std::cout << "Testing basic JavaScript execution..." << std::endl;
        SecureInterpretationResult result = secure_interpreter->interpretSecure(
            "let x = 5; print('Hello: ' + x);", "", "", print_callback, "debug_client");

        std::cout << "Result success: " << (result.success ? "true" : "false") << std::endl;
        std::cout << "Result error: '" << result.error_message << "'" << std::endl;
        std::cout << "Result execution mode: " << static_cast<int>(result.execution_mode) << std::endl;
        std::cout << "Result session ID: '" << result.session_id << "'" << std::endl;
        std::cout << "Output stream content: '" << output_stream.str() << "'" << std::endl;

        std::cout << "\nTesting enterprise authentication..." << std::endl;
        output_stream.str("");
        output_stream.clear();

        SecureInterpretationResult enterprise_result = secure_interpreter->interpretSecure(
            "let x = 10; print('Enterprise: ' + x);", "test_secret_key", "test_enterprise_token", print_callback, "enterprise_client");

        std::cout << "Enterprise result success: " << (enterprise_result.success ? "true" : "false") << std::endl;
        std::cout << "Enterprise result error: '" << enterprise_result.error_message << "'" << std::endl;
        std::cout << "Enterprise execution mode: " << static_cast<int>(enterprise_result.execution_mode) << std::endl;
        std::cout << "Enterprise session ID: '" << enterprise_result.session_id << "'" << std::endl;
        std::cout << "Enterprise output: '" << output_stream.str() << "'" << std::endl;
        
        return result.success ? 0 : 1;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception caught: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception caught" << std::endl;
        return 1;
    }
}
