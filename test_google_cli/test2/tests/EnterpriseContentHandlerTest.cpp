#include <gtest/gtest.h>
#include <memory>
#include "../security/EnterpriseContentHandler.h"
#include "../security/SecurityManager.h"

using namespace security;

class EnterpriseContentHandlerTest : public ::testing::Test {
protected:
    void SetUp() override {
        SecurityConfig config;
        config.session_timeout = std::chrono::minutes(5);
        config.max_failed_attempts = 3;
        config.lockout_duration = std::chrono::minutes(5);
        config.enable_audit_logging = true;
        config.require_both_credentials = true;
        
        security_manager_ = std::make_shared<SecurityManager>(config);
        enterprise_handler_ = std::make_unique<EnterpriseContentHandler>(security_manager_);
        
        // Create a valid enterprise session
        AuthResult auth_result = security_manager_->authenticate(
            test_secret_key_, test_enterprise_token_, test_client_id_);
        ASSERT_TRUE(auth_result.success);
        test_session_id_ = auth_result.session_id;
    }

    void TearDown() override {
        enterprise_handler_.reset();
        security_manager_.reset();
    }

    std::shared_ptr<SecurityManager> security_manager_;
    std::unique_ptr<EnterpriseContentHandler> enterprise_handler_;
    
    const std::string test_secret_key_ = "test_secret_key_123";
    const std::string test_enterprise_token_ = "test_enterprise_token_456";
    const std::string test_client_id_ = "test_client";
    std::string test_session_id_;
};

// Test enterprise content detection
TEST_F(EnterpriseContentHandlerTest, EnterpriseContentDetection) {
    std::string enterprise_code = R"(
        var data = getSecureData('user_profile');
        var config = getOrgConfig();
        setSecureData('last_access', new Date());
    )";
    
    std::string standard_code = R"(
        var x = 5;
        var y = 10;
        console.log(x + y);
    )";
    
    EXPECT_TRUE(enterprise_handler_->isEnterpriseContent(enterprise_code));
    EXPECT_FALSE(enterprise_handler_->isEnterpriseContent(standard_code));
}

// Test enterprise function registration and execution
TEST_F(EnterpriseContentHandlerTest, EnterpriseFunctionManagement) {
    // Register a custom enterprise function
    enterprise_handler_->registerEnterpriseFunction("customFunction", 
        [](const std::vector<Value>& args, const std::string& session_id) -> Value {
            return Value("Custom function executed");
        });
    
    EXPECT_TRUE(enterprise_handler_->hasEnterpriseFunction("customFunction"));
    
    // Call the function
    std::vector<Value> args;
    Value result = enterprise_handler_->callEnterpriseFunction("customFunction", args, test_session_id_);
    EXPECT_EQ(result.as_string(), "Custom function executed");
    
    // Unregister the function
    enterprise_handler_->unregisterEnterpriseFunction("customFunction");
    EXPECT_FALSE(enterprise_handler_->hasEnterpriseFunction("customFunction"));
}

// Test built-in enterprise functions
TEST_F(EnterpriseContentHandlerTest, BuiltInEnterpriseFunctions) {
    // Test getSecureData function
    EXPECT_TRUE(enterprise_handler_->hasEnterpriseFunction("getSecureData"));
    EXPECT_TRUE(enterprise_handler_->hasEnterpriseFunction("setSecureData"));
    EXPECT_TRUE(enterprise_handler_->hasEnterpriseFunction("getOrgConfig"));
    EXPECT_TRUE(enterprise_handler_->hasEnterpriseFunction("getCurrentUser"));
    EXPECT_TRUE(enterprise_handler_->hasEnterpriseFunction("validateAccess"));
}

// Test secure variable management
TEST_F(EnterpriseContentHandlerTest, SecureVariableManagement) {
    // Set a secure variable
    Value test_value("test_secret_data");
    bool set_result = enterprise_handler_->setSecureVariable(
        "test_var", test_value, test_session_id_, "enterprise", false);
    EXPECT_TRUE(set_result);
    
    // Get the secure variable
    Value retrieved_value = enterprise_handler_->getSecureVariable("test_var", test_session_id_);
    EXPECT_EQ(retrieved_value.as_string(), "test_secret_data");
    
    // Test access control
    EXPECT_TRUE(enterprise_handler_->canAccessSecureVariable("test_var", test_session_id_));
    
    // Test with invalid session
    EXPECT_FALSE(enterprise_handler_->canAccessSecureVariable("test_var", "invalid_session"));
}

// Test read-only secure variables
TEST_F(EnterpriseContentHandlerTest, ReadOnlySecureVariables) {
    // Set a read-only variable
    Value readonly_value("readonly_data");
    bool set_result = enterprise_handler_->setSecureVariable(
        "readonly_var", readonly_value, test_session_id_, "enterprise", true);
    EXPECT_TRUE(set_result);
    
    // Try to modify read-only variable (should fail)
    Value new_value("modified_data");
    bool modify_result = enterprise_handler_->setSecureVariable(
        "readonly_var", new_value, test_session_id_, "enterprise", false);
    EXPECT_FALSE(modify_result);
    
    // Original value should remain unchanged
    Value retrieved_value = enterprise_handler_->getSecureVariable("readonly_var", test_session_id_);
    EXPECT_EQ(retrieved_value.as_string(), "readonly_data");
}

// Test access level restrictions
TEST_F(EnterpriseContentHandlerTest, AccessLevelRestrictions) {
    // Set variables with different access levels
    Value admin_value("admin_data");
    Value user_value("user_data");
    
    enterprise_handler_->setSecureVariable("admin_var", admin_value, test_session_id_, "admin", false);
    enterprise_handler_->setSecureVariable("user_var", user_value, test_session_id_, "user", false);
    
    // Both should be accessible with enterprise session
    EXPECT_TRUE(enterprise_handler_->canAccessSecureVariable("admin_var", test_session_id_));
    EXPECT_TRUE(enterprise_handler_->canAccessSecureVariable("user_var", test_session_id_));
}

// Test organization context
TEST_F(EnterpriseContentHandlerTest, OrganizationContext) {
    const std::string test_org = "TEST_ORGANIZATION";
    
    enterprise_handler_->setCurrentOrganization(test_org);
    std::string current_org = enterprise_handler_->getCurrentOrganization();
    EXPECT_EQ(current_org, test_org);
}

// Test enterprise function access control
TEST_F(EnterpriseContentHandlerTest, EnterpriseFunctionAccessControl) {
    // Test with valid session
    std::vector<std::string> available_functions = 
        enterprise_handler_->getAvailableEnterpriseFunctions(test_session_id_);
    EXPECT_FALSE(available_functions.empty());
    
    // Test with invalid session
    std::vector<std::string> no_functions = 
        enterprise_handler_->getAvailableEnterpriseFunctions("invalid_session");
    EXPECT_TRUE(no_functions.empty());
}

// Test enterprise content parsing
TEST_F(EnterpriseContentHandlerTest, EnterpriseContentParsing) {
    std::string complex_enterprise_code = R"(
        // Enterprise-specific operations
        var userProfile = getSecureData('user_profile');
        var orgSettings = getOrgConfig();
        
        if (validateAccess('admin')) {
            setSecureData('admin_action', 'performed');
        }
        
        var currentUser = getCurrentUser();
        print('User: ' + currentUser.name);
    )";
    
    EXPECT_TRUE(enterprise_handler_->isEnterpriseContent(complex_enterprise_code));
    
    // Should contain enterprise keywords
    EXPECT_TRUE(enterprise_handler_->hasEnterpriseKeywords(complex_enterprise_code));
}

// Test threat detection integration
TEST_F(EnterpriseContentHandlerTest, ThreatDetectionIntegration) {
    // Initially not under threat
    EXPECT_FALSE(enterprise_handler_->isUnderThreat());
    
    // Simulate threat condition through security manager
    // (This would typically be triggered by multiple failed authentication attempts)
}

// Test enterprise function error handling
TEST_F(EnterpriseContentHandlerTest, EnterpriseFunctionErrorHandling) {
    // Try to call non-existent function
    std::vector<Value> args;
    Value result = enterprise_handler_->callEnterpriseFunction("nonExistentFunction", args, test_session_id_);
    
    // Should return an error value or handle gracefully
    EXPECT_TRUE(result.type == ValueType::STRING || result.type == ValueType::UNDEFINED);
}

// Test concurrent access to enterprise functions
TEST_F(EnterpriseContentHandlerTest, ConcurrentEnterpriseAccess) {
    const int num_threads = 5;
    std::vector<std::thread> threads;
    std::vector<bool> results(num_threads, false);
    
    // Create multiple sessions
    std::vector<std::string> session_ids;
    for (int i = 0; i < num_threads; ++i) {
        AuthResult auth_result = security_manager_->authenticate(
            test_secret_key_, test_enterprise_token_, "client_" + std::to_string(i));
        ASSERT_TRUE(auth_result.success);
        session_ids.push_back(auth_result.session_id);
    }
    
    // Launch multiple threads accessing enterprise functions
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, &results, &session_ids]() {
            std::vector<Value> args;
            args.push_back(Value("test_data_" + std::to_string(i)));
            
            Value result = enterprise_handler_->callEnterpriseFunction(
                "getSecureData", args, session_ids[i]);
            results[i] = (result.type != ValueType::UNDEFINED);
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // All accesses should succeed
    for (bool result : results) {
        EXPECT_TRUE(result);
    }
}

// Test enterprise keyword detection
TEST_F(EnterpriseContentHandlerTest, EnterpriseKeywordDetection) {
    std::vector<std::string> enterprise_keywords = {
        "getSecureData", "setSecureData", "getOrgConfig", 
        "getCurrentUser", "validateAccess"
    };
    
    for (const auto& keyword : enterprise_keywords) {
        std::string code = "var result = " + keyword + "('test');";
        EXPECT_TRUE(enterprise_handler_->hasEnterpriseKeywords(code));
    }
    
    std::string standard_code = "var x = 5; console.log(x);";
    EXPECT_FALSE(enterprise_handler_->hasEnterpriseKeywords(standard_code));
}

// Test session validation for enterprise operations
TEST_F(EnterpriseContentHandlerTest, SessionValidationForEnterpriseOps) {
    // Valid session should allow enterprise operations
    std::vector<Value> args;
    args.push_back(Value("test_key"));
    
    Value result = enterprise_handler_->callEnterpriseFunction("getSecureData", args, test_session_id_);
    EXPECT_NE(result.type, ValueType::UNDEFINED);
    
    // Invalid session should reject enterprise operations
    Value invalid_result = enterprise_handler_->callEnterpriseFunction("getSecureData", args, "invalid_session");
    // Should either return undefined or an error value
}

// Test enterprise content interpretation
TEST_F(EnterpriseContentHandlerTest, EnterpriseContentInterpretation) {
    std::string enterprise_script = R"(
        var config = getOrgConfig();
        var user = getCurrentUser();
        setSecureData('last_login', new Date().toString());
    )";
    
    // This would typically be called by SecureInterpreter
    EXPECT_TRUE(enterprise_handler_->isEnterpriseContent(enterprise_script));
    
    // Verify that all enterprise functions are available
    std::vector<std::string> functions = enterprise_handler_->getAvailableEnterpriseFunctions(test_session_id_);
    EXPECT_GE(functions.size(), 4); // At least the built-in functions
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
