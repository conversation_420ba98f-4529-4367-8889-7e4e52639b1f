#!/bin/bash

# Security System Test Runner
# This script builds and runs all security tests for the enterprise JavaScript interpreter

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    print_error "CMakeLists.txt not found. Please run this script from the tests directory."
    exit 1
fi

# Create build directory
BUILD_DIR="build"
if [ ! -d "$BUILD_DIR" ]; then
    print_status "Creating build directory..."
    mkdir -p "$BUILD_DIR"
fi

cd "$BUILD_DIR"

# Check for required dependencies
print_status "Checking dependencies..."

# Check for Google Test
if ! pkg-config --exists gtest; then
    print_error "Google Test not found. Please install libgtest-dev:"
    echo "  sudo apt-get install libgtest-dev"
    echo "  cd /usr/src/gtest"
    echo "  sudo cmake CMakeLists.txt"
    echo "  sudo make"
    echo "  sudo cp lib/*.a /usr/lib"
    exit 1
fi

# Check for OpenSSL
if ! pkg-config --exists openssl; then
    print_error "OpenSSL not found. Please install libssl-dev:"
    echo "  sudo apt-get install libssl-dev"
    exit 1
fi

print_success "All dependencies found"

# Configure with CMake
print_status "Configuring build with CMake..."
if ! cmake ..; then
    print_error "CMake configuration failed"
    exit 1
fi

# Build the tests
print_status "Building tests..."
if ! make -j$(nproc); then
    print_error "Build failed"
    exit 1
fi

print_success "Build completed successfully"

# Run individual test suites
echo ""
print_status "Running Security Test Suite..."
echo "=================================================="

# Array to track test results
declare -a test_results
declare -a test_names

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_executable="$2"
    
    echo ""
    print_status "Running $test_name..."
    echo "--------------------------------------------------"
    
    if ./"$test_executable"; then
        print_success "$test_name passed"
        test_results+=("PASS")
    else
        print_error "$test_name failed"
        test_results+=("FAIL")
    fi
    
    test_names+=("$test_name")
}

# Run all test suites
run_test "SecurityManager Tests" "SecurityManagerTest"
run_test "SecureInterpreter Tests" "SecureInterpreterTest"
run_test "EnterpriseContentHandler Tests" "EnterpriseContentHandlerTest"
run_test "Integration Tests" "IntegrationTest"

# Print summary
echo ""
echo "=================================================="
print_status "Test Summary"
echo "=================================================="

total_tests=${#test_names[@]}
passed_tests=0
failed_tests=0

for i in "${!test_names[@]}"; do
    test_name="${test_names[$i]}"
    result="${test_results[$i]}"
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓${NC} $test_name"
        ((passed_tests++))
    else
        echo -e "${RED}✗${NC} $test_name"
        ((failed_tests++))
    fi
done

echo ""
echo "Total Tests: $total_tests"
echo -e "Passed: ${GREEN}$passed_tests${NC}"
echo -e "Failed: ${RED}$failed_tests${NC}"

# Generate coverage report if gcov is available
if command -v gcov &> /dev/null; then
    echo ""
    print_status "Generating code coverage report..."
    
    # Find all .gcno files and generate coverage
    find . -name "*.gcno" -exec gcov {} \; > /dev/null 2>&1
    
    if [ -f "*.gcov" ]; then
        print_success "Coverage report generated (*.gcov files)"
    else
        print_warning "No coverage data found"
    fi
fi

# Exit with appropriate code
if [ $failed_tests -eq 0 ]; then
    echo ""
    print_success "All tests passed! 🎉"
    exit 0
else
    echo ""
    print_error "$failed_tests test(s) failed"
    exit 1
fi
