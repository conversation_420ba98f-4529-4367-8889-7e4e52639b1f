cmake_minimum_required(VERSION 3.16)
project(SecurityTests VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Compiler flags for testing
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra -Wpedantic --coverage")
set(CMAKE_CXX_FLAGS_RELEASE "-O2 -DNDEBUG -Wall")

# Platform-specific settings
if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN -DNOMINMAX)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
elseif(UNIX)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pthread")
endif()

# Find required packages
find_package(Threads REQUIRED)
find_package(OpenSSL REQUIRED)

# Try to find Google Test (optional)
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(GTEST QUIET gtest>=1.10.0)
    pkg_check_modules(GTEST_MAIN QUIET gtest_main>=1.10.0)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)
if(GTEST_FOUND)
    include_directories(${GTEST_INCLUDE_DIRS})
endif()

# Source files from parent directory
set(UTILITIES_SOURCES
    ../utilities/Logger.cpp
    ../utilities/Config.cpp
    ../utilities/ErrorHandler.cpp
)

set(CORE_SOURCES
    ../core/Application.cpp
    ../core/ThreadPool.cpp
)

set(JS_ENGINE_SOURCES
    ../js_engine/Interpreter.cpp
    ../js_engine/Tokenizer.cpp
)

set(SECURITY_SOURCES
    ../security/SecurityManager.cpp
    ../security/EnterpriseContentHandler.cpp
    ../security/SecureInterpreter.cpp
)

# Create libraries for testing
add_library(test_utilities STATIC ${UTILITIES_SOURCES})
add_library(test_core STATIC ${CORE_SOURCES})
add_library(test_js_engine STATIC ${JS_ENGINE_SOURCES})
add_library(test_security STATIC ${SECURITY_SOURCES})

# Library dependencies
target_link_libraries(test_core test_utilities)
target_link_libraries(test_js_engine test_utilities)
target_link_libraries(test_security test_utilities test_js_engine)

# System libraries
target_link_libraries(test_utilities Threads::Threads)
target_link_libraries(test_security OpenSSL::SSL OpenSSL::Crypto)

# Simple test executable (no external dependencies)
add_executable(simple_test simple_test.cpp)
target_link_libraries(simple_test
    test_security
    test_js_engine
    test_utilities
    Threads::Threads
)

# Debug test executable
add_executable(debug_test debug_test.cpp)
target_link_libraries(debug_test
    test_security
    test_js_engine
    test_utilities
    Threads::Threads
)

# Google Test executables (only if Google Test is available)
if(GTEST_FOUND)
    add_executable(SecurityManagerTest SecurityManagerTest.cpp)
    target_link_libraries(SecurityManagerTest
        test_security
        test_js_engine
        test_utilities
        ${GTEST_LIBRARIES}
        ${GTEST_MAIN_LIBRARIES}
        Threads::Threads
    )

    add_executable(SecureInterpreterTest SecureInterpreterTest.cpp)
    target_link_libraries(SecureInterpreterTest
        test_security
        test_js_engine
        test_utilities
        ${GTEST_LIBRARIES}
        ${GTEST_MAIN_LIBRARIES}
        Threads::Threads
    )

    add_executable(EnterpriseContentHandlerTest EnterpriseContentHandlerTest.cpp)
    target_link_libraries(EnterpriseContentHandlerTest
        test_security
        test_js_engine
        test_utilities
        ${GTEST_LIBRARIES}
        ${GTEST_MAIN_LIBRARIES}
        Threads::Threads
    )

    add_executable(IntegrationTest IntegrationTest.cpp)
    target_link_libraries(IntegrationTest
        test_security
        test_js_engine
        test_utilities
        ${GTEST_LIBRARIES}
        ${GTEST_MAIN_LIBRARIES}
        Threads::Threads
    )
endif()

# Test runner targets
add_custom_target(run_simple_test
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/simple_test
    DEPENDS simple_test
    COMMENT "Running simple security tests"
)

if(GTEST_FOUND)
    add_custom_target(run_tests
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/SecurityManagerTest
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/SecureInterpreterTest
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/EnterpriseContentHandlerTest
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/IntegrationTest
        DEPENDS SecurityManagerTest SecureInterpreterTest EnterpriseContentHandlerTest IntegrationTest
        COMMENT "Running all security tests"
    )
else()
    add_custom_target(run_tests
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/simple_test
        DEPENDS simple_test
        COMMENT "Running simple security tests (Google Test not available)"
    )
endif()

# Individual test targets
add_custom_target(test_security_manager
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/SecurityManagerTest
    DEPENDS SecurityManagerTest
    COMMENT "Running SecurityManager tests"
)

add_custom_target(test_secure_interpreter
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/SecureInterpreterTest
    DEPENDS SecureInterpreterTest
    COMMENT "Running SecureInterpreter tests"
)

add_custom_target(test_enterprise_handler
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/EnterpriseContentHandlerTest
    DEPENDS EnterpriseContentHandlerTest
    COMMENT "Running EnterpriseContentHandler tests"
)

add_custom_target(test_integration
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/IntegrationTest
    DEPENDS IntegrationTest
    COMMENT "Running Integration tests"
)

# Coverage target (if gcov is available)
find_program(GCOV_PATH gcov)
if(GCOV_PATH)
    add_custom_target(coverage
        COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/generate_coverage.sh
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating code coverage report"
    )
endif()

# Configuration summary
message(STATUS "")
message(STATUS "Security Tests Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Google Test found: ${GTEST_FOUND}")
message(STATUS "")
message(STATUS "Test targets:")
message(STATUS "  run_tests - Run all tests")
message(STATUS "  test_security_manager - Run SecurityManager tests")
message(STATUS "  test_secure_interpreter - Run SecureInterpreter tests")
message(STATUS "  test_enterprise_handler - Run EnterpriseContentHandler tests")
message(STATUS "  test_integration - Run Integration tests")
if(GCOV_PATH)
    message(STATUS "  coverage - Generate code coverage report")
endif()
message(STATUS "")
