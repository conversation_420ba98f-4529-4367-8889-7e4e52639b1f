#include <gtest/gtest.h>
#include <memory>
#include <sstream>
#include "../security/SecureInterpreter.h"

using namespace security;

class IntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        SecurityConfig config;
        config.session_timeout = std::chrono::minutes(5);
        config.max_failed_attempts = 3;
        config.lockout_duration = std::chrono::minutes(5);
        config.enable_audit_logging = true;
        config.require_both_credentials = true;
        
        secure_interpreter_ = std::make_unique<SecureInterpreter>(config);
        
        // Clear output stream
        output_stream_.str("");
        output_stream_.clear();
    }

    void TearDown() override {
        secure_interpreter_.reset();
    }

    std::unique_ptr<SecureInterpreter> secure_interpreter_;
    std::ostringstream output_stream_;
    
    const std::string valid_secret_key_ = "enterprise_secret_2024";
    const std::string valid_enterprise_token_ = "ent_token_abc123xyz";
    const std::string test_client_id_ = "integration_test_client";
    
    std::function<void(const std::string&)> getPrintCallback() {
        return [this](const std::string& output) {
            output_stream_ << output;
        };
    }
};

// Test complete workflow: Authentication -> Enterprise Operations -> Session Management
TEST_F(IntegrationTest, CompleteEnterpriseWorkflow) {
    // Step 1: Authenticate with both credentials for enterprise access
    std::string setup_code = R"(
        // Set up enterprise environment
        var orgConfig = getOrgConfig();
        setSecureData('session_start', new Date().toString());
        print('Enterprise session initialized');
    )";
    
    SecureInterpretationResult setup_result = secure_interpreter_->interpretSecure(
        setup_code, valid_secret_key_, valid_enterprise_token_, getPrintCallback(), test_client_id_);
    
    ASSERT_TRUE(setup_result.success);
    EXPECT_EQ(setup_result.execution_mode, ExecutionMode::ENTERPRISE);
    EXPECT_FALSE(setup_result.session_id.empty());
    
    // Step 2: Use the session for subsequent enterprise operations
    std::string enterprise_ops = R"(
        var userData = getSecureData('user_profile');
        var currentUser = getCurrentUser();
        
        if (validateAccess('admin')) {
            setSecureData('admin_operation', 'completed');
            print('Admin operation completed');
        } else {
            print('Standard user operation');
        }
        
        var sessionData = getSecureData('session_start');
        print('Session started at: ' + sessionData);
    )";
    
    SecureInterpretationResult ops_result = secure_interpreter_->interpretWithSession(
        enterprise_ops, setup_result.session_id, getPrintCallback());
    
    EXPECT_TRUE(ops_result.success);
    EXPECT_EQ(ops_result.session_id, setup_result.session_id);
    
    // Step 3: Verify output contains expected enterprise operations
    std::string output = output_stream_.str();
    EXPECT_TRUE(output.find("Enterprise session initialized") != std::string::npos);
}

// Test security boundary enforcement
TEST_F(IntegrationTest, SecurityBoundaryEnforcement) {
    // Test 1: Standard mode should not access enterprise functions
    std::string restricted_code = R"(
        try {
            var data = getSecureData('sensitive_info');
            print('ERROR: Should not access secure data');
        } catch (e) {
            print('Access correctly restricted');
        }
    )";
    
    SecureInterpretationResult standard_result = secure_interpreter_->interpretSecure(
        restricted_code, "", "", getPrintCallback(), test_client_id_);
    
    // Should either fail or execute in restricted mode
    if (standard_result.success) {
        EXPECT_NE(standard_result.execution_mode, ExecutionMode::ENTERPRISE);
    }
    
    // Test 2: Enterprise mode should allow all operations
    SecureInterpretationResult enterprise_result = secure_interpreter_->interpretSecure(
        "var data = getSecureData('test'); print('Enterprise access granted');",
        valid_secret_key_, valid_enterprise_token_, getPrintCallback(), "client2");
    
    EXPECT_TRUE(enterprise_result.success);
    EXPECT_EQ(enterprise_result.execution_mode, ExecutionMode::ENTERPRISE);
}

// Test hybrid mode functionality
TEST_F(IntegrationTest, HybridModeFunctionality) {
    std::string hybrid_code = R"(
        // Standard JavaScript operations
        var x = 10;
        var y = 20;
        print('Standard calculation: ' + (x + y));
        
        // Some enterprise operations (limited access)
        var config = getOrgConfig();
        print('Organization configured');
        
        // Complex logic mixing both
        if (x > 5) {
            var user = getCurrentUser();
            print('User context available');
        }
    )";
    
    // Test with only secret key (hybrid mode)
    SecureInterpretationResult hybrid_result = secure_interpreter_->interpretSecure(
        hybrid_code, valid_secret_key_, "", getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(hybrid_result.success);
    EXPECT_EQ(hybrid_result.execution_mode, ExecutionMode::HYBRID);
    
    std::string output = output_stream_.str();
    EXPECT_TRUE(output.find("Standard calculation: 30") != std::string::npos);
}

// Test session persistence and state management
TEST_F(IntegrationTest, SessionPersistenceAndState) {
    // Create initial session with data
    std::string init_code = R"(
        var counter = 0;
        setSecureData('counter', '0');
        print('Counter initialized');
    )";
    
    SecureInterpretationResult init_result = secure_interpreter_->interpretSecure(
        init_code, valid_secret_key_, valid_enterprise_token_, getPrintCallback(), test_client_id_);
    
    ASSERT_TRUE(init_result.success);
    std::string session_id = init_result.session_id;
    
    // Increment counter in subsequent calls
    for (int i = 1; i <= 3; ++i) {
        output_stream_.str(""); // Clear output
        
        std::string increment_code = R"(
            var currentCount = parseInt(getSecureData('counter'));
            currentCount++;
            setSecureData('counter', currentCount.toString());
            print('Counter: ' + currentCount);
        )";
        
        SecureInterpretationResult increment_result = secure_interpreter_->interpretWithSession(
            increment_code, session_id, getPrintCallback());
        
        EXPECT_TRUE(increment_result.success);
        EXPECT_EQ(increment_result.session_id, session_id);
        
        std::string output = output_stream_.str();
        EXPECT_TRUE(output.find("Counter: " + std::to_string(i)) != std::string::npos);
    }
}

// Test error handling and recovery
TEST_F(IntegrationTest, ErrorHandlingAndRecovery) {
    // Test syntax error handling
    std::string syntax_error_code = "var x = ; // syntax error";
    
    SecureInterpretationResult error_result = secure_interpreter_->interpretSecure(
        syntax_error_code, valid_secret_key_, valid_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_FALSE(error_result.success);
    EXPECT_FALSE(error_result.error_message.empty());
    
    // Test recovery with valid code using same credentials
    std::string recovery_code = "var x = 5; print('Recovered: ' + x);";
    
    SecureInterpretationResult recovery_result = secure_interpreter_->interpretSecure(
        recovery_code, valid_secret_key_, valid_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(recovery_result.success);
    EXPECT_EQ(recovery_result.execution_mode, ExecutionMode::ENTERPRISE);
}

// Test concurrent multi-client access
TEST_F(IntegrationTest, ConcurrentMultiClientAccess) {
    const int num_clients = 5;
    std::vector<std::thread> threads;
    std::vector<SecureInterpretationResult> results(num_clients);
    
    // Launch multiple clients with different credentials
    for (int i = 0; i < num_clients; ++i) {
        threads.emplace_back([this, i, &results]() {
            std::string client_id = "client_" + std::to_string(i);
            std::string code = R"(
                var clientData = 'Client )" + std::to_string(i) + R"( data';
                setSecureData('client_info', clientData);
                var retrieved = getSecureData('client_info');
                print('Client )" + std::to_string(i) + R"(: ' + retrieved);
            )";
            
            results[i] = secure_interpreter_->interpretSecure(
                code, valid_secret_key_, valid_enterprise_token_, 
                [](const std::string&){}, client_id);
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // All clients should succeed
    for (const auto& result : results) {
        EXPECT_TRUE(result.success);
        EXPECT_EQ(result.execution_mode, ExecutionMode::ENTERPRISE);
        EXPECT_FALSE(result.session_id.empty());
    }
}

// Test backward compatibility with existing JavaScript
TEST_F(IntegrationTest, BackwardCompatibilityTest) {
    std::string legacy_js = R"(
        // Traditional JavaScript that should work unchanged
        function factorial(n) {
            if (n <= 1) return 1;
            return n * factorial(n - 1);
        }
        
        var result = factorial(5);
        print('Factorial of 5: ' + result);
        
        // Array operations
        var arr = [1, 2, 3, 4, 5];
        var sum = 0;
        for (var i = 0; i < arr.length; i++) {
            sum += arr[i];
        }
        print('Array sum: ' + sum);
        
        // Object operations
        var obj = {
            name: 'Test',
            value: 42,
            method: function() {
                return this.name + ': ' + this.value;
            }
        };
        print(obj.method());
    )";
    
    // Test without any credentials (standard mode)
    SecureInterpretationResult standard_result = secure_interpreter_->interpretSecure(
        legacy_js, "", "", getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(standard_result.success);
    EXPECT_EQ(standard_result.execution_mode, ExecutionMode::STANDARD);
    
    std::string output = output_stream_.str();
    EXPECT_TRUE(output.find("Factorial of 5: 120") != std::string::npos);
    EXPECT_TRUE(output.find("Array sum: 15") != std::string::npos);
    EXPECT_TRUE(output.find("Test: 42") != std::string::npos);
}

// Test enterprise function isolation between sessions
TEST_F(IntegrationTest, EnterpriseFunctionIsolation) {
    // Client 1 sets data
    SecureInterpretationResult client1_result = secure_interpreter_->interpretSecure(
        "setSecureData('shared_key', 'client1_data');",
        valid_secret_key_, valid_enterprise_token_, getPrintCallback(), "client1");
    
    ASSERT_TRUE(client1_result.success);
    
    // Client 2 tries to access client 1's data
    SecureInterpretationResult client2_result = secure_interpreter_->interpretSecure(
        "var data = getSecureData('shared_key'); print('Data: ' + data);",
        valid_secret_key_, valid_enterprise_token_, getPrintCallback(), "client2");
    
    EXPECT_TRUE(client2_result.success);
    // Data should be isolated between different client sessions
    // (Implementation detail: depends on how session isolation is implemented)
}

// Test comprehensive security audit trail
TEST_F(IntegrationTest, SecurityAuditTrail) {
    std::string audit_code = R"(
        // Perform various enterprise operations that should be audited
        var config = getOrgConfig();
        setSecureData('audit_test', 'sensitive_data');
        var user = getCurrentUser();
        var access = validateAccess('admin');
        
        print('Audit operations completed');
    )";
    
    SecureInterpretationResult audit_result = secure_interpreter_->interpretSecure(
        audit_code, valid_secret_key_, valid_enterprise_token_, getPrintCallback(), test_client_id_);
    
    EXPECT_TRUE(audit_result.success);
    EXPECT_EQ(audit_result.execution_mode, ExecutionMode::ENTERPRISE);
    
    // Audit events should be logged (verified through logging system)
    // This would typically be verified by checking log files or audit database
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
