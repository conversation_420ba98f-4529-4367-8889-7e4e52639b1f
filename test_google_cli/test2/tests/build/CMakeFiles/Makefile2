# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/tests/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/test_utilities.dir/all
all: CMakeFiles/test_core.dir/all
all: CMakeFiles/test_js_engine.dir/all
all: CMakeFiles/test_security.dir/all
all: CMakeFiles/simple_test.dir/all
all: CMakeFiles/debug_test.dir/all
all: CMakeFiles/SecurityManagerTest.dir/all
all: CMakeFiles/SecureInterpreterTest.dir/all
all: CMakeFiles/EnterpriseContentHandlerTest.dir/all
all: CMakeFiles/IntegrationTest.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/test_utilities.dir/clean
clean: CMakeFiles/test_core.dir/clean
clean: CMakeFiles/test_js_engine.dir/clean
clean: CMakeFiles/test_security.dir/clean
clean: CMakeFiles/simple_test.dir/clean
clean: CMakeFiles/debug_test.dir/clean
clean: CMakeFiles/SecurityManagerTest.dir/clean
clean: CMakeFiles/SecureInterpreterTest.dir/clean
clean: CMakeFiles/EnterpriseContentHandlerTest.dir/clean
clean: CMakeFiles/IntegrationTest.dir/clean
clean: CMakeFiles/run_simple_test.dir/clean
clean: CMakeFiles/run_tests.dir/clean
clean: CMakeFiles/test_security_manager.dir/clean
clean: CMakeFiles/test_secure_interpreter.dir/clean
clean: CMakeFiles/test_enterprise_handler.dir/clean
clean: CMakeFiles/test_integration.dir/clean
clean: CMakeFiles/coverage.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/test_utilities.dir

# All Build rule for target.
CMakeFiles/test_utilities.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_utilities.dir/build.make CMakeFiles/test_utilities.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_utilities.dir/build.make CMakeFiles/test_utilities.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=30,31,32,33 "Built target test_utilities"
.PHONY : CMakeFiles/test_utilities.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_utilities.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_utilities.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/test_utilities.dir/rule

# Convenience name for target.
test_utilities: CMakeFiles/test_utilities.dir/rule
.PHONY : test_utilities

# clean rule for target.
CMakeFiles/test_utilities.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_utilities.dir/build.make CMakeFiles/test_utilities.dir/clean
.PHONY : CMakeFiles/test_utilities.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_core.dir

# All Build rule for target.
CMakeFiles/test_core.dir/all: CMakeFiles/test_utilities.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_core.dir/build.make CMakeFiles/test_core.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_core.dir/build.make CMakeFiles/test_core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=16,17,18 "Built target test_core"
.PHONY : CMakeFiles/test_core.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/test_core.dir/rule

# Convenience name for target.
test_core: CMakeFiles/test_core.dir/rule
.PHONY : test_core

# clean rule for target.
CMakeFiles/test_core.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_core.dir/build.make CMakeFiles/test_core.dir/clean
.PHONY : CMakeFiles/test_core.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_js_engine.dir

# All Build rule for target.
CMakeFiles/test_js_engine.dir/all: CMakeFiles/test_utilities.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_js_engine.dir/build.make CMakeFiles/test_js_engine.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_js_engine.dir/build.make CMakeFiles/test_js_engine.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=21,22,23 "Built target test_js_engine"
.PHONY : CMakeFiles/test_js_engine.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_js_engine.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_js_engine.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/test_js_engine.dir/rule

# Convenience name for target.
test_js_engine: CMakeFiles/test_js_engine.dir/rule
.PHONY : test_js_engine

# clean rule for target.
CMakeFiles/test_js_engine.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_js_engine.dir/build.make CMakeFiles/test_js_engine.dir/clean
.PHONY : CMakeFiles/test_js_engine.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_security.dir

# All Build rule for target.
CMakeFiles/test_security.dir/all: CMakeFiles/test_utilities.dir/all
CMakeFiles/test_security.dir/all: CMakeFiles/test_js_engine.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_security.dir/build.make CMakeFiles/test_security.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_security.dir/build.make CMakeFiles/test_security.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=25,26,27,28 "Built target test_security"
.PHONY : CMakeFiles/test_security.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_security.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_security.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/test_security.dir/rule

# Convenience name for target.
test_security: CMakeFiles/test_security.dir/rule
.PHONY : test_security

# clean rule for target.
CMakeFiles/test_security.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_security.dir/build.make CMakeFiles/test_security.dir/clean
.PHONY : CMakeFiles/test_security.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/simple_test.dir

# All Build rule for target.
CMakeFiles/simple_test.dir/all: CMakeFiles/test_utilities.dir/all
CMakeFiles/simple_test.dir/all: CMakeFiles/test_js_engine.dir/all
CMakeFiles/simple_test.dir/all: CMakeFiles/test_security.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=14,15 "Built target simple_test"
.PHONY : CMakeFiles/simple_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/simple_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/simple_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/simple_test.dir/rule

# Convenience name for target.
simple_test: CMakeFiles/simple_test.dir/rule
.PHONY : simple_test

# clean rule for target.
CMakeFiles/simple_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/clean
.PHONY : CMakeFiles/simple_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/debug_test.dir

# All Build rule for target.
CMakeFiles/debug_test.dir/all: CMakeFiles/test_utilities.dir/all
CMakeFiles/debug_test.dir/all: CMakeFiles/test_js_engine.dir/all
CMakeFiles/debug_test.dir/all: CMakeFiles/test_security.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_test.dir/build.make CMakeFiles/debug_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_test.dir/build.make CMakeFiles/debug_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=10,11 "Built target debug_test"
.PHONY : CMakeFiles/debug_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/debug_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/debug_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/debug_test.dir/rule

# Convenience name for target.
debug_test: CMakeFiles/debug_test.dir/rule
.PHONY : debug_test

# clean rule for target.
CMakeFiles/debug_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_test.dir/build.make CMakeFiles/debug_test.dir/clean
.PHONY : CMakeFiles/debug_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/SecurityManagerTest.dir

# All Build rule for target.
CMakeFiles/SecurityManagerTest.dir/all: CMakeFiles/test_utilities.dir/all
CMakeFiles/SecurityManagerTest.dir/all: CMakeFiles/test_js_engine.dir/all
CMakeFiles/SecurityManagerTest.dir/all: CMakeFiles/test_security.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SecurityManagerTest.dir/build.make CMakeFiles/SecurityManagerTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SecurityManagerTest.dir/build.make CMakeFiles/SecurityManagerTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=7,8 "Built target SecurityManagerTest"
.PHONY : CMakeFiles/SecurityManagerTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SecurityManagerTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/SecurityManagerTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/SecurityManagerTest.dir/rule

# Convenience name for target.
SecurityManagerTest: CMakeFiles/SecurityManagerTest.dir/rule
.PHONY : SecurityManagerTest

# clean rule for target.
CMakeFiles/SecurityManagerTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SecurityManagerTest.dir/build.make CMakeFiles/SecurityManagerTest.dir/clean
.PHONY : CMakeFiles/SecurityManagerTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/SecureInterpreterTest.dir

# All Build rule for target.
CMakeFiles/SecureInterpreterTest.dir/all: CMakeFiles/test_utilities.dir/all
CMakeFiles/SecureInterpreterTest.dir/all: CMakeFiles/test_js_engine.dir/all
CMakeFiles/SecureInterpreterTest.dir/all: CMakeFiles/test_security.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SecureInterpreterTest.dir/build.make CMakeFiles/SecureInterpreterTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SecureInterpreterTest.dir/build.make CMakeFiles/SecureInterpreterTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=5,6 "Built target SecureInterpreterTest"
.PHONY : CMakeFiles/SecureInterpreterTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SecureInterpreterTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/SecureInterpreterTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/SecureInterpreterTest.dir/rule

# Convenience name for target.
SecureInterpreterTest: CMakeFiles/SecureInterpreterTest.dir/rule
.PHONY : SecureInterpreterTest

# clean rule for target.
CMakeFiles/SecureInterpreterTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SecureInterpreterTest.dir/build.make CMakeFiles/SecureInterpreterTest.dir/clean
.PHONY : CMakeFiles/SecureInterpreterTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/EnterpriseContentHandlerTest.dir

# All Build rule for target.
CMakeFiles/EnterpriseContentHandlerTest.dir/all: CMakeFiles/test_utilities.dir/all
CMakeFiles/EnterpriseContentHandlerTest.dir/all: CMakeFiles/test_js_engine.dir/all
CMakeFiles/EnterpriseContentHandlerTest.dir/all: CMakeFiles/test_security.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EnterpriseContentHandlerTest.dir/build.make CMakeFiles/EnterpriseContentHandlerTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EnterpriseContentHandlerTest.dir/build.make CMakeFiles/EnterpriseContentHandlerTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=1,2 "Built target EnterpriseContentHandlerTest"
.PHONY : CMakeFiles/EnterpriseContentHandlerTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/EnterpriseContentHandlerTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/EnterpriseContentHandlerTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/EnterpriseContentHandlerTest.dir/rule

# Convenience name for target.
EnterpriseContentHandlerTest: CMakeFiles/EnterpriseContentHandlerTest.dir/rule
.PHONY : EnterpriseContentHandlerTest

# clean rule for target.
CMakeFiles/EnterpriseContentHandlerTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EnterpriseContentHandlerTest.dir/build.make CMakeFiles/EnterpriseContentHandlerTest.dir/clean
.PHONY : CMakeFiles/EnterpriseContentHandlerTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/IntegrationTest.dir

# All Build rule for target.
CMakeFiles/IntegrationTest.dir/all: CMakeFiles/test_utilities.dir/all
CMakeFiles/IntegrationTest.dir/all: CMakeFiles/test_js_engine.dir/all
CMakeFiles/IntegrationTest.dir/all: CMakeFiles/test_security.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/IntegrationTest.dir/build.make CMakeFiles/IntegrationTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/IntegrationTest.dir/build.make CMakeFiles/IntegrationTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=3,4 "Built target IntegrationTest"
.PHONY : CMakeFiles/IntegrationTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/IntegrationTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/IntegrationTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/IntegrationTest.dir/rule

# Convenience name for target.
IntegrationTest: CMakeFiles/IntegrationTest.dir/rule
.PHONY : IntegrationTest

# clean rule for target.
CMakeFiles/IntegrationTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/IntegrationTest.dir/build.make CMakeFiles/IntegrationTest.dir/clean
.PHONY : CMakeFiles/IntegrationTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/run_simple_test.dir

# All Build rule for target.
CMakeFiles/run_simple_test.dir/all: CMakeFiles/simple_test.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_simple_test.dir/build.make CMakeFiles/run_simple_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_simple_test.dir/build.make CMakeFiles/run_simple_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=12 "Built target run_simple_test"
.PHONY : CMakeFiles/run_simple_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run_simple_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/run_simple_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/run_simple_test.dir/rule

# Convenience name for target.
run_simple_test: CMakeFiles/run_simple_test.dir/rule
.PHONY : run_simple_test

# clean rule for target.
CMakeFiles/run_simple_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_simple_test.dir/build.make CMakeFiles/run_simple_test.dir/clean
.PHONY : CMakeFiles/run_simple_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/run_tests.dir

# All Build rule for target.
CMakeFiles/run_tests.dir/all: CMakeFiles/SecurityManagerTest.dir/all
CMakeFiles/run_tests.dir/all: CMakeFiles/SecureInterpreterTest.dir/all
CMakeFiles/run_tests.dir/all: CMakeFiles/EnterpriseContentHandlerTest.dir/all
CMakeFiles/run_tests.dir/all: CMakeFiles/IntegrationTest.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=13 "Built target run_tests"
.PHONY : CMakeFiles/run_tests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/run_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/run_tests.dir/rule

# Convenience name for target.
run_tests: CMakeFiles/run_tests.dir/rule
.PHONY : run_tests

# clean rule for target.
CMakeFiles/run_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/clean
.PHONY : CMakeFiles/run_tests.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_security_manager.dir

# All Build rule for target.
CMakeFiles/test_security_manager.dir/all: CMakeFiles/SecurityManagerTest.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_security_manager.dir/build.make CMakeFiles/test_security_manager.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_security_manager.dir/build.make CMakeFiles/test_security_manager.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=29 "Built target test_security_manager"
.PHONY : CMakeFiles/test_security_manager.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_security_manager.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_security_manager.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/test_security_manager.dir/rule

# Convenience name for target.
test_security_manager: CMakeFiles/test_security_manager.dir/rule
.PHONY : test_security_manager

# clean rule for target.
CMakeFiles/test_security_manager.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_security_manager.dir/build.make CMakeFiles/test_security_manager.dir/clean
.PHONY : CMakeFiles/test_security_manager.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_secure_interpreter.dir

# All Build rule for target.
CMakeFiles/test_secure_interpreter.dir/all: CMakeFiles/SecureInterpreterTest.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_secure_interpreter.dir/build.make CMakeFiles/test_secure_interpreter.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_secure_interpreter.dir/build.make CMakeFiles/test_secure_interpreter.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=24 "Built target test_secure_interpreter"
.PHONY : CMakeFiles/test_secure_interpreter.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_secure_interpreter.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_secure_interpreter.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/test_secure_interpreter.dir/rule

# Convenience name for target.
test_secure_interpreter: CMakeFiles/test_secure_interpreter.dir/rule
.PHONY : test_secure_interpreter

# clean rule for target.
CMakeFiles/test_secure_interpreter.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_secure_interpreter.dir/build.make CMakeFiles/test_secure_interpreter.dir/clean
.PHONY : CMakeFiles/test_secure_interpreter.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_enterprise_handler.dir

# All Build rule for target.
CMakeFiles/test_enterprise_handler.dir/all: CMakeFiles/EnterpriseContentHandlerTest.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_enterprise_handler.dir/build.make CMakeFiles/test_enterprise_handler.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_enterprise_handler.dir/build.make CMakeFiles/test_enterprise_handler.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=19 "Built target test_enterprise_handler"
.PHONY : CMakeFiles/test_enterprise_handler.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_enterprise_handler.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_enterprise_handler.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/test_enterprise_handler.dir/rule

# Convenience name for target.
test_enterprise_handler: CMakeFiles/test_enterprise_handler.dir/rule
.PHONY : test_enterprise_handler

# clean rule for target.
CMakeFiles/test_enterprise_handler.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_enterprise_handler.dir/build.make CMakeFiles/test_enterprise_handler.dir/clean
.PHONY : CMakeFiles/test_enterprise_handler.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_integration.dir

# All Build rule for target.
CMakeFiles/test_integration.dir/all: CMakeFiles/IntegrationTest.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_integration.dir/build.make CMakeFiles/test_integration.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_integration.dir/build.make CMakeFiles/test_integration.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=20 "Built target test_integration"
.PHONY : CMakeFiles/test_integration.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_integration.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_integration.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/test_integration.dir/rule

# Convenience name for target.
test_integration: CMakeFiles/test_integration.dir/rule
.PHONY : test_integration

# clean rule for target.
CMakeFiles/test_integration.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_integration.dir/build.make CMakeFiles/test_integration.dir/clean
.PHONY : CMakeFiles/test_integration.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/coverage.dir

# All Build rule for target.
CMakeFiles/coverage.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage.dir/build.make CMakeFiles/coverage.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage.dir/build.make CMakeFiles/coverage.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=9 "Built target coverage"
.PHONY : CMakeFiles/coverage.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/coverage.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/coverage.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles 0
.PHONY : CMakeFiles/coverage.dir/rule

# Convenience name for target.
coverage: CMakeFiles/coverage.dir/rule
.PHONY : coverage

# clean rule for target.
CMakeFiles/coverage.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage.dir/build.make CMakeFiles/coverage.dir/clean
.PHONY : CMakeFiles/coverage.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

