# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/tests/build

# Include any dependencies generated for this target.
include CMakeFiles/test_core.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_core.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_core.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_core.dir/flags.make

CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o: CMakeFiles/test_core.dir/flags.make
CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o: /home/<USER>/test_google_cli/test2/core/Application.cpp
CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o: CMakeFiles/test_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o -MF CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o.d -o CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o -c /home/<USER>/test_google_cli/test2/core/Application.cpp

CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/core/Application.cpp > CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.i

CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/core/Application.cpp -o CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.s

CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o: CMakeFiles/test_core.dir/flags.make
CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o: /home/<USER>/test_google_cli/test2/core/ThreadPool.cpp
CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o: CMakeFiles/test_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o -MF CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o.d -o CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o -c /home/<USER>/test_google_cli/test2/core/ThreadPool.cpp

CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/core/ThreadPool.cpp > CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.i

CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/core/ThreadPool.cpp -o CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.s

# Object files for target test_core
test_core_OBJECTS = \
"CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o" \
"CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o"

# External object files for target test_core
test_core_EXTERNAL_OBJECTS =

libtest_core.a: CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/Application.cpp.o
libtest_core.a: CMakeFiles/test_core.dir/home/<USER>/test_google_cli/test2/core/ThreadPool.cpp.o
libtest_core.a: CMakeFiles/test_core.dir/build.make
libtest_core.a: CMakeFiles/test_core.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libtest_core.a"
	$(CMAKE_COMMAND) -P CMakeFiles/test_core.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_core.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_core.dir/build: libtest_core.a
.PHONY : CMakeFiles/test_core.dir/build

CMakeFiles/test_core.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_core.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_core.dir/clean

CMakeFiles/test_core.dir/depend:
	cd /home/<USER>/test_google_cli/test2/tests/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles/test_core.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_core.dir/depend

