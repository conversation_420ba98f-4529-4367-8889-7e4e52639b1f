# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/tests/build

# Utility rule file for test_enterprise_handler.

# Include any custom commands dependencies for this target.
include CMakeFiles/test_enterprise_handler.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_enterprise_handler.dir/progress.make

CMakeFiles/test_enterprise_handler: EnterpriseContentHandlerTest
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running EnterpriseContentHandler tests"
	./EnterpriseContentHandlerTest

test_enterprise_handler: CMakeFiles/test_enterprise_handler
test_enterprise_handler: CMakeFiles/test_enterprise_handler.dir/build.make
.PHONY : test_enterprise_handler

# Rule to build all files generated by this target.
CMakeFiles/test_enterprise_handler.dir/build: test_enterprise_handler
.PHONY : CMakeFiles/test_enterprise_handler.dir/build

CMakeFiles/test_enterprise_handler.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_enterprise_handler.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_enterprise_handler.dir/clean

CMakeFiles/test_enterprise_handler.dir/depend:
	cd /home/<USER>/test_google_cli/test2/tests/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles/test_enterprise_handler.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_enterprise_handler.dir/depend

