# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/tests/build

# Include any dependencies generated for this target.
include CMakeFiles/debug_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/debug_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/debug_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/debug_test.dir/flags.make

CMakeFiles/debug_test.dir/debug_test.cpp.o: CMakeFiles/debug_test.dir/flags.make
CMakeFiles/debug_test.dir/debug_test.cpp.o: /home/<USER>/test_google_cli/test2/tests/debug_test.cpp
CMakeFiles/debug_test.dir/debug_test.cpp.o: CMakeFiles/debug_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/debug_test.dir/debug_test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/debug_test.dir/debug_test.cpp.o -MF CMakeFiles/debug_test.dir/debug_test.cpp.o.d -o CMakeFiles/debug_test.dir/debug_test.cpp.o -c /home/<USER>/test_google_cli/test2/tests/debug_test.cpp

CMakeFiles/debug_test.dir/debug_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/debug_test.dir/debug_test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/tests/debug_test.cpp > CMakeFiles/debug_test.dir/debug_test.cpp.i

CMakeFiles/debug_test.dir/debug_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/debug_test.dir/debug_test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/tests/debug_test.cpp -o CMakeFiles/debug_test.dir/debug_test.cpp.s

# Object files for target debug_test
debug_test_OBJECTS = \
"CMakeFiles/debug_test.dir/debug_test.cpp.o"

# External object files for target debug_test
debug_test_EXTERNAL_OBJECTS =

debug_test: CMakeFiles/debug_test.dir/debug_test.cpp.o
debug_test: CMakeFiles/debug_test.dir/build.make
debug_test: libtest_security.a
debug_test: libtest_js_engine.a
debug_test: libtest_utilities.a
debug_test: /usr/lib/x86_64-linux-gnu/libssl.so
debug_test: /usr/lib/x86_64-linux-gnu/libcrypto.so
debug_test: CMakeFiles/debug_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable debug_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/debug_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/debug_test.dir/build: debug_test
.PHONY : CMakeFiles/debug_test.dir/build

CMakeFiles/debug_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/debug_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/debug_test.dir/clean

CMakeFiles/debug_test.dir/depend:
	cd /home/<USER>/test_google_cli/test2/tests/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles/debug_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/debug_test.dir/depend

