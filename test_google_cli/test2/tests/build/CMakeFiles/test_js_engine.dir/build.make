# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/tests/build

# Include any dependencies generated for this target.
include CMakeFiles/test_js_engine.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_js_engine.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_js_engine.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_js_engine.dir/flags.make

CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o: CMakeFiles/test_js_engine.dir/flags.make
CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o: /home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp
CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o: CMakeFiles/test_js_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o -MF CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o.d -o CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o -c /home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp

CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp > CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.i

CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp -o CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.s

CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o: CMakeFiles/test_js_engine.dir/flags.make
CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o: /home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp
CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o: CMakeFiles/test_js_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o -MF CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o.d -o CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o -c /home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp

CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp > CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.i

CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp -o CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.s

# Object files for target test_js_engine
test_js_engine_OBJECTS = \
"CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o" \
"CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o"

# External object files for target test_js_engine
test_js_engine_EXTERNAL_OBJECTS =

libtest_js_engine.a: CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp.o
libtest_js_engine.a: CMakeFiles/test_js_engine.dir/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp.o
libtest_js_engine.a: CMakeFiles/test_js_engine.dir/build.make
libtest_js_engine.a: CMakeFiles/test_js_engine.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libtest_js_engine.a"
	$(CMAKE_COMMAND) -P CMakeFiles/test_js_engine.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_js_engine.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_js_engine.dir/build: libtest_js_engine.a
.PHONY : CMakeFiles/test_js_engine.dir/build

CMakeFiles/test_js_engine.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_js_engine.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_js_engine.dir/clean

CMakeFiles/test_js_engine.dir/depend:
	cd /home/<USER>/test_google_cli/test2/tests/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles/test_js_engine.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_js_engine.dir/depend

