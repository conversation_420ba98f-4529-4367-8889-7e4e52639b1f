# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/tests/build

# Include any dependencies generated for this target.
include CMakeFiles/SecurityManagerTest.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/SecurityManagerTest.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/SecurityManagerTest.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/SecurityManagerTest.dir/flags.make

CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o: CMakeFiles/SecurityManagerTest.dir/flags.make
CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o: /home/<USER>/test_google_cli/test2/tests/SecurityManagerTest.cpp
CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o: CMakeFiles/SecurityManagerTest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o -MF CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o.d -o CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o -c /home/<USER>/test_google_cli/test2/tests/SecurityManagerTest.cpp

CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/tests/SecurityManagerTest.cpp > CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.i

CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/tests/SecurityManagerTest.cpp -o CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.s

# Object files for target SecurityManagerTest
SecurityManagerTest_OBJECTS = \
"CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o"

# External object files for target SecurityManagerTest
SecurityManagerTest_EXTERNAL_OBJECTS =

SecurityManagerTest: CMakeFiles/SecurityManagerTest.dir/SecurityManagerTest.cpp.o
SecurityManagerTest: CMakeFiles/SecurityManagerTest.dir/build.make
SecurityManagerTest: libtest_security.a
SecurityManagerTest: libtest_js_engine.a
SecurityManagerTest: libtest_utilities.a
SecurityManagerTest: /usr/lib/x86_64-linux-gnu/libssl.so
SecurityManagerTest: /usr/lib/x86_64-linux-gnu/libcrypto.so
SecurityManagerTest: CMakeFiles/SecurityManagerTest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable SecurityManagerTest"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/SecurityManagerTest.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/SecurityManagerTest.dir/build: SecurityManagerTest
.PHONY : CMakeFiles/SecurityManagerTest.dir/build

CMakeFiles/SecurityManagerTest.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/SecurityManagerTest.dir/cmake_clean.cmake
.PHONY : CMakeFiles/SecurityManagerTest.dir/clean

CMakeFiles/SecurityManagerTest.dir/depend:
	cd /home/<USER>/test_google_cli/test2/tests/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles/SecurityManagerTest.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/SecurityManagerTest.dir/depend

