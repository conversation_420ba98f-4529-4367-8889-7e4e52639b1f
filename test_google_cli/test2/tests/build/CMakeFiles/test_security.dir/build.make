# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/tests/build

# Include any dependencies generated for this target.
include CMakeFiles/test_security.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_security.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_security.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_security.dir/flags.make

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o: CMakeFiles/test_security.dir/flags.make
CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o: /home/<USER>/test_google_cli/test2/security/SecurityManager.cpp
CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o: CMakeFiles/test_security.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o -MF CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o.d -o CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o -c /home/<USER>/test_google_cli/test2/security/SecurityManager.cpp

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/security/SecurityManager.cpp > CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.i

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/security/SecurityManager.cpp -o CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.s

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o: CMakeFiles/test_security.dir/flags.make
CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o: /home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp
CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o: CMakeFiles/test_security.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o -MF CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o.d -o CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o -c /home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp > CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.i

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp -o CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.s

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o: CMakeFiles/test_security.dir/flags.make
CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o: /home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp
CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o: CMakeFiles/test_security.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o -MF CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o.d -o CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o -c /home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp > CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.i

CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp -o CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.s

# Object files for target test_security
test_security_OBJECTS = \
"CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o" \
"CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o" \
"CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o"

# External object files for target test_security
test_security_EXTERNAL_OBJECTS =

libtest_security.a: CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecurityManager.cpp.o
libtest_security.a: CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp.o
libtest_security.a: CMakeFiles/test_security.dir/home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp.o
libtest_security.a: CMakeFiles/test_security.dir/build.make
libtest_security.a: CMakeFiles/test_security.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/test2/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library libtest_security.a"
	$(CMAKE_COMMAND) -P CMakeFiles/test_security.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_security.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_security.dir/build: libtest_security.a
.PHONY : CMakeFiles/test_security.dir/build

CMakeFiles/test_security.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_security.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_security.dir/clean

CMakeFiles/test_security.dir/depend:
	cd /home/<USER>/test_google_cli/test2/tests/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build /home/<USER>/test_google_cli/test2/tests/build/CMakeFiles/test_security.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_security.dir/depend

