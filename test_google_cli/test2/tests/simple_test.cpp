#include <iostream>
#include <cassert>
#include <memory>
#include <sstream>
#include <chrono>
#include <thread>
#include "../security/SecureInterpreter.h"

using namespace security;

// Simple test framework
class SimpleTest {
private:
    static int total_tests;
    static int passed_tests;
    static int failed_tests;
    
public:
    static void assert_true(bool condition, const std::string& test_name) {
        total_tests++;
        if (condition) {
            std::cout << "✓ PASS: " << test_name << std::endl;
            passed_tests++;
        } else {
            std::cout << "✗ FAIL: " << test_name << std::endl;
            failed_tests++;
        }
    }
    
    static void assert_false(bool condition, const std::string& test_name) {
        assert_true(!condition, test_name);
    }
    
    static void assert_equals(const std::string& expected, const std::string& actual, const std::string& test_name) {
        assert_true(expected == actual, test_name + " (expected: '" + expected + "', got: '" + actual + "')");
    }
    
    static void print_summary() {
        std::cout << "\n" << std::string(50, '=') << std::endl;
        std::cout << "Test Summary:" << std::endl;
        std::cout << "Total: " << total_tests << std::endl;
        std::cout << "Passed: " << passed_tests << std::endl;
        std::cout << "Failed: " << failed_tests << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        if (failed_tests == 0) {
            std::cout << "🎉 All tests passed!" << std::endl;
        } else {
            std::cout << "❌ " << failed_tests << " test(s) failed" << std::endl;
        }
    }
    
    static int get_exit_code() {
        return failed_tests > 0 ? 1 : 0;
    }
};

int SimpleTest::total_tests = 0;
int SimpleTest::passed_tests = 0;
int SimpleTest::failed_tests = 0;

// Test helper class
class SecurityTestSuite {
private:
    std::unique_ptr<SecureInterpreter> secure_interpreter_;
    std::ostringstream output_stream_;
    
    const std::string test_secret_key_ = "test_secret_key_123";
    const std::string test_enterprise_token_ = "test_enterprise_token_456";
    const std::string test_client_id_ = "test_client";
    
    std::function<void(const std::string&)> getPrintCallback() {
        return [this](const std::string& output) {
            output_stream_ << output;
        };
    }
    
public:
    SecurityTestSuite() {
        SecurityConfig config;
        config.session_timeout = std::chrono::minutes(5);
        config.max_failed_attempts = 3;
        config.lockout_duration = std::chrono::minutes(5);
        config.enable_audit_logging = true;
        config.require_both_credentials = true;
        
        secure_interpreter_ = std::make_unique<SecureInterpreter>(config);
    }
    
    void clearOutput() {
        output_stream_.str("");
        output_stream_.clear();
    }
    
    std::string getOutput() {
        std::string output = output_stream_.str();
        // Trim trailing newline for easier testing
        if (!output.empty() && output.back() == '\n') {
            output.pop_back();
        }
        return output;
    }
    
    void testBasicAuthentication() {
        std::cout << "\n--- Testing Basic Authentication ---" << std::endl;
        
        // Test with both credentials (should get enterprise mode)
        SecureInterpretationResult enterprise_result = secure_interpreter_->interpretSecure(
            "let x = 5; print(x);", test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);

        SimpleTest::assert_true(enterprise_result.success, "Enterprise authentication should succeed");
        SimpleTest::assert_true(enterprise_result.execution_mode == ExecutionMode::ENTERPRISE, "Should get enterprise mode with both credentials");
        SimpleTest::assert_false(enterprise_result.session_id.empty(), "Should create session ID");

        // Test with only secret key (should get hybrid mode)
        clearOutput();
        SecureInterpretationResult hybrid_result = secure_interpreter_->interpretSecure(
            "let y = 10; print(y);", test_secret_key_, "", getPrintCallback(), "client2");

        SimpleTest::assert_true(hybrid_result.success, "Hybrid authentication should succeed");
        SimpleTest::assert_true(hybrid_result.execution_mode == ExecutionMode::HYBRID, "Should get hybrid mode with secret key only");

        // Test with no credentials (should get standard mode)
        clearOutput();
        SecureInterpretationResult standard_result = secure_interpreter_->interpretSecure(
            "let z = 15; print(z);", "", "", getPrintCallback(), "client3");
        
        SimpleTest::assert_true(standard_result.success, "Standard authentication should succeed");
        SimpleTest::assert_true(standard_result.execution_mode == ExecutionMode::STANDARD, "Should get standard mode with no credentials");
    }
    
    void testJavaScriptExecution() {
        std::cout << "\n--- Testing JavaScript Execution ---" << std::endl;
        
        // Test basic JavaScript execution
        clearOutput();
        SecureInterpretationResult result = secure_interpreter_->interpretSecure(
            "let x = 5; let y = 10; print(x + y);", "", "", getPrintCallback(), test_client_id_);

        SimpleTest::assert_true(result.success, "Basic JavaScript should execute successfully");
        SimpleTest::assert_equals("15", getOutput(), "Basic arithmetic should work correctly");

        // Test function definition and execution
        clearOutput();
        SecureInterpretationResult func_result = secure_interpreter_->interpretSecure(
            "function add(a, b) { return a + b; } print(add(3, 7));", "", "", getPrintCallback(), "client_func");
        
        SimpleTest::assert_true(func_result.success, "Function definition and execution should work");
        SimpleTest::assert_equals("10", getOutput(), "Function should return correct result");
    }
    
    void testEnterpriseFeatures() {
        std::cout << "\n--- Testing Enterprise Features ---" << std::endl;
        
        // Test enterprise function access with proper credentials
        clearOutput();
        SecureInterpretationResult enterprise_result = secure_interpreter_->interpretSecure(
            "let config = getOrgConfig(); print('Enterprise access granted');",
            test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);

        SimpleTest::assert_true(enterprise_result.success, "Enterprise functions should be accessible with proper credentials");
        SimpleTest::assert_true(enterprise_result.execution_mode == ExecutionMode::ENTERPRISE, "Should execute in enterprise mode");

        // Test enterprise function access without proper credentials
        clearOutput();
        SecureInterpretationResult restricted_result = secure_interpreter_->interpretSecure(
            "try { let config = getOrgConfig(); print('Should not reach here'); } catch(e) { print('Access restricted'); }",
            "", "", getPrintCallback(), "client_restricted");
        
        // Should either fail or execute in restricted mode
        SimpleTest::assert_true(restricted_result.execution_mode != ExecutionMode::ENTERPRISE, "Should not get enterprise mode without credentials");
    }
    
    void testSessionManagement() {
        std::cout << "\n--- Testing Session Management ---" << std::endl;
        
        // Create a session
        SecureInterpretationResult first_result = secure_interpreter_->interpretSecure(
            "let sessionVar = 'test_value'; print('Session created');",
            test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
        
        SimpleTest::assert_true(first_result.success, "Session creation should succeed");
        std::string session_id = first_result.session_id;
        SimpleTest::assert_false(session_id.empty(), "Session ID should not be empty");
        
        // Use the session for subsequent execution
        clearOutput();
        SecureInterpretationResult session_result = secure_interpreter_->interpretWithSession(
            "print('Using session: ' + sessionVar);", session_id, getPrintCallback());
        
        SimpleTest::assert_true(session_result.success, "Session-based execution should succeed");
        SimpleTest::assert_equals(session_id, session_result.session_id, "Session ID should be preserved");
        
        // Test invalid session
        SecureInterpretationResult invalid_session_result = secure_interpreter_->interpretWithSession(
            "print('This should fail');", "invalid_session_id", getPrintCallback());
        
        SimpleTest::assert_false(invalid_session_result.success, "Invalid session should fail");
    }
    
    void testErrorHandling() {
        std::cout << "\n--- Testing Error Handling ---" << std::endl;
        
        // Test syntax error
        SecureInterpretationResult syntax_error_result = secure_interpreter_->interpretSecure(
            "let x = ; // syntax error", test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);

        SimpleTest::assert_false(syntax_error_result.success, "Syntax error should cause failure");
        SimpleTest::assert_false(syntax_error_result.error_message.empty(), "Error message should not be empty");

        // Test recovery after error
        SecureInterpretationResult recovery_result = secure_interpreter_->interpretSecure(
            "let x = 5; print('Recovered: ' + x);", test_secret_key_, test_enterprise_token_, getPrintCallback(), test_client_id_);
        
        SimpleTest::assert_true(recovery_result.success, "Should recover after syntax error");
    }
    
    void testBackwardCompatibility() {
        std::cout << "\n--- Testing Backward Compatibility ---" << std::endl;
        
        // Test complex JavaScript that should work unchanged
        std::string complex_js = R"(
            function fibonacci(n) {
                if (n <= 1) return n;
                return fibonacci(n-1) + fibonacci(n-2);
            }
            
            let result = fibonacci(8);
            print(result);
        )";
        
        clearOutput();
        SecureInterpretationResult compat_result = secure_interpreter_->interpretSecure(
            complex_js, "", "", getPrintCallback(), test_client_id_);
        
        SimpleTest::assert_true(compat_result.success, "Complex JavaScript should execute successfully");
        SimpleTest::assert_equals("21", getOutput(), "Fibonacci function should return correct result");
        SimpleTest::assert_true(compat_result.execution_mode == ExecutionMode::STANDARD, "Should execute in standard mode");
    }
    
    void runAllTests() {
        std::cout << "🔒 Security System Test Suite" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        testBasicAuthentication();
        testJavaScriptExecution();
        testEnterpriseFeatures();
        testSessionManagement();
        testErrorHandling();
        testBackwardCompatibility();
        
        SimpleTest::print_summary();
    }
};

int main() {
    try {
        SecurityTestSuite test_suite;
        test_suite.runAllTests();
        return SimpleTest::get_exit_code();
    } catch (const std::exception& e) {
        std::cerr << "Test suite failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test suite failed with unknown exception" << std::endl;
        return 1;
    }
}
