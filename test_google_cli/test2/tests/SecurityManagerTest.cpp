#include <gtest/gtest.h>
#include <memory>
#include <chrono>
#include <thread>
#include "../security/SecurityManager.h"

using namespace security;

class SecurityManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        SecurityConfig config;
        config.session_timeout = std::chrono::minutes(1);
        config.max_failed_attempts = 3;
        config.lockout_duration = std::chrono::minutes(5);
        config.enable_audit_logging = true;
        config.require_both_credentials = true;
        
        security_manager_ = std::make_unique<SecurityManager>(config);
    }

    void TearDown() override {
        security_manager_.reset();
    }

    std::unique_ptr<SecurityManager> security_manager_;
    const std::string test_secret_key_ = "test_secret_key_123";
    const std::string test_enterprise_token_ = "test_enterprise_token_456";
    const std::string test_organization_ = "TEST_ORG";
    const std::string test_client_id_ = "test_client";
};

// Test successful authentication with both credentials
TEST_F(SecurityManagerTest, AuthenticateWithBothCredentials) {
    AuthResult result = security_manager_->authenticate(
        test_secret_key_, test_enterprise_token_, test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.security_level, SecurityLevel::ENTERPRISE);
    EXPECT_FALSE(result.session_id.empty());
    EXPECT_TRUE(result.error_message.empty());
}

// Test authentication with only secret key
TEST_F(SecurityManagerTest, AuthenticateWithSecretKeyOnly) {
    AuthResult result = security_manager_->authenticate(
        test_secret_key_, "", test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.security_level, SecurityLevel::STANDARD);
    EXPECT_FALSE(result.session_id.empty());
}

// Test authentication with only enterprise token
TEST_F(SecurityManagerTest, AuthenticateWithEnterpriseTokenOnly) {
    AuthResult result = security_manager_->authenticate(
        "", test_enterprise_token_, test_client_id_);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.security_level, SecurityLevel::STANDARD);
    EXPECT_FALSE(result.session_id.empty());
}

// Test authentication failure with no credentials
TEST_F(SecurityManagerTest, AuthenticateWithNoCredentials) {
    AuthResult result = security_manager_->authenticate("", "", test_client_id_);
    
    EXPECT_FALSE(result.success);
    EXPECT_EQ(result.security_level, SecurityLevel::NONE);
    EXPECT_TRUE(result.session_id.empty());
    EXPECT_FALSE(result.error_message.empty());
}

// Test session validation
TEST_F(SecurityManagerTest, SessionValidation) {
    // Create a session
    AuthResult auth_result = security_manager_->authenticate(
        test_secret_key_, test_enterprise_token_, test_client_id_);
    ASSERT_TRUE(auth_result.success);
    
    // Validate the session
    bool is_valid = security_manager_->validateSession(auth_result.session_id);
    EXPECT_TRUE(is_valid);
    
    // Test invalid session
    bool invalid_session = security_manager_->validateSession("invalid_session_id");
    EXPECT_FALSE(invalid_session);
}

// Test session timeout
TEST_F(SecurityManagerTest, SessionTimeout) {
    // Create a session with very short timeout
    SecurityConfig short_config;
    short_config.session_timeout = std::chrono::milliseconds(100);
    SecurityManager short_manager(short_config);
    
    AuthResult auth_result = short_manager.authenticate(
        test_secret_key_, test_enterprise_token_, test_client_id_);
    ASSERT_TRUE(auth_result.success);
    
    // Wait for timeout
    std::this_thread::sleep_for(std::chrono::milliseconds(150));
    
    // Session should be invalid now
    bool is_valid = short_manager.validateSession(auth_result.session_id);
    EXPECT_FALSE(is_valid);
}

// Test failed authentication attempts and lockout
TEST_F(SecurityManagerTest, FailedAttemptsAndLockout) {
    const std::string wrong_key = "wrong_key";
    const std::string wrong_token = "wrong_token";
    
    // Make multiple failed attempts
    for (int i = 0; i < 3; ++i) {
        AuthResult result = security_manager_->authenticate(
            wrong_key, wrong_token, test_client_id_);
        EXPECT_FALSE(result.success);
    }
    
    // Check if client is locked out
    bool is_locked = security_manager_->isClientLockedOut(test_client_id_);
    EXPECT_TRUE(is_locked);
    
    // Even correct credentials should fail during lockout
    AuthResult result = security_manager_->authenticate(
        test_secret_key_, test_enterprise_token_, test_client_id_);
    EXPECT_FALSE(result.success);
    EXPECT_FALSE(result.error_message.empty());
}

// Test security level determination
TEST_F(SecurityManagerTest, SecurityLevelDetermination) {
    // Both credentials -> Enterprise
    AuthResult enterprise_result = security_manager_->authenticate(
        test_secret_key_, test_enterprise_token_, test_client_id_);
    EXPECT_EQ(enterprise_result.security_level, SecurityLevel::ENTERPRISE);
    
    // Only secret key -> Standard
    AuthResult standard_result1 = security_manager_->authenticate(
        test_secret_key_, "", "client2");
    EXPECT_EQ(standard_result1.security_level, SecurityLevel::STANDARD);
    
    // Only enterprise token -> Standard
    AuthResult standard_result2 = security_manager_->authenticate(
        "", test_enterprise_token_, "client3");
    EXPECT_EQ(standard_result2.security_level, SecurityLevel::STANDARD);
}

// Test session information retrieval
TEST_F(SecurityManagerTest, SessionInformation) {
    AuthResult auth_result = security_manager_->authenticate(
        test_secret_key_, test_enterprise_token_, test_client_id_);
    ASSERT_TRUE(auth_result.success);
    
    SecurityLevel level = security_manager_->getSessionSecurityLevel(auth_result.session_id);
    EXPECT_EQ(level, SecurityLevel::ENTERPRISE);
    
    std::string client_id = security_manager_->getSessionClientId(auth_result.session_id);
    EXPECT_EQ(client_id, test_client_id_);
}

// Test threat detection
TEST_F(SecurityManagerTest, ThreatDetection) {
    // Initially no threat
    EXPECT_FALSE(security_manager_->isUnderAttack());
    
    // Make many failed attempts to trigger threat detection
    for (int i = 0; i < 10; ++i) {
        security_manager_->authenticate("wrong", "wrong", "attacker_" + std::to_string(i));
    }
    
    // Should detect threat
    EXPECT_TRUE(security_manager_->isUnderAttack());
}

// Test organization management
TEST_F(SecurityManagerTest, OrganizationManagement) {
    security_manager_->setCurrentOrganization(test_organization_);
    std::string current_org = security_manager_->getCurrentOrganization();
    EXPECT_EQ(current_org, test_organization_);
}

// Test credential hashing consistency
TEST_F(SecurityManagerTest, CredentialHashingConsistency) {
    // This is a white-box test that requires access to private methods
    // We'll test through authentication behavior instead
    
    // Same credentials should always produce same result
    AuthResult result1 = security_manager_->authenticate(
        test_secret_key_, test_enterprise_token_, "hash_test_client");
    AuthResult result2 = security_manager_->authenticate(
        test_secret_key_, test_enterprise_token_, "hash_test_client");
    
    EXPECT_TRUE(result1.success);
    EXPECT_TRUE(result2.success);
    EXPECT_EQ(result1.security_level, result2.security_level);
}

// Test session cleanup
TEST_F(SecurityManagerTest, SessionCleanup) {
    // Create multiple sessions
    std::vector<std::string> session_ids;
    for (int i = 0; i < 5; ++i) {
        AuthResult result = security_manager_->authenticate(
            test_secret_key_, test_enterprise_token_, "client_" + std::to_string(i));
        ASSERT_TRUE(result.success);
        session_ids.push_back(result.session_id);
    }
    
    // All sessions should be valid
    for (const auto& session_id : session_ids) {
        EXPECT_TRUE(security_manager_->validateSession(session_id));
    }
    
    // Invalidate one session
    security_manager_->invalidateSession(session_ids[0]);
    EXPECT_FALSE(security_manager_->validateSession(session_ids[0]));
    
    // Others should still be valid
    for (size_t i = 1; i < session_ids.size(); ++i) {
        EXPECT_TRUE(security_manager_->validateSession(session_ids[i]));
    }
}

// Test concurrent access
TEST_F(SecurityManagerTest, ConcurrentAccess) {
    const int num_threads = 10;
    std::vector<std::thread> threads;
    std::vector<bool> results(num_threads, false);
    
    // Launch multiple threads trying to authenticate
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, &results]() {
            AuthResult result = security_manager_->authenticate(
                test_secret_key_, test_enterprise_token_, "concurrent_client_" + std::to_string(i));
            results[i] = result.success;
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // All authentications should succeed
    for (bool result : results) {
        EXPECT_TRUE(result);
    }
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
