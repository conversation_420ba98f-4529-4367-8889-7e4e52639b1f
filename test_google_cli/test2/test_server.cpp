#include "server/HttpServer.h"
#include "server/Request.h"
#include "server/Response.h"
#include "server/Utils.h"

int main() {
    try {
        HttpServer server(8080);
        Router& router = server.get_router();

        // Simple test routes without JS interpreter
        router.get("/", [](const HttpRequest& req) {
            HttpResponse response;
            response.status_code = 200;
            response.status_message = "OK";
            response.headers["Content-Type"] = "text/html";
            response.body = "<html><body><h1>Enhanced C++ Web Server</h1>"
                           "<p>Server is running with enhanced features!</p>"
                           "<p>Path: " + req.path + "</p>"
                           "<p>Enhanced features include:</p>"
                           "<ul>"
                           "<li>Middleware support</li>"
                           "<li>Thread pool management</li>"
                           "<li>Configuration management</li>"
                           "<li>Connection statistics</li>"
                           "<li>Error handling</li>"
                           "</ul>"
                           "</body></html>";
            return response;
        });

        router.get("/about", [](const HttpRequest& req) {
            HttpResponse response;
            response.status_code = 200;
            response.status_message = "OK";
            response.headers["Content-Type"] = "text/html";
            response.body = "<html><body><h1>About Enhanced Server</h1>"
                           "<p>This is an enhanced C++ web server with advanced features.</p>"
                           "</body></html>";
            return response;
        });

        router.get("/stats", [&server](const HttpRequest& req) {
            HttpResponse response;
            response.status_code = 200;
            response.status_message = "OK";
            response.headers["Content-Type"] = "application/json";
            response.body = server.getStatsJson();
            return response;
        });

        router.get("/health", [](const HttpRequest& req) {
            HttpResponse response;
            response.status_code = 200;
            response.status_message = "OK";
            response.headers["Content-Type"] = "application/json";
            response.body = "{\"status\":\"healthy\",\"timestamp\":\"" + 
                           std::to_string(std::time(nullptr)) + "\"}";
            return response;
        });

        // Enable middleware features
        server.enableCors();
        server.enableLogging();
        server.enableSecurityHeaders();

        std::cout << "Enhanced server starting on port 8080..." << std::endl;
        std::cout << "Test endpoints:" << std::endl;
        std::cout << "  http://localhost:8080/ - Main page" << std::endl;
        std::cout << "  http://localhost:8080/about - About page" << std::endl;
        std::cout << "  http://localhost:8080/stats - Server statistics" << std::endl;
        std::cout << "  http://localhost:8080/health - Health check" << std::endl;
        std::cout << "Press Ctrl+C to stop the server." << std::endl;

        server.start();
    } catch (const std::exception& e) {
        std::cerr << "Server error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
