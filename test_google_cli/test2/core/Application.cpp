#include "Application.h"
#include <algorithm>
#include <csignal>
#include <iostream>
#include <chrono>

namespace core {

// Global event buses
EventBus<ApplicationStartedEvent> g_app_started_bus;
EventBus<ApplicationShutdownEvent> g_app_shutdown_bus;
EventBus<ServiceRegisteredEvent> g_service_registered_bus;
EventBus<ConfigurationChangedEvent> g_config_changed_bus;

// Static application instance
static std::unique_ptr<Application> g_application_instance;
static std::mutex g_instance_mutex;

// ServiceContainer implementation
void ServiceContainer::initializeAll() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (auto& [name, service] : services_) {
        try {
            service->initialize();
            
            // Publish service registered event
            ServiceRegisteredEvent event;
            event.service_name = name;
            event.service_type = typeid(*service).name();
            event.timestamp = std::chrono::system_clock::now();
            g_service_registered_bus.publish(event);
            
        } catch (const std::exception& e) {
            throw std::runtime_error("Failed to initialize service '" + name + "': " + e.what());
        }
    }
}

void ServiceContainer::shutdownAll() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Shutdown in reverse order
    for (auto it = services_.rbegin(); it != services_.rend(); ++it) {
        try {
            it->second->shutdown();
        } catch (const std::exception& e) {
            // Log error but continue shutdown
            std::cerr << "Error shutting down service '" << it->first << "': " << e.what() << std::endl;
        }
    }
}

std::vector<std::string> ServiceContainer::getServiceNames() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<std::string> names;
    names.reserve(services_.size());
    
    for (const auto& [name, service] : services_) {
        names.push_back(name);
    }
    
    return names;
}

// BaseService implementation
BaseService::BaseService(const std::string& name) 
    : name_(name), initialized_(false) {
    logger_ = utilities::Logger::getLogger("Service." + name_);
}

void BaseService::initialize() {
    if (initialized_.load()) {
        return;
    }
    
    logger_->info("Initializing service: " + name_);
    
    try {
        doInitialize();
        initialized_.store(true);
        logger_->info("Service initialized successfully: " + name_);
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize service " + name_ + ": " + e.what());
        throw;
    }
}

void BaseService::shutdown() {
    if (!initialized_.load()) {
        return;
    }
    
    logger_->info("Shutting down service: " + name_);
    
    try {
        doShutdown();
        initialized_.store(false);
        logger_->info("Service shut down successfully: " + name_);
    } catch (const std::exception& e) {
        logger_->error("Error shutting down service " + name_ + ": " + e.what());
    }
}

// BaseApplicationModule implementation
BaseApplicationModule::BaseApplicationModule(const std::string& name, int priority)
    : name_(name), priority_(priority) {
    logger_ = utilities::Logger::getLogger("Module." + name_);
}

void BaseApplicationModule::initialize() {
    logger_->info("Initializing module: " + name_);
    
    try {
        doInitialize();
        logger_->info("Module initialized successfully: " + name_);
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize module " + name_ + ": " + e.what());
        throw;
    }
}

void BaseApplicationModule::shutdown() {
    logger_->info("Shutting down module: " + name_);
    
    try {
        doShutdown();
        logger_->info("Module shut down successfully: " + name_);
    } catch (const std::exception& e) {
        logger_->error("Error shutting down module " + name_ + ": " + e.what());
    }
}

// Application implementation
Application::Application() 
    : container_(std::make_unique<ServiceContainer>()),
      running_(false),
      shutdown_requested_(false) {
    
    // Initialize core services
    logger_ = utilities::Logger::getLogger("Application");
    config_ = std::make_unique<utilities::ConfigManager>();

    // Note: Logger and ConfigManager are used directly, not as services
    // They don't inherit from IService interface

    logger_->info("Application instance created");
}

Application::~Application() {
    if (running_.load()) {
        shutdown();
    }
    logger_->info("Application instance destroyed");
}

void Application::loadConfiguration(const std::string& config_file) {
    logger_->info("Loading configuration...");
    
    if (!config_file.empty()) {
        config_->addSource(std::make_unique<utilities::FileConfigSource>(config_file, true));
    }
    
    // Add environment variables with APP_ prefix
    config_->addSource(std::make_unique<utilities::EnvironmentConfigSource>("APP_"));
    
    // Set default values
    config_->setDefault("app.name", std::string("C++ Web Server"));
    config_->setDefault("app.version", std::string("1.0.0"));
    config_->setDefault("app.log_level", std::string("INFO"));
    config_->setDefault("server.port", 8080);
    config_->setDefault("server.host", std::string("localhost"));
    config_->setDefault("server.threads", 4);
    
    // Load configuration
    config_->loadFromSources();
    
    // Setup configuration change callback
    config_->addChangeCallback([this](const std::string& key, 
                                     const utilities::ConfigValue& old_val, 
                                     const utilities::ConfigValue& new_val) {
        ConfigurationChangedEvent event;
        event.key = key;
        event.old_value = utilities::ConfigManager::toString(old_val);
        event.new_value = utilities::ConfigManager::toString(new_val);
        event.timestamp = std::chrono::system_clock::now();
        g_config_changed_bus.publish(event);
    });
    
    logger_->info("Configuration loaded successfully");
}

void Application::loadConfigurationFromArgs(int argc, char* argv[]) {
    config_->addSource(std::make_unique<utilities::CommandLineConfigSource>(argc, argv));
    config_->loadFromSources();
}

void Application::addModule(std::unique_ptr<IApplicationModule> module) {
    std::lock_guard<std::mutex> lock(modules_mutex_);
    
    logger_->info("Adding module: " + module->getName());
    modules_.push_back(std::move(module));
    
    // Sort modules by priority after adding
    sortModulesByPriority();
}

void Application::sortModulesByPriority() {
    std::sort(modules_.begin(), modules_.end(), 
              [](const std::unique_ptr<IApplicationModule>& a, 
                 const std::unique_ptr<IApplicationModule>& b) {
                  return a->getPriority() > b->getPriority();
              });
}

void Application::setupSignalHandlers() {
    // Setup signal handling in a separate thread
    signal_handler_thread_ = std::thread([this]() {
        signalHandlerLoop();
    });
}

void Application::signalHandlerLoop() {
    // Simple signal handling - in a real implementation, you might use signalfd on Linux
    // or other platform-specific mechanisms
    while (running_.load() && !shutdown_requested_.load()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void Application::initialize() {
    logger_->info("Initializing application...");
    
    // Configure modules
    {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        for (auto& module : modules_) {
            logger_->debug("Configuring module: " + module->getName());
            module->configure(*container_);
        }
    }
    
    // Initialize all services
    container_->initializeAll();
    
    // Initialize modules
    {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        for (auto& module : modules_) {
            module->initialize();
        }
    }
    
    // Setup signal handlers
    setupSignalHandlers();
    
    running_.store(true);
    
    // Publish application started event
    ApplicationStartedEvent event;
    event.timestamp = std::chrono::system_clock::now();
    event.version = config_->get<std::string>("app.version", "unknown");
    g_app_started_bus.publish(event);
    
    logger_->info("Application initialized successfully");
}

void Application::run() {
    if (!running_.load()) {
        throw std::runtime_error("Application not initialized");
    }
    
    logger_->info("Application started and running...");
    
    // Main application loop
    while (running_.load() && !shutdown_requested_.load()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    logger_->info("Application main loop exited");
}

void Application::shutdown() {
    if (!running_.load()) {
        return;
    }
    
    logger_->info("Shutting down application...");
    
    shutdown_requested_.store(true);
    
    // Publish shutdown event
    ApplicationShutdownEvent event;
    event.timestamp = std::chrono::system_clock::now();
    event.reason = "Normal shutdown";
    g_app_shutdown_bus.publish(event);
    
    // Wait for signal handler thread
    if (signal_handler_thread_.joinable()) {
        signal_handler_thread_.join();
    }
    
    // Shutdown modules in reverse order
    {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        for (auto it = modules_.rbegin(); it != modules_.rend(); ++it) {
            (*it)->shutdown();
        }
    }
    
    // Shutdown all services
    container_->shutdownAll();
    
    running_.store(false);
    
    logger_->info("Application shut down successfully");
}

void Application::requestShutdown() {
    logger_->info("Shutdown requested");
    shutdown_requested_.store(true);
}

Application& Application::getInstance() {
    std::lock_guard<std::mutex> lock(g_instance_mutex);
    
    if (!g_application_instance) {
        g_application_instance = std::make_unique<Application>();
    }
    
    return *g_application_instance;
}

void Application::setInstance(std::unique_ptr<Application> app) {
    std::lock_guard<std::mutex> lock(g_instance_mutex);
    g_application_instance = std::move(app);
}

} // namespace core
