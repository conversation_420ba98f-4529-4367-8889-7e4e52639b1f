#include "ThreadPool.h"
#include <iostream>

namespace core {

ThreadPool::ThreadPool(size_t threads, size_t max_queue_size, std::chrono::milliseconds worker_timeout)
    : stop_(false), paused_(false), max_queue_size_(max_queue_size), worker_timeout_(worker_timeout) {
    
    logger_ = utilities::Logger::getLogger("ThreadPool");
    
    logger_->info("Creating thread pool with " + std::to_string(threads) + " threads");
    
    workers_.reserve(threads);
    for (size_t i = 0; i < threads; ++i) {
        workers_.emplace_back([this] { workerLoop(); });
    }
    
    logger_->info("Thread pool created successfully");
}

ThreadPool::~ThreadPool() {
    shutdown();
}

void ThreadPool::workerLoop() {
    logger_->debug("Worker thread started");
    
    while (true) {
        Task task([]{}, TaskPriority::NORMAL, "");
        bool has_task = false;
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            
            // Wait for task or stop signal
            condition_.wait(lock, [this] {
                return stop_.load() || (!task_queue_.empty() && !paused_.load());
            });
            
            if (stop_.load()) {
                break;
            }
            
            // Wait while paused
            if (paused_.load()) {
                pause_condition_.wait(lock, [this] {
                    return !paused_.load() || stop_.load();
                });
                
                if (stop_.load()) {
                    break;
                }
            }
            
            // Get next ready task
            while (!task_queue_.empty()) {
                if (task_queue_.top().isReady()) {
                    task = task_queue_.top();
                    task_queue_.pop();
                    stats_.tasks_in_queue.fetch_sub(1);
                    has_task = true;
                    break;
                } else {
                    // No ready tasks, wait a bit
                    break;
                }
            }
        }
        
        if (has_task) {
            stats_.active_threads.fetch_add(1);
            
            try {
                auto start_time = std::chrono::high_resolution_clock::now();
                
                task.execute();
                
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
                
                stats_.tasks_completed.fetch_add(1);
                
                if (!task.getName().empty()) {
                    logger_->debug("Task '" + task.getName() + "' completed in " + 
                                 std::to_string(duration.count()) + "ms");
                }
                
            } catch (const std::exception& e) {
                stats_.tasks_failed.fetch_add(1);
                logger_->error("Task execution failed: " + std::string(e.what()));
            } catch (...) {
                stats_.tasks_failed.fetch_add(1);
                logger_->error("Task execution failed with unknown exception");
            }
            
            stats_.active_threads.fetch_sub(1);
        }
    }
    
    logger_->debug("Worker thread stopped");
}

void ThreadPool::pause() {
    logger_->info("Pausing thread pool");
    paused_.store(true);
}

void ThreadPool::resume() {
    logger_->info("Resuming thread pool");
    paused_.store(false);
    pause_condition_.notify_all();
}

void ThreadPool::shutdown() {
    if (stop_.load()) {
        return;
    }
    
    logger_->info("Shutting down thread pool gracefully");
    
    stop_.store(true);
    condition_.notify_all();
    pause_condition_.notify_all();
    
    for (std::thread& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    logger_->info("Thread pool shut down successfully. Stats: " +
                 std::to_string(stats_.tasks_completed.load()) + " completed, " +
                 std::to_string(stats_.tasks_failed.load()) + " failed");
}

void ThreadPool::shutdownNow() {
    logger_->info("Shutting down thread pool immediately");
    
    stop_.store(true);
    condition_.notify_all();
    pause_condition_.notify_all();
    
    // Clear the queue
    clearQueue();
    
    for (std::thread& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    logger_->info("Thread pool shut down immediately");
}

void ThreadPool::waitForAll() {
    while (stats_.tasks_in_queue.load() > 0 || stats_.active_threads.load() > 0) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

bool ThreadPool::waitForAll(std::chrono::milliseconds timeout) {
    auto start = std::chrono::steady_clock::now();
    
    while (stats_.tasks_in_queue.load() > 0 || stats_.active_threads.load() > 0) {
        if (std::chrono::steady_clock::now() - start > timeout) {
            return false;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    return true;
}

void ThreadPool::clearQueue() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    size_t cleared = task_queue_.size();
    while (!task_queue_.empty()) {
        task_queue_.pop();
    }
    
    stats_.tasks_in_queue.store(0);
    
    if (cleared > 0) {
        logger_->warning("Cleared " + std::to_string(cleared) + " pending tasks from queue");
    }
}

// ThreadPoolService implementation
ThreadPoolService::ThreadPoolService(size_t thread_count, size_t max_queue_size)
    : BaseService("ThreadPool"), thread_count_(thread_count), max_queue_size_(max_queue_size) {
}

void ThreadPoolService::doInitialize() {
    thread_pool_ = std::make_unique<ThreadPool>(thread_count_, max_queue_size_);
    logger_->info("ThreadPool service initialized with " + std::to_string(thread_count_) + " threads");
}

void ThreadPoolService::doShutdown() {
    if (thread_pool_) {
        thread_pool_->shutdown();
        thread_pool_.reset();
    }
    logger_->info("ThreadPool service shut down");
}

} // namespace core
