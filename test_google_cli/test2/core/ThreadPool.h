#ifndef CORE_THREADPOOL_H
#define CORE_THREADPOOL_H

#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <stdexcept>
#include <atomic>
#include <chrono>
#include "../utilities/Logger.h"
#include "Application.h"

namespace core {

/**
 * @brief Task priority levels
 */
enum class TaskPriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
};

/**
 * @brief Task wrapper with priority and metadata
 */
class Task {
private:
    std::function<void()> function_;
    TaskPriority priority_;
    std::chrono::system_clock::time_point created_at_;
    std::chrono::system_clock::time_point scheduled_at_;
    std::string name_;
    
public:
    Task(std::function<void()> func, TaskPriority priority = TaskPriority::NORMAL, 
         const std::string& name = "")
        : function_(std::move(func)), priority_(priority), 
          created_at_(std::chrono::system_clock::now()),
          scheduled_at_(std::chrono::system_clock::now()),
          name_(name) {}
    
    Task(std::function<void()> func, TaskPriority priority, 
         std::chrono::system_clock::time_point schedule_time,
         const std::string& name = "")
        : function_(std::move(func)), priority_(priority), 
          created_at_(std::chrono::system_clock::now()),
          scheduled_at_(schedule_time),
          name_(name) {}
    
    void execute() const { function_(); }
    
    TaskPriority getPriority() const { return priority_; }
    std::chrono::system_clock::time_point getCreatedAt() const { return created_at_; }
    std::chrono::system_clock::time_point getScheduledAt() const { return scheduled_at_; }
    const std::string& getName() const { return name_; }
    
    bool isReady() const {
        return std::chrono::system_clock::now() >= scheduled_at_;
    }
    
    // Comparison for priority queue (higher priority first)
    bool operator<(const Task& other) const {
        if (priority_ != other.priority_) {
            return priority_ < other.priority_;
        }
        return scheduled_at_ > other.scheduled_at_; // Earlier scheduled time has higher priority
    }
};

/**
 * @brief Thread pool statistics
 */
struct ThreadPoolStats {
    std::atomic<size_t> tasks_submitted{0};
    std::atomic<size_t> tasks_completed{0};
    std::atomic<size_t> tasks_failed{0};
    std::atomic<size_t> tasks_in_queue{0};
    std::atomic<size_t> active_threads{0};
    std::chrono::system_clock::time_point start_time;
    
    ThreadPoolStats() : start_time(std::chrono::system_clock::now()) {}
    
    double getCompletionRate() const {
        size_t submitted = tasks_submitted.load();
        return submitted > 0 ? static_cast<double>(tasks_completed.load()) / submitted : 0.0;
    }
    
    double getFailureRate() const {
        size_t completed = tasks_completed.load();
        return completed > 0 ? static_cast<double>(tasks_failed.load()) / completed : 0.0;
    }
    
    std::chrono::milliseconds getUptime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now() - start_time);
    }
};

/**
 * @brief High-performance thread pool with priority queues and scheduling
 */
class ThreadPool {
private:
    std::vector<std::thread> workers_;
    std::priority_queue<Task> task_queue_;
    std::mutex queue_mutex_;
    std::condition_variable condition_;
    std::atomic<bool> stop_;
    std::atomic<bool> paused_;
    std::condition_variable pause_condition_;
    
    ThreadPoolStats stats_;
    std::shared_ptr<utilities::Logger> logger_;
    
    size_t max_queue_size_;
    std::chrono::milliseconds worker_timeout_;
    
    void workerLoop();
    
public:
    explicit ThreadPool(size_t threads = std::thread::hardware_concurrency(),
                       size_t max_queue_size = 1000,
                       std::chrono::milliseconds worker_timeout = std::chrono::milliseconds(1000));
    
    ~ThreadPool();
    
    // Non-copyable, non-movable
    ThreadPool(const ThreadPool&) = delete;
    ThreadPool& operator=(const ThreadPool&) = delete;
    ThreadPool(ThreadPool&&) = delete;
    ThreadPool& operator=(ThreadPool&&) = delete;
    
    // Task submission
    template<class F, class... Args>
    auto submit(F&& f, Args&&... args)
        -> std::future<typename std::result_of<F(Args...)>::type> {
        return submit(TaskPriority::NORMAL, "", std::forward<F>(f), std::forward<Args>(args)...);
    }

    // Simple enqueue method for void functions (no return value)
    template<class F>
    void enqueue(F&& f) {
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);

            if (stop_.load()) {
                throw std::runtime_error("ThreadPool is stopped");
            }

            if (task_queue_.size() >= max_queue_size_) {
                throw std::runtime_error("ThreadPool queue is full");
            }

            task_queue_.emplace(std::forward<F>(f), TaskPriority::NORMAL, "");
            stats_.tasks_submitted.fetch_add(1);
            stats_.tasks_in_queue.fetch_add(1);
        }

        condition_.notify_one();
    }
    
    template<class F, class... Args>
    auto submit(TaskPriority priority, F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        return submit(priority, "", std::forward<F>(f), std::forward<Args>(args)...);
    }
    
    template<class F, class... Args>
    auto submit(TaskPriority priority, const std::string& name, F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        
        using return_type = typename std::result_of<F(Args...)>::type;
        
        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        std::future<return_type> result = task->get_future();
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            
            if (stop_.load()) {
                throw std::runtime_error("ThreadPool is stopped");
            }
            
            if (task_queue_.size() >= max_queue_size_) {
                throw std::runtime_error("ThreadPool queue is full");
            }
            
            task_queue_.emplace([task](){ (*task)(); }, priority, name);
            stats_.tasks_submitted.fetch_add(1);
            stats_.tasks_in_queue.fetch_add(1);
        }
        
        condition_.notify_one();
        return result;
    }
    
    // Scheduled task submission
    template<class F, class... Args>
    auto schedule(std::chrono::system_clock::time_point when, F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        return schedule(when, TaskPriority::NORMAL, "", std::forward<F>(f), std::forward<Args>(args)...);
    }
    
    template<class F, class... Args>
    auto schedule(std::chrono::system_clock::time_point when, TaskPriority priority, 
                  const std::string& name, F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        
        using return_type = typename std::result_of<F(Args...)>::type;
        
        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        std::future<return_type> result = task->get_future();
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            
            if (stop_.load()) {
                throw std::runtime_error("ThreadPool is stopped");
            }
            
            if (task_queue_.size() >= max_queue_size_) {
                throw std::runtime_error("ThreadPool queue is full");
            }
            
            task_queue_.emplace([task](){ (*task)(); }, priority, when, name);
            stats_.tasks_submitted.fetch_add(1);
            stats_.tasks_in_queue.fetch_add(1);
        }
        
        condition_.notify_one();
        return result;
    }
    
    // Convenience methods for delayed execution
    template<class F, class... Args>
    auto scheduleAfter(std::chrono::milliseconds delay, F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        auto when = std::chrono::system_clock::now() + delay;
        return schedule(when, std::forward<F>(f), std::forward<Args>(args)...);
    }
    
    // Pool management
    void pause();
    void resume();
    void shutdown();
    void shutdownNow();
    
    bool isPaused() const { return paused_.load(); }
    bool isStopped() const { return stop_.load(); }
    
    // Statistics and monitoring
    const ThreadPoolStats& getStats() const { return stats_; }
    size_t getThreadCount() const { return workers_.size(); }
    size_t getQueueSize() const { return stats_.tasks_in_queue.load(); }
    size_t getActiveThreads() const { return stats_.active_threads.load(); }
    
    // Configuration
    void setMaxQueueSize(size_t size) { max_queue_size_ = size; }
    void setWorkerTimeout(std::chrono::milliseconds timeout) { worker_timeout_ = timeout; }
    
    // Utility methods
    void waitForAll();
    bool waitForAll(std::chrono::milliseconds timeout);
    void clearQueue();
};

/**
 * @brief Thread pool service for dependency injection
 */
class ThreadPoolService : public core::BaseService {
private:
    std::unique_ptr<ThreadPool> thread_pool_;
    size_t thread_count_;
    size_t max_queue_size_;
    
protected:
    void doInitialize() override;
    void doShutdown() override;
    
public:
    explicit ThreadPoolService(size_t thread_count = std::thread::hardware_concurrency(),
                              size_t max_queue_size = 1000);
    
    ThreadPool& getThreadPool() { return *thread_pool_; }
    const ThreadPool& getThreadPool() const { return *thread_pool_; }
};

} // namespace core

#endif // CORE_THREADPOOL_H
