#ifndef CORE_APPLICATION_H
#define CORE_APPLICATION_H

#include <memory>
#include <string>
#include <map>
#include <functional>
#include <vector>
#include <thread>
#include <atomic>
#include <mutex>
#include "../utilities/Logger.h"
#include "../utilities/Config.h"
#include "../utilities/ErrorHandler.h"

namespace core {

/**
 * @brief Service interface for dependency injection
 */
class IService {
public:
    virtual ~IService() = default;
    virtual void initialize() = 0;
    virtual void shutdown() = 0;
    virtual const std::string& getName() const = 0;
};

/**
 * @brief Service container for dependency injection
 */
class ServiceContainer {
private:
    std::map<std::string, std::shared_ptr<IService>> services_;
    std::map<std::string, std::function<std::shared_ptr<IService>()>> factories_;
    mutable std::mutex mutex_;
    
public:
    template<typename T>
    void registerService(const std::string& name, std::shared_ptr<T> service) {
        std::lock_guard<std::mutex> lock(mutex_);
        services_[name] = std::static_pointer_cast<IService>(service);
    }
    
    template<typename T>
    void registerFactory(const std::string& name, std::function<std::shared_ptr<T>()> factory) {
        std::lock_guard<std::mutex> lock(mutex_);
        factories_[name] = [factory]() -> std::shared_ptr<IService> {
            return std::static_pointer_cast<IService>(factory());
        };
    }
    
    template<typename T>
    std::shared_ptr<T> getService(const std::string& name) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = services_.find(name);
        if (it != services_.end()) {
            return std::static_pointer_cast<T>(it->second);
        }
        
        auto factory_it = factories_.find(name);
        if (factory_it != factories_.end()) {
            auto service = factory_it->second();
            services_[name] = service;
            return std::static_pointer_cast<T>(service);
        }
        
        return nullptr;
    }
    
    void initializeAll();
    void shutdownAll();
    std::vector<std::string> getServiceNames() const;
};

/**
 * @brief Application lifecycle interface
 */
class IApplicationModule {
public:
    virtual ~IApplicationModule() = default;
    virtual void configure(ServiceContainer& container) = 0;
    virtual void initialize() = 0;
    virtual void shutdown() = 0;
    virtual const std::string& getName() const = 0;
    virtual int getPriority() const { return 0; } // Higher priority loads first
};

/**
 * @brief Main application class with dependency injection and modular architecture
 */
class Application {
private:
    std::unique_ptr<ServiceContainer> container_;
    std::vector<std::unique_ptr<IApplicationModule>> modules_;
    std::shared_ptr<utilities::Logger> logger_;
    std::shared_ptr<utilities::ConfigManager> config_;
    std::atomic<bool> running_;
    std::atomic<bool> shutdown_requested_;
    std::thread signal_handler_thread_;
    mutable std::mutex modules_mutex_;
    
    void setupSignalHandlers();
    void signalHandlerLoop();
    void sortModulesByPriority();
    
public:
    Application();
    ~Application();
    
    // Non-copyable, non-movable
    Application(const Application&) = delete;
    Application& operator=(const Application&) = delete;
    Application(Application&&) = delete;
    Application& operator=(Application&&) = delete;
    
    // Configuration
    void loadConfiguration(const std::string& config_file = "");
    void loadConfigurationFromArgs(int argc, char* argv[]);
    
    // Module management
    void addModule(std::unique_ptr<IApplicationModule> module);
    template<typename T, typename... Args>
    void addModule(Args&&... args) {
        addModule(std::make_unique<T>(std::forward<Args>(args)...));
    }
    
    // Service container access
    ServiceContainer& getContainer() { return *container_; }
    const ServiceContainer& getContainer() const { return *container_; }
    
    template<typename T>
    std::shared_ptr<T> getService(const std::string& name) {
        return container_->getService<T>(name);
    }
    
    // Core services
    std::shared_ptr<utilities::Logger> getLogger() const { return logger_; }
    std::shared_ptr<utilities::ConfigManager> getConfig() const { return config_; }
    
    // Application lifecycle
    void initialize();
    void run();
    void shutdown();
    void requestShutdown();
    
    bool isRunning() const { return running_.load(); }
    bool isShutdownRequested() const { return shutdown_requested_.load(); }
    
    // Static instance for global access
    static Application& getInstance();
    static void setInstance(std::unique_ptr<Application> app);
};

/**
 * @brief Base service implementation
 */
class BaseService : public IService {
protected:
    std::string name_;
    std::shared_ptr<utilities::Logger> logger_;
    std::atomic<bool> initialized_;
    
public:
    explicit BaseService(const std::string& name);
    virtual ~BaseService() = default;
    
    const std::string& getName() const override { return name_; }
    bool isInitialized() const { return initialized_.load(); }
    
    void initialize() override;
    void shutdown() override;
    
protected:
    virtual void doInitialize() = 0;
    virtual void doShutdown() = 0;
};

/**
 * @brief Base application module implementation
 */
class BaseApplicationModule : public IApplicationModule {
protected:
    std::string name_;
    std::shared_ptr<utilities::Logger> logger_;
    int priority_;
    
public:
    explicit BaseApplicationModule(const std::string& name, int priority = 0);
    virtual ~BaseApplicationModule() = default;
    
    const std::string& getName() const override { return name_; }
    int getPriority() const override { return priority_; }
    
    void initialize() override;
    void shutdown() override;
    
protected:
    virtual void doConfigure(ServiceContainer& container) = 0;
    virtual void doInitialize() = 0;
    virtual void doShutdown() = 0;
};

/**
 * @brief Event system for loose coupling
 */
template<typename EventType>
class EventBus {
private:
    std::vector<std::function<void(const EventType&)>> handlers_;
    mutable std::mutex mutex_;
    
public:
    void subscribe(std::function<void(const EventType&)> handler) {
        std::lock_guard<std::mutex> lock(mutex_);
        handlers_.push_back(handler);
    }
    
    void publish(const EventType& event) {
        std::lock_guard<std::mutex> lock(mutex_);
        for (const auto& handler : handlers_) {
            try {
                handler(event);
            } catch (const std::exception& e) {
                // Log error but continue with other handlers
                auto& app = Application::getInstance();
                if (auto logger = app.getLogger()) {
                    logger->error("Error in event handler: " + std::string(e.what()));
                }
            }
        }
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        handlers_.clear();
    }
    
    size_t getHandlerCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return handlers_.size();
    }
};

/**
 * @brief Common application events
 */
struct ApplicationStartedEvent {
    std::chrono::system_clock::time_point timestamp;
    std::string version;
};

struct ApplicationShutdownEvent {
    std::chrono::system_clock::time_point timestamp;
    std::string reason;
};

struct ServiceRegisteredEvent {
    std::string service_name;
    std::string service_type;
    std::chrono::system_clock::time_point timestamp;
};

struct ConfigurationChangedEvent {
    std::string key;
    std::string old_value;
    std::string new_value;
    std::chrono::system_clock::time_point timestamp;
};

// Global event buses
extern EventBus<ApplicationStartedEvent> g_app_started_bus;
extern EventBus<ApplicationShutdownEvent> g_app_shutdown_bus;
extern EventBus<ServiceRegisteredEvent> g_service_registered_bus;
extern EventBus<ConfigurationChangedEvent> g_config_changed_bus;

/**
 * @brief Utility macros for easier service registration
 */
#define REGISTER_SERVICE(container, name, type, ...) \
    container.registerService(name, std::make_shared<type>(__VA_ARGS__))

#define REGISTER_FACTORY(container, name, type, factory_func) \
    container.registerFactory<type>(name, factory_func)

#define GET_SERVICE(type, name) \
    Application::getInstance().getService<type>(name)

#define GET_LOGGER() \
    Application::getInstance().getLogger()

#define GET_CONFIG() \
    Application::getInstance().getConfig()

} // namespace core

#endif // CORE_APPLICATION_H
