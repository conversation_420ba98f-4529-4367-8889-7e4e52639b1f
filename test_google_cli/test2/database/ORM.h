#ifndef DATABASE_ORM_H
#define DATABASE_ORM_H

#include "Database.h"
#include <type_traits>
#include <sstream>
#include <vector>
#include <memory>
#include <functional>
#include <map>
#include <set>

namespace database {

/**
 * @brief Field metadata for ORM mapping
 */
struct FieldInfo {
    std::string name;
    std::string type;
    bool primary_key = false;
    bool auto_increment = false;
    bool nullable = true;
    std::string default_value;
    size_t max_length = 0;
    
    FieldInfo(const std::string& field_name, const std::string& field_type)
        : name(field_name), type(field_type) {}
    
    FieldInfo& primaryKey(bool is_pk = true) { primary_key = is_pk; return *this; }
    FieldInfo& autoIncrement(bool auto_inc = true) { auto_increment = auto_inc; return *this; }
    FieldInfo& notNull(bool not_null = true) { nullable = !not_null; return *this; }
    FieldInfo& defaultValue(const std::string& value) { default_value = value; return *this; }
    FieldInfo& maxLength(size_t length) { max_length = length; return *this; }
};

/**
 * @brief Table metadata
 */
class TableInfo {
private:
    std::string table_name_;
    std::vector<FieldInfo> fields_;
    std::vector<std::string> indexes_;
    std::vector<std::string> unique_constraints_;
    
public:
    explicit TableInfo(const std::string& name) : table_name_(name) {}
    
    TableInfo& addField(const FieldInfo& field) {
        fields_.push_back(field);
        return *this;
    }
    
    TableInfo& addIndex(const std::string& field_name) {
        indexes_.push_back(field_name);
        return *this;
    }
    
    TableInfo& addUniqueConstraint(const std::string& field_name) {
        unique_constraints_.push_back(field_name);
        return *this;
    }
    
    const std::string& getTableName() const { return table_name_; }
    const std::vector<FieldInfo>& getFields() const { return fields_; }
    const std::vector<std::string>& getIndexes() const { return indexes_; }
    const std::vector<std::string>& getUniqueConstraints() const { return unique_constraints_; }
    
    std::string generateCreateTableSQL() const;
    std::vector<std::string> generateIndexSQL() const;
};

/**
 * @brief Query builder for constructing SQL queries
 */
class QueryBuilder {
private:
    std::string table_name_;
    std::vector<std::string> select_fields_;
    std::vector<std::string> where_conditions_;
    std::vector<std::string> order_by_;
    std::vector<std::string> group_by_;
    std::string having_condition_;
    std::vector<std::string> join_clauses_;
    int limit_ = -1;
    int offset_ = 0;
    
    std::map<std::string, DbValue> parameters_;
    
public:
    explicit QueryBuilder(const std::string& table_name) : table_name_(table_name) {}
    
    // SELECT operations
    QueryBuilder& select(const std::vector<std::string>& fields);
    QueryBuilder& select(const std::string& field);
    QueryBuilder& selectAll();
    
    // WHERE conditions
    QueryBuilder& where(const std::string& condition);
    QueryBuilder& where(const std::string& field, const std::string& op, const DbValue& value);
    QueryBuilder& whereEquals(const std::string& field, const DbValue& value);
    QueryBuilder& whereNotEquals(const std::string& field, const DbValue& value);
    QueryBuilder& whereIn(const std::string& field, const std::vector<DbValue>& values);
    QueryBuilder& whereNotIn(const std::string& field, const std::vector<DbValue>& values);
    QueryBuilder& whereLike(const std::string& field, const std::string& pattern);
    QueryBuilder& whereNotNull(const std::string& field);
    QueryBuilder& whereNull(const std::string& field);
    QueryBuilder& whereBetween(const std::string& field, const DbValue& min, const DbValue& max);
    
    // Logical operators
    QueryBuilder& and_(const std::string& condition);
    QueryBuilder& or_(const std::string& condition);
    
    // JOIN operations
    QueryBuilder& innerJoin(const std::string& table, const std::string& condition);
    QueryBuilder& leftJoin(const std::string& table, const std::string& condition);
    QueryBuilder& rightJoin(const std::string& table, const std::string& condition);
    
    // ORDER BY
    QueryBuilder& orderBy(const std::string& field, const std::string& direction = "ASC");
    QueryBuilder& orderByAsc(const std::string& field);
    QueryBuilder& orderByDesc(const std::string& field);
    
    // GROUP BY and HAVING
    QueryBuilder& groupBy(const std::string& field);
    QueryBuilder& having(const std::string& condition);
    
    // LIMIT and OFFSET
    QueryBuilder& limit(int count);
    QueryBuilder& offset(int count);
    QueryBuilder& paginate(int page, int per_page);
    
    // Parameter binding
    QueryBuilder& setParameter(const std::string& name, const DbValue& value);
    
    // Build queries
    std::string buildSelect() const;
    std::string buildInsert(const std::map<std::string, DbValue>& values) const;
    std::string buildUpdate(const std::map<std::string, DbValue>& values) const;
    std::string buildDelete() const;
    std::string buildCount() const;
    
    // Execute queries
    ResultSet execute(Connection& conn) const;
    int64_t executeUpdate(Connection& conn, const std::map<std::string, DbValue>& values) const;
    int64_t executeInsert(Connection& conn, const std::map<std::string, DbValue>& values) const;
    int64_t executeDelete(Connection& conn) const;
    int64_t executeCount(Connection& conn) const;
    
private:
    std::string generateParameterName() const;
    void bindParameters(PreparedStatement& stmt) const;
};

/**
 * @brief Base model class for ORM entities
 */
class BaseModel {
protected:
    std::map<std::string, DbValue> fields_;
    std::set<std::string> dirty_fields_;
    bool is_new_record_ = true;
    
    virtual TableInfo getTableInfo() const = 0;
    virtual std::string getPrimaryKeyField() const { return "id"; }
    
public:
    virtual ~BaseModel() = default;
    
    // Field access
    template<typename T>
    T get(const std::string& field_name) const {
        auto it = fields_.find(field_name);
        if (it == fields_.end()) {
            throw std::runtime_error("Field not found: " + field_name);
        }
        
        if (std::holds_alternative<T>(it->second)) {
            return std::get<T>(it->second);
        }
        throw std::runtime_error("Type mismatch for field: " + field_name);
    }
    
    template<typename T>
    std::optional<T> getOptional(const std::string& field_name) const {
        auto it = fields_.find(field_name);
        if (it == fields_.end() || std::holds_alternative<std::nullptr_t>(it->second)) {
            return std::nullopt;
        }
        
        if (std::holds_alternative<T>(it->second)) {
            return std::get<T>(it->second);
        }
        throw std::runtime_error("Type mismatch for field: " + field_name);
    }
    
    void set(const std::string& field_name, const DbValue& value) {
        fields_[field_name] = value;
        dirty_fields_.insert(field_name);
    }
    
    bool hasField(const std::string& field_name) const {
        return fields_.find(field_name) != fields_.end();
    }
    
    // State management
    bool isNewRecord() const { return is_new_record_; }
    bool isDirty() const { return !dirty_fields_.empty(); }
    bool isFieldDirty(const std::string& field_name) const {
        return dirty_fields_.find(field_name) != dirty_fields_.end();
    }
    
    void markClean() {
        dirty_fields_.clear();
        is_new_record_ = false;
    }
    
    void markDirty(const std::string& field_name) {
        dirty_fields_.insert(field_name);
    }
    
    // Database operations
    virtual bool save(Connection& conn);
    virtual bool destroy(Connection& conn);
    virtual bool reload(Connection& conn);
    
    // Serialization
    std::string toJson() const;
    void fromJson(const std::string& json);
    
    // Validation
    virtual std::vector<std::string> validate() const { return {}; }
    bool isValid() const { return validate().empty(); }
    
protected:
    void loadFromRow(const DbRow& row);
    std::map<std::string, DbValue> getDirtyFields() const;
    std::map<std::string, DbValue> getAllFields() const { return fields_; }
};

/**
 * @brief Repository pattern for database operations
 */
template<typename ModelType>
class Repository {
private:
    DatabaseManager& db_manager_;
    std::shared_ptr<utilities::Logger> logger_;
    
public:
    explicit Repository(DatabaseManager& db_manager)
        : db_manager_(db_manager) {
        logger_ = utilities::Logger::getLogger("Repository");
    }
    
    // CRUD operations
    std::optional<ModelType> findById(const DbValue& id) {
        auto conn = db_manager_.getConnection();
        
        ModelType model;
        QueryBuilder query(model.getTableInfo().getTableName());
        query.selectAll()
             .whereEquals(model.getPrimaryKeyField(), id)
             .limit(1);
        
        auto result = query.execute(*conn);
        if (result.next()) {
            model.loadFromRow(result.getCurrentRow());
            return model;
        }
        
        return std::nullopt;
    }
    
    std::vector<ModelType> findAll() {
        auto conn = db_manager_.getConnection();
        
        ModelType model;
        QueryBuilder query(model.getTableInfo().getTableName());
        query.selectAll();
        
        auto result = query.execute(*conn);
        std::vector<ModelType> models;
        
        while (result.next()) {
            ModelType m;
            m.loadFromRow(result.getCurrentRow());
            models.push_back(std::move(m));
        }
        
        return models;
    }
    
    std::vector<ModelType> findWhere(const std::string& condition) {
        auto conn = db_manager_.getConnection();
        
        ModelType model;
        QueryBuilder query(model.getTableInfo().getTableName());
        query.selectAll().where(condition);
        
        auto result = query.execute(*conn);
        std::vector<ModelType> models;
        
        while (result.next()) {
            ModelType m;
            m.loadFromRow(result.getCurrentRow());
            models.push_back(std::move(m));
        }
        
        return models;
    }
    
    QueryBuilder query() {
        ModelType model;
        return QueryBuilder(model.getTableInfo().getTableName());
    }
    
    bool save(ModelType& model) {
        auto conn = db_manager_.getConnection();
        return model.save(*conn);
    }
    
    bool destroy(ModelType& model) {
        auto conn = db_manager_.getConnection();
        return model.destroy(*conn);
    }
    
    int64_t count() {
        auto conn = db_manager_.getConnection();
        
        ModelType model;
        QueryBuilder query(model.getTableInfo().getTableName());
        return query.executeCount(*conn);
    }
    
    // Batch operations
    bool saveAll(std::vector<ModelType>& models) {
        return db_manager_.withTransaction([&](Connection& conn) {
            for (auto& model : models) {
                if (!model.save(conn)) {
                    return false;
                }
            }
            return true;
        });
    }
    
    // Schema operations
    void createTable() {
        auto conn = db_manager_.getConnection();
        
        ModelType model;
        auto table_info = model.getTableInfo();
        
        std::string create_sql = table_info.generateCreateTableSQL();
        conn->executeUpdate(create_sql);
        
        auto index_sqls = table_info.generateIndexSQL();
        for (const auto& index_sql : index_sqls) {
            conn->executeUpdate(index_sql);
        }
        
        logger_->info("Created table: " + table_info.getTableName());
    }
    
    void dropTable() {
        auto conn = db_manager_.getConnection();
        
        ModelType model;
        auto table_info = model.getTableInfo();
        
        std::string drop_sql = "DROP TABLE IF EXISTS " + table_info.getTableName();
        conn->executeUpdate(drop_sql);
        
        logger_->info("Dropped table: " + table_info.getTableName());
    }
};

/**
 * @brief Migration system for database schema changes
 */
class Migration {
public:
    virtual ~Migration() = default;
    virtual void up(Connection& conn) = 0;
    virtual void down(Connection& conn) = 0;
    virtual std::string getName() const = 0;
    virtual int64_t getVersion() const = 0;
};

class MigrationManager {
private:
    DatabaseManager& db_manager_;
    std::vector<std::unique_ptr<Migration>> migrations_;
    std::shared_ptr<utilities::Logger> logger_;
    
    void ensureMigrationTable();
    std::set<int64_t> getAppliedMigrations();
    void recordMigration(int64_t version);
    void removeMigrationRecord(int64_t version);
    
public:
    explicit MigrationManager(DatabaseManager& db_manager);
    
    void addMigration(std::unique_ptr<Migration> migration);
    void migrate();
    void rollback(int steps = 1);
    void rollbackTo(int64_t version);
    
    std::vector<int64_t> getPendingMigrations();
    std::vector<int64_t> getAppliedMigrationVersions();
};

} // namespace database

#endif // DATABASE_ORM_H
