#ifndef DATABASE_DATABASE_H
#define DATABASE_DATABASE_H

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <functional>
#include <variant>
#include <optional>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <chrono>
#include <sqlite3.h>
#include "../utilities/Logger.h"
#include "../utilities/ErrorHandler.h"
#include "../core/Application.h"

namespace database {

/**
 * @brief Database value type
 */
using DbValue = std::variant<std::nullptr_t, int64_t, double, std::string, std::vector<uint8_t>>;

/**
 * @brief Database row type
 */
using DbRow = std::unordered_map<std::string, DbValue>;

/**
 * @brief Database result set
 */
class ResultSet {
private:
    std::vector<DbRow> rows_;
    std::vector<std::string> column_names_;
    size_t current_row_;
    
public:
    ResultSet() : current_row_(0) {}
    explicit ResultSet(std::vector<DbRow> rows, std::vector<std::string> columns)
        : rows_(std::move(rows)), column_names_(std::move(columns)), current_row_(0) {}
    
    // Iterator interface
    bool next();
    void reset();
    bool hasNext() const;
    
    // Data access
    DbValue getValue(const std::string& column) const;
    DbValue getValue(size_t index) const;
    
    template<typename T>
    T get(const std::string& column) const {
        auto value = getValue(column);
        if (std::holds_alternative<T>(value)) {
            return std::get<T>(value);
        }
        throw std::runtime_error("Type mismatch for column: " + column);
    }
    
    template<typename T>
    std::optional<T> getOptional(const std::string& column) const {
        auto value = getValue(column);
        if (std::holds_alternative<std::nullptr_t>(value)) {
            return std::nullopt;
        }
        if (std::holds_alternative<T>(value)) {
            return std::get<T>(value);
        }
        throw std::runtime_error("Type mismatch for column: " + column);
    }
    
    // Metadata
    size_t getRowCount() const { return rows_.size(); }
    size_t getColumnCount() const { return column_names_.size(); }
    const std::vector<std::string>& getColumnNames() const { return column_names_; }
    
    // Row access
    const DbRow& getCurrentRow() const;
    const std::vector<DbRow>& getAllRows() const { return rows_; }
    
    // Utility methods
    bool isEmpty() const { return rows_.empty(); }
    std::string toJson() const;
};

/**
 * @brief Prepared statement wrapper
 */
class PreparedStatement {
private:
    sqlite3_stmt* stmt_;
    std::string sql_;
    std::shared_ptr<utilities::Logger> logger_;
    
public:
    explicit PreparedStatement(sqlite3_stmt* stmt, const std::string& sql);
    ~PreparedStatement();
    
    // Non-copyable, movable
    PreparedStatement(const PreparedStatement&) = delete;
    PreparedStatement& operator=(const PreparedStatement&) = delete;
    PreparedStatement(PreparedStatement&& other) noexcept;
    PreparedStatement& operator=(PreparedStatement&& other) noexcept;
    
    // Parameter binding
    void bind(int index, std::nullptr_t);
    void bind(int index, int64_t value);
    void bind(int index, double value);
    void bind(int index, const std::string& value);
    void bind(int index, const std::vector<uint8_t>& value);
    void bind(int index, const DbValue& value);
    
    // Named parameter binding
    void bind(const std::string& name, const DbValue& value);
    
    // Execution
    ResultSet execute();
    int64_t executeUpdate(); // Returns affected rows
    
    // Utility
    void reset();
    void clearBindings();
    const std::string& getSql() const { return sql_; }
};

/**
 * @brief Database connection
 */
class Connection {
private:
    sqlite3* db_;
    std::string connection_string_;
    std::shared_ptr<utilities::Logger> logger_;
    std::chrono::steady_clock::time_point created_at_;
    std::chrono::steady_clock::time_point last_used_;
    bool in_transaction_;
    
public:
    explicit Connection(const std::string& connection_string);
    ~Connection();
    
    // Non-copyable, movable
    Connection(const Connection&) = delete;
    Connection& operator=(const Connection&) = delete;
    Connection(Connection&& other) noexcept;
    Connection& operator=(Connection&& other) noexcept;
    
    // Connection management
    void open();
    void close();
    bool isOpen() const;
    
    // Query execution
    ResultSet execute(const std::string& sql);
    int64_t executeUpdate(const std::string& sql);
    std::unique_ptr<PreparedStatement> prepare(const std::string& sql);
    
    // Transaction management
    void beginTransaction();
    void commit();
    void rollback();
    bool isInTransaction() const { return in_transaction_; }
    
    // Utility methods
    int64_t getLastInsertId();
    std::string escape(const std::string& value);
    
    // Metadata
    std::chrono::steady_clock::time_point getCreatedAt() const { return created_at_; }
    std::chrono::steady_clock::time_point getLastUsed() const { return last_used_; }
    void updateLastUsed() { last_used_ = std::chrono::steady_clock::now(); }
    
    // Raw access (use with caution)
    sqlite3* getRawConnection() { return db_; }
};

/**
 * @brief Connection pool for managing database connections
 */
class ConnectionPool {
private:
    std::string connection_string_;
    size_t max_connections_;
    size_t min_connections_;
    std::chrono::seconds max_idle_time_;
    std::chrono::seconds connection_timeout_;
    
    std::queue<std::unique_ptr<Connection>> available_connections_;
    std::unordered_map<Connection*, std::chrono::steady_clock::time_point> active_connections_;
    
    mutable std::mutex mutex_;
    std::condition_variable condition_;
    std::thread cleanup_thread_;
    std::atomic<bool> shutdown_;
    
    std::shared_ptr<utilities::Logger> logger_;
    
    // Statistics
    std::atomic<size_t> total_connections_created_{0};
    std::atomic<size_t> total_connections_destroyed_{0};
    std::atomic<size_t> total_connections_borrowed_{0};
    std::atomic<size_t> total_connections_returned_{0};
    
    void createConnection();
    void cleanupIdleConnections();
    void cleanupLoop();
    
public:
    explicit ConnectionPool(const std::string& connection_string,
                           size_t max_connections = 10,
                           size_t min_connections = 2,
                           std::chrono::seconds max_idle_time = std::chrono::seconds(300),
                           std::chrono::seconds connection_timeout = std::chrono::seconds(30));
    
    ~ConnectionPool();
    
    // Non-copyable, non-movable
    ConnectionPool(const ConnectionPool&) = delete;
    ConnectionPool& operator=(const ConnectionPool&) = delete;
    ConnectionPool(ConnectionPool&&) = delete;
    ConnectionPool& operator=(ConnectionPool&&) = delete;
    
    // Connection management
    std::unique_ptr<Connection> borrowConnection();
    void returnConnection(std::unique_ptr<Connection> connection);
    
    // Pool management
    void initialize();
    void shutdown();
    
    // Statistics
    size_t getActiveConnectionCount() const;
    size_t getAvailableConnectionCount() const;
    size_t getTotalConnectionCount() const;
    
    struct PoolStats {
        size_t active_connections;
        size_t available_connections;
        size_t total_connections;
        size_t total_created;
        size_t total_destroyed;
        size_t total_borrowed;
        size_t total_returned;
    };
    
    PoolStats getStats() const;
    std::string getStatsJson() const;
};

/**
 * @brief RAII connection wrapper
 */
class ConnectionGuard {
private:
    std::unique_ptr<Connection> connection_;
    ConnectionPool* pool_;
    
public:
    ConnectionGuard(std::unique_ptr<Connection> connection, ConnectionPool* pool)
        : connection_(std::move(connection)), pool_(pool) {}
    
    ~ConnectionGuard() {
        if (connection_ && pool_) {
            pool_->returnConnection(std::move(connection_));
        }
    }
    
    // Non-copyable, movable
    ConnectionGuard(const ConnectionGuard&) = delete;
    ConnectionGuard& operator=(const ConnectionGuard&) = delete;
    ConnectionGuard(ConnectionGuard&& other) noexcept
        : connection_(std::move(other.connection_)), pool_(other.pool_) {
        other.pool_ = nullptr;
    }
    ConnectionGuard& operator=(ConnectionGuard&& other) noexcept {
        if (this != &other) {
            if (connection_ && pool_) {
                pool_->returnConnection(std::move(connection_));
            }
            connection_ = std::move(other.connection_);
            pool_ = other.pool_;
            other.pool_ = nullptr;
        }
        return *this;
    }
    
    Connection& operator*() { return *connection_; }
    Connection* operator->() { return connection_.get(); }
    const Connection& operator*() const { return *connection_; }
    const Connection* operator->() const { return connection_.get(); }
    
    Connection* get() { return connection_.get(); }
    const Connection* get() const { return connection_.get(); }
    
    bool isValid() const { return connection_ != nullptr; }
};

/**
 * @brief Database manager service
 */
class DatabaseManager : public core::BaseService {
private:
    std::unique_ptr<ConnectionPool> connection_pool_;
    std::string connection_string_;
    size_t max_connections_;
    size_t min_connections_;
    
protected:
    void doInitialize() override;
    void doShutdown() override;
    
public:
    explicit DatabaseManager(const std::string& connection_string = "database.db",
                            size_t max_connections = 10,
                            size_t min_connections = 2);
    
    // Connection management
    ConnectionGuard getConnection();
    ConnectionPool& getConnectionPool() { return *connection_pool_; }
    const ConnectionPool& getConnectionPool() const { return *connection_pool_; }
    
    // Convenience methods
    ResultSet execute(const std::string& sql);
    int64_t executeUpdate(const std::string& sql);
    
    // Transaction helper
    template<typename Func>
    auto withTransaction(Func&& func) -> decltype(func(std::declval<Connection&>())) {
        auto conn = getConnection();
        conn->beginTransaction();
        
        try {
            auto result = func(*conn);
            conn->commit();
            return result;
        } catch (...) {
            conn->rollback();
            throw;
        }
    }
    
    // Configuration
    void setConnectionString(const std::string& connection_string);
    void setPoolSize(size_t max_connections, size_t min_connections);
    
    // Statistics
    std::string getStatsJson() const;
};

} // namespace database

#endif // DATABASE_DATABASE_H
