cmake_minimum_required(VERSION 3.16)
project(EnhancedWebServer VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra -Wpedantic")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -Wall")
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-O2 -g -DNDEBUG")

# Platform-specific settings
if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN -DNOMINMAX)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
elseif(UNIX)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pthread")
endif()

# Find required packages
find_package(Threads REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# Source files - only include files that exist
set(UTILITIES_SOURCES
    utilities/Logger.cpp
    utilities/Config.cpp
    utilities/ErrorHandler.cpp
)

set(CORE_SOURCES
    core/Application.cpp
    core/ThreadPool.cpp
)

set(SERVER_SOURCES
    server/HttpServer.cpp
    server/Router.cpp
    server/Middleware.cpp
    server/Request.cpp
    server/Response.cpp
    server/Utils.cpp
)

set(JS_ENGINE_SOURCES
    js_engine/Interpreter.cpp
    js_engine/Tokenizer.cpp
)

# Create libraries
add_library(utilities STATIC ${UTILITIES_SOURCES})
add_library(core STATIC ${CORE_SOURCES})
add_library(server STATIC ${SERVER_SOURCES})
add_library(js_engine STATIC ${JS_ENGINE_SOURCES})

# Library dependencies
target_link_libraries(core utilities)
target_link_libraries(server core utilities)
target_link_libraries(js_engine utilities)

# System libraries
target_link_libraries(utilities Threads::Threads)

# Main executable
add_executable(webserver main.cpp)
target_link_libraries(webserver
    server
    core
    utilities
    js_engine
)

# Test executable (without JS interpreter to avoid segfault)
add_executable(test_server test_server.cpp)
target_link_libraries(test_server
    server
    core
    utilities
)

# Configuration summary
message(STATUS "")
message(STATUS "Enhanced Web Server Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "")
message(STATUS "Build targets:")
message(STATUS "  webserver - Main application")
message(STATUS "")
