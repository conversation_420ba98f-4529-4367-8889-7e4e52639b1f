#ifndef UTILITIES_ERROR_HANDLER_H
#define UTILITIES_ERROR_HANDLER_H

#include <exception>
#include <string>
#include <memory>
#include <functional>
#include <chrono>
#include <vector>
#include <map>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <sstream>
#include <typeinfo>
#include "Logger.h"

namespace utilities {

/**
 * @brief Error severity levels
 */
enum class ErrorSeverity {
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2,
    CRITICAL = 3
};

/**
 * @brief Error categories for classification
 */
enum class ErrorCategory {
    VALIDATION,
    CONFIGURATION,
    NETWORK,
    PROCESSING,
    SYSTEM,
    SECURITY,
    BUSINESS_LOGIC,
    UNKNOWN
};

/**
 * @brief Error response strategies
 */
enum class ErrorStrategy {
    RETRY,
    FALLBACK,
    ABORT,
    IGNORE,
    ESCALATE
};

/**
 * @brief Error context information
 */
struct ErrorContext {
    std::chrono::system_clock::time_point timestamp;
    std::string module;
    std::string function;
    int line;
    std::thread::id thread_id;
    std::string stack_trace;
    std::map<std::string, std::string> environment_info;
    std::map<std::string, std::string> custom_data;
    
    ErrorContext() : timestamp(std::chrono::system_clock::now()),
                     line(0), thread_id(std::this_thread::get_id()) {}
};

/**
 * @brief Base custom exception class
 */
class BaseCustomException : public std::exception {
protected:
    std::string message_;
    ErrorSeverity severity_;
    ErrorCategory category_;
    ErrorContext context_;
    std::string error_code_;
    
public:
    BaseCustomException(const std::string& message, 
                       ErrorSeverity severity = ErrorSeverity::MEDIUM,
                       ErrorCategory category = ErrorCategory::UNKNOWN,
                       const std::string& error_code = "");
    
    virtual ~BaseCustomException() = default;
    
    const char* what() const noexcept override { return message_.c_str(); }
    
    // Getters
    ErrorSeverity getSeverity() const { return severity_; }
    ErrorCategory getCategory() const { return category_; }
    const ErrorContext& getContext() const { return context_; }
    const std::string& getErrorCode() const { return error_code_; }
    const std::string& getMessage() const { return message_; }
    
    // Context setters
    void setContext(const std::string& module, const std::string& function, int line);
    void addContextData(const std::string& key, const std::string& value);
    void addEnvironmentInfo(const std::string& key, const std::string& value);
    
    // Utility methods
    std::string toString() const;
    std::string toJson() const;
};

/**
 * @brief Validation error exception
 */
class ValidationError : public BaseCustomException {
public:
    ValidationError(const std::string& message, const std::string& error_code = "VALIDATION_ERROR")
        : BaseCustomException(message, ErrorSeverity::MEDIUM, ErrorCategory::VALIDATION, error_code) {}
};

/**
 * @brief Configuration error exception
 */
class ConfigurationError : public BaseCustomException {
public:
    ConfigurationError(const std::string& message, const std::string& error_code = "CONFIG_ERROR")
        : BaseCustomException(message, ErrorSeverity::HIGH, ErrorCategory::CONFIGURATION, error_code) {}
};

/**
 * @brief Network error exception
 */
class NetworkError : public BaseCustomException {
public:
    NetworkError(const std::string& message, const std::string& error_code = "NETWORK_ERROR")
        : BaseCustomException(message, ErrorSeverity::HIGH, ErrorCategory::NETWORK, error_code) {}
};

/**
 * @brief Processing error exception
 */
class ProcessingError : public BaseCustomException {
public:
    ProcessingError(const std::string& message, const std::string& error_code = "PROCESSING_ERROR")
        : BaseCustomException(message, ErrorSeverity::MEDIUM, ErrorCategory::PROCESSING, error_code) {}
};

/**
 * @brief System error exception
 */
class SystemError : public BaseCustomException {
public:
    SystemError(const std::string& message, const std::string& error_code = "SYSTEM_ERROR")
        : BaseCustomException(message, ErrorSeverity::CRITICAL, ErrorCategory::SYSTEM, error_code) {}
};

/**
 * @brief Security error exception
 */
class SecurityError : public BaseCustomException {
public:
    SecurityError(const std::string& message, const std::string& error_code = "SECURITY_ERROR")
        : BaseCustomException(message, ErrorSeverity::CRITICAL, ErrorCategory::SECURITY, error_code) {}
};

/**
 * @brief Business logic error exception
 */
class BusinessLogicError : public BaseCustomException {
public:
    BusinessLogicError(const std::string& message, const std::string& error_code = "BUSINESS_ERROR")
        : BaseCustomException(message, ErrorSeverity::MEDIUM, ErrorCategory::BUSINESS_LOGIC, error_code) {}
};

/**
 * @brief Retry configuration
 */
struct RetryConfig {
    int max_attempts = 3;
    std::chrono::milliseconds initial_delay{100};
    double backoff_multiplier = 2.0;
    std::chrono::milliseconds max_delay{5000};
    bool exponential_backoff = true;
    std::function<bool(const std::exception&)> should_retry = nullptr;
    
    RetryConfig() = default;
    RetryConfig(int attempts, std::chrono::milliseconds delay)
        : max_attempts(attempts), initial_delay(delay) {}
};

/**
 * @brief Error handler callback function type
 */
using ErrorCallback = std::function<void(const BaseCustomException&)>;

/**
 * @brief Fallback function type
 */
template<typename T>
using FallbackFunction = std::function<T()>;

/**
 * @brief Error notification interface
 */
class ErrorNotifier {
public:
    virtual ~ErrorNotifier() = default;
    virtual void notify(const BaseCustomException& error) = 0;
};

/**
 * @brief Email error notifier (placeholder implementation)
 */
class EmailNotifier : public ErrorNotifier {
private:
    std::string email_address_;
    
public:
    explicit EmailNotifier(const std::string& email) : email_address_(email) {}
    void notify(const BaseCustomException& error) override;
};

/**
 * @brief Log-based error notifier
 */
class LogNotifier : public ErrorNotifier {
private:
    std::shared_ptr<Logger> logger_;
    
public:
    explicit LogNotifier(std::shared_ptr<Logger> logger) : logger_(logger) {}
    void notify(const BaseCustomException& error) override;
};

/**
 * @brief Main Error Handler class
 */
class ErrorHandler {
private:
    std::shared_ptr<Logger> logger_;
    std::vector<std::unique_ptr<ErrorNotifier>> notifiers_;
    std::map<ErrorCategory, ErrorStrategy> category_strategies_;
    std::map<ErrorCategory, ErrorCallback> category_callbacks_;
    std::map<std::string, ErrorCallback> error_code_callbacks_;
    mutable std::mutex mutex_;
    
    // Statistics
    std::map<ErrorCategory, int> error_counts_;
    std::map<std::string, int> error_code_counts_;
    
public:
    explicit ErrorHandler(std::shared_ptr<Logger> logger = nullptr);
    
    // Configuration methods
    void setLogger(std::shared_ptr<Logger> logger) { logger_ = logger; }
    void addNotifier(std::unique_ptr<ErrorNotifier> notifier);
    void setStrategyForCategory(ErrorCategory category, ErrorStrategy strategy);
    void setCallbackForCategory(ErrorCategory category, ErrorCallback callback);
    void setCallbackForErrorCode(const std::string& error_code, ErrorCallback callback);
    
    // Error handling methods
    void handleError(const BaseCustomException& error);
    void handleError(const std::exception& error, const std::string& module = "", 
                     const std::string& function = "", int line = 0);
    
    // Retry mechanism
    template<typename Func, typename... Args>
    auto retry(Func&& func, const RetryConfig& config, Args&&... args) 
        -> decltype(func(args...));
    
    // Fallback mechanism
    template<typename T, typename Func, typename FallbackFunc>
    T withFallback(Func&& func, FallbackFunc&& fallback_func);
    
    // Statistics
    std::map<ErrorCategory, int> getErrorCounts() const;
    std::map<std::string, int> getErrorCodeCounts() const;
    void resetStatistics();
    
    // Utility methods
    static std::string getStackTrace();
    static std::map<std::string, std::string> getEnvironmentInfo();
    
private:
    void logError(const BaseCustomException& error);
    void notifyError(const BaseCustomException& error);
    void executeStrategy(const BaseCustomException& error, ErrorStrategy strategy);
    void executeCallback(const BaseCustomException& error);
    void updateStatistics(const BaseCustomException& error);
    
    std::string severityToString(ErrorSeverity severity) const;
    std::string categoryToString(ErrorCategory category) const;
    std::string strategyToString(ErrorStrategy strategy) const;
};

// Global error handler instance
ErrorHandler& getGlobalErrorHandler();

} // namespace utilities

// Convenience macros for error handling with automatic context
#define THROW_VALIDATION_ERROR(message) \
    do { \
        utilities::ValidationError e(message); \
        e.setContext(__FILE__, __FUNCTION__, __LINE__); \
        throw e; \
    } while(0)

#define THROW_CONFIG_ERROR(message) \
    do { \
        utilities::ConfigurationError e(message); \
        e.setContext(__FILE__, __FUNCTION__, __LINE__); \
        throw e; \
    } while(0)

#define THROW_NETWORK_ERROR(message) \
    do { \
        utilities::NetworkError e(message); \
        e.setContext(__FILE__, __FUNCTION__, __LINE__); \
        throw e; \
    } while(0)

#define THROW_PROCESSING_ERROR(message) \
    do { \
        utilities::ProcessingError e(message); \
        e.setContext(__FILE__, __FUNCTION__, __LINE__); \
        throw e; \
    } while(0)

#define THROW_SYSTEM_ERROR(message) \
    do { \
        utilities::SystemError e(message); \
        e.setContext(__FILE__, __FUNCTION__, __LINE__); \
        throw e; \
    } while(0)

#define THROW_SECURITY_ERROR(message) \
    do { \
        utilities::SecurityError e(message); \
        e.setContext(__FILE__, __FUNCTION__, __LINE__); \
        throw e; \
    } while(0)

#define THROW_BUSINESS_ERROR(message) \
    do { \
        utilities::BusinessLogicError e(message); \
        e.setContext(__FILE__, __FUNCTION__, __LINE__); \
        throw e; \
    } while(0)

#define HANDLE_ERROR(error) \
    utilities::getGlobalErrorHandler().handleError(error, __FILE__, __FUNCTION__, __LINE__)

#define TRY_WITH_ERROR_HANDLING(code) \
    try { \
        code \
    } catch (const utilities::BaseCustomException& e) { \
        HANDLE_ERROR(e); \
        throw; \
    } catch (const std::exception& e) { \
        HANDLE_ERROR(e); \
        throw; \
    }

#endif // UTILITIES_ERROR_HANDLER_H
