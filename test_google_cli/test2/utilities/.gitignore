# Build artifacts
*.o
*.a
*.so
*.dll
*.dylib
*.exe

# Executables
test_utilities
example_app

# Log files
*.log
logs/

# CMake build directory
build/
cmake-build-*/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS-specific files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Documentation output
docs/html/
docs/latex/

# Test output
test_output/
coverage/

# Package files
*.tar.gz
*.zip

# Configuration files (keep examples)
config.conf
!examples/config.conf
