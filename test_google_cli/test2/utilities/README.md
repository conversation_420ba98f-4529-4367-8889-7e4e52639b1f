# C++ Utilities Library

A comprehensive logging and error handling system for C++ applications using only the standard library.

## Features

### 🔍 Advanced Logging System
- **Color-coded console output** with customizable colors for different log levels
- **File logging with automatic rotation** to prevent disk space issues
- **Configurable log levels** for both console and file output
- **Filtering capabilities** by module, function, or custom criteria
- **Structured logging** with JSON format support
- **Thread-safe operations** for multi-threaded applications
- **Performance monitoring** with automatic timing

### 🛡️ Robust Error Handling
- **Custom exception classes** for different error types
- **Error context capture** including stack traces and environment info
- **Configurable error response strategies** (retry, fallback, abort, ignore, escalate)
- **Callback function support** for custom error handling logic
- **Error categorization and severity levels**
- **Integration with logger** for automatic error logging
- **Graceful degradation mechanisms**
- **Error reporting and notification capabilities**

### ⚙️ Configuration Management
- **Multiple configuration sources** (files, environment variables, command line)
- **Type-safe configuration access** with automatic type conversion
- **Configuration validation** with custom validators
- **Change notifications** for runtime configuration updates
- **Default value support** with fallback mechanisms
- **Section-based configuration** using dot notation
- **Auto-save functionality** for persistent configuration changes

### 🔗 Seamless Integration
- **Easy-to-use macros** for quick integration
- **Function wrappers** for automatic logging and error handling
- **Resource guards** for automatic cleanup with logging
- **Performance monitors** for operation timing
- **Configuration-based setup** for zero-code configuration

## Quick Start

### Basic Logging

```cpp
#include "utilities/Logger.h"
#include "utilities/Integration.h"

int main() {
    // Create a logger with console and file output
    auto logger = utilities::Logger::getLogger("MyApp");
    
    // Add console handler with colors
    auto console_handler = std::make_unique<utilities::ConsoleHandler>();
    console_handler->setLevel(utilities::LogLevel::INFO);
    logger->addHandler(std::move(console_handler));
    
    // Add file handler with rotation
    auto file_handler = std::make_unique<utilities::FileHandler>("logs/app.log");
    file_handler->setLevel(utilities::LogLevel::DEBUG);
    logger->addHandler(std::move(file_handler));
    
    // Use the logger
    LOG_INFO(logger, "Application started");
    LOG_WARNING(logger, "This is a warning");
    LOG_ERROR(logger, "This is an error");
    
    return 0;
}
```

### Error Handling

```cpp
#include "utilities/ErrorHandler.h"

void example_function() {
    try {
        // Some operation that might fail
        if (some_condition) {
            THROW_VALIDATION_ERROR("Invalid input data");
        }
        
        // Network operation
        if (network_failed) {
            THROW_NETWORK_ERROR("Connection timeout");
        }
        
    } catch (const utilities::BaseCustomException& e) {
        // Handle custom exceptions
        utilities::getGlobalErrorHandler().handleError(e);
        
        // The error handler will:
        // - Log the error with full context
        // - Execute the appropriate strategy (retry, fallback, etc.)
        // - Send notifications if configured
        // - Update error statistics
    }
}
```

### Configuration Management

```cpp
#include "utilities/Config.h"

int main(int argc, char* argv[]) {
    // Build configuration from multiple sources
    auto config = utilities::ConfigBuilder()
        .withFileSource("config.conf")
        .withEnvironmentSource("MYAPP_")
        .withCommandLineSource(argc, argv)
        .withAutoSave(true)
        .withDefault("server.port", 8080)
        .withDefault("server.host", std::string("localhost"))
        .build();
    
    // Load configuration
    config->loadFromSources();
    
    // Use configuration
    int port = config->get<int>("server.port");
    std::string host = config->get<std::string>("server.host");
    bool debug = config->get<bool>("debug", false); // with default
    
    // Set values
    config->set("last_startup", std::chrono::system_clock::now());
    
    return 0;
}
```

### Integrated Usage with Macros

```cpp
#include "utilities/Integration.h"

void process_data() {
    // Monitor performance automatically
    MONITOR_PERFORMANCE("process_data");
    
    // Guard resources with automatic cleanup
    FILE* file = fopen("data.txt", "r");
    GUARD_RESOURCE(file, [](FILE* f) { if (f) fclose(f); }, "data_file");
    
    // Retry operations with logging
    auto result = RETRY_WITH_LOGGING(
        []() { return risky_network_operation(); },
        utilities::RetryConfig{3, std::chrono::milliseconds(100)},
        "network_operation"
    );
}

int main() {
    // Setup logging and error handling from configuration
    SETUP_LOGGING_FROM_CONFIG();
    
    // Your application code here
    process_data();
    
    return 0;
}
```

## Configuration File Format

### Basic Key-Value Format (config.conf)
```
# Server configuration
server.host=localhost
server.port=8080
server.ssl=true

# Logging configuration
logging.console.enabled=true
logging.console.level=INFO
logging.console.colors=true
logging.file.enabled=true
logging.file.path=logs/app.log
logging.file.level=DEBUG
logging.file.max_size=10485760
logging.file.backup_count=5

# Error handling
error_handling.notifications.email.enabled=false
error_handling.notifications.log.enabled=true
```

### Environment Variables
```bash
export MYAPP_SERVER_HOST=production.example.com
export MYAPP_SERVER_PORT=443
export MYAPP_LOGGING_CONSOLE_LEVEL=WARNING
```

### Command Line Arguments
```bash
./myapp --server.port=9000 --debug=true --logging.console.level=DEBUG
```

## Advanced Features

### Custom Error Handlers

```cpp
// Custom error callback for specific error types
utilities::getGlobalErrorHandler().setCallbackForCategory(
    utilities::ErrorCategory::NETWORK,
    [](const utilities::BaseCustomException& error) {
        // Custom handling for network errors
        std::cout << "Network error occurred: " << error.getMessage() << std::endl;
        // Maybe trigger a reconnection attempt
    }
);

// Custom retry logic
utilities::RetryConfig retry_config;
retry_config.max_attempts = 5;
retry_config.initial_delay = std::chrono::milliseconds(100);
retry_config.exponential_backoff = true;
retry_config.should_retry = [](const std::exception& e) {
    // Only retry on network errors
    return dynamic_cast<const utilities::NetworkError*>(&e) != nullptr;
};
```

### Custom Log Filters

```cpp
// Filter logs by module
auto module_filter = std::make_unique<utilities::ModuleFilter>(
    std::vector<std::string>{"NetworkModule", "DatabaseModule"}
);
logger->addFilter(std::move(module_filter));

// Custom filter using lambda
auto custom_filter = std::make_unique<utilities::CustomFilter>(
    [](const utilities::LogEntry& entry) {
        // Only log errors and above during business hours
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        bool business_hours = (tm.tm_hour >= 9 && tm.tm_hour < 17);
        bool important = (entry.level >= utilities::LogLevel::ERROR);
        
        return !business_hours || important;
    }
);
logger->addFilter(std::move(custom_filter));
```

### Performance Monitoring

```cpp
void expensive_operation() {
    // Monitor with custom threshold
    MONITOR_PERFORMANCE_WITH_THRESHOLD("expensive_operation", 5000); // 5 second threshold
    
    // Your expensive code here
    std::this_thread::sleep_for(std::chrono::seconds(3));
    
    // Monitor will automatically log performance when function exits
}
```

## Thread Safety

All components are designed to be thread-safe:

- **Logger**: Uses mutexes to protect shared state during logging operations
- **ErrorHandler**: Thread-safe error handling and statistics tracking
- **ConfigManager**: Protected configuration access and modification
- **Integration utilities**: Safe for use in multi-threaded environments

## Building and Integration

### Requirements
- C++17 or later
- Standard library only (no external dependencies)
- CMake 3.10+ (recommended for building)

### CMake Integration

```cmake
# Add the utilities directory to your project
add_subdirectory(utilities)

# Link against the utilities library
target_link_libraries(your_target utilities)

# Include the headers
target_include_directories(your_target PRIVATE utilities)
```

### Manual Compilation

```bash
# Compile all source files
g++ -std=c++17 -I. utilities/*.cpp your_main.cpp -o your_app

# With debug symbols and warnings
g++ -std=c++17 -I. -g -Wall -Wextra utilities/*.cpp your_main.cpp -o your_app
```

## Best Practices

1. **Use the global instances** for simple applications
2. **Create separate loggers** for different modules in larger applications
3. **Configure logging levels** appropriately for production vs development
4. **Use structured logging (JSON)** for log analysis tools
5. **Set up error notifications** for critical production errors
6. **Monitor performance** of critical operations
7. **Use configuration files** for environment-specific settings
8. **Implement graceful degradation** using fallback mechanisms

## License

This library is provided as-is for educational and production use. Feel free to modify and distribute according to your needs.
