#ifndef UTILITIES_INTEGRATION_H
#define UTILITIES_INTEGRATION_H

#include "Logger.h"
#include "ErrorHandler.h"
#include "Config.h"
#include <functional>
#include <chrono>
#include <type_traits>

namespace utilities {

/**
 * @brief Function wrapper for automatic logging and error handling
 */
template<typename Func>
class FunctionWrapper {
private:
    Func func_;
    std::shared_ptr<Logger> logger_;
    std::string function_name_;
    bool log_entry_exit_;
    bool handle_exceptions_;
    
public:
    FunctionWrapper(Func func, 
                   std::shared_ptr<Logger> logger = nullptr,
                   const std::string& function_name = "unknown",
                   bool log_entry_exit = true,
                   bool handle_exceptions = true)
        : func_(func), logger_(logger), function_name_(function_name),
          log_entry_exit_(log_entry_exit), handle_exceptions_(handle_exceptions) {}
    
    template<typename... Args>
    auto operator()(Args&&... args) -> decltype(func_(std::forward<Args>(args)...)) {
        auto start_time = std::chrono::high_resolution_clock::now();
        
        if (log_entry_exit_ && logger_) {
            logger_->debug("Entering function: " + function_name_);
        }
        
        try {
            if constexpr (std::is_void_v<decltype(func_(std::forward<Args>(args)...))>) {
                func_(std::forward<Args>(args)...);
                
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
                
                if (log_entry_exit_ && logger_) {
                    logger_->debug("Exiting function: " + function_name_ + 
                                 " (duration: " + std::to_string(duration.count()) + "ms)");
                }
            } else {
                auto result = func_(std::forward<Args>(args)...);
                
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
                
                if (log_entry_exit_ && logger_) {
                    logger_->debug("Exiting function: " + function_name_ + 
                                 " (duration: " + std::to_string(duration.count()) + "ms)");
                }
                
                return result;
            }
        } catch (const BaseCustomException& e) {
            if (handle_exceptions_) {
                getGlobalErrorHandler().handleError(e);
            }
            if (logger_) {
                logger_->error("Exception in function " + function_name_ + ": " + e.getMessage());
            }
            throw;
        } catch (const std::exception& e) {
            if (handle_exceptions_) {
                getGlobalErrorHandler().handleError(e, __FILE__, function_name_, __LINE__);
            }
            if (logger_) {
                logger_->error("Exception in function " + function_name_ + ": " + e.what());
            }
            throw;
        }
    }
};

/**
 * @brief Performance monitor for function execution
 */
class PerformanceMonitor {
private:
    std::shared_ptr<Logger> logger_;
    std::string operation_name_;
    std::chrono::high_resolution_clock::time_point start_time_;
    std::chrono::milliseconds warning_threshold_;
    
public:
    PerformanceMonitor(const std::string& operation_name,
                      std::shared_ptr<Logger> logger = nullptr,
                      std::chrono::milliseconds warning_threshold = std::chrono::milliseconds(1000))
        : logger_(logger), operation_name_(operation_name), 
          start_time_(std::chrono::high_resolution_clock::now()),
          warning_threshold_(warning_threshold) {
        
        if (logger_) {
            logger_->debug("Starting performance monitoring for: " + operation_name_);
        }
    }
    
    ~PerformanceMonitor() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
        
        if (logger_) {
            std::string message = "Performance: " + operation_name_ + 
                                " completed in " + std::to_string(duration.count()) + "ms";
            
            if (duration >= warning_threshold_) {
                logger_->warning(message + " (exceeded threshold of " + 
                               std::to_string(warning_threshold_.count()) + "ms)");
            } else {
                logger_->info(message);
            }
        }
    }
    
    std::chrono::milliseconds getElapsed() const {
        auto current_time = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time_);
    }
};

/**
 * @brief Resource guard for automatic cleanup with logging
 */
template<typename Resource, typename Cleanup>
class ResourceGuard {
private:
    Resource resource_;
    Cleanup cleanup_;
    std::shared_ptr<Logger> logger_;
    std::string resource_name_;
    bool released_;
    
public:
    ResourceGuard(Resource resource, Cleanup cleanup, 
                 const std::string& resource_name = "resource",
                 std::shared_ptr<Logger> logger = nullptr)
        : resource_(resource), cleanup_(cleanup), logger_(logger), 
          resource_name_(resource_name), released_(false) {
        
        if (logger_) {
            logger_->debug("Acquired resource: " + resource_name_);
        }
    }
    
    ~ResourceGuard() {
        if (!released_) {
            release();
        }
    }
    
    // Non-copyable
    ResourceGuard(const ResourceGuard&) = delete;
    ResourceGuard& operator=(const ResourceGuard&) = delete;
    
    // Movable
    ResourceGuard(ResourceGuard&& other) noexcept
        : resource_(std::move(other.resource_)), cleanup_(std::move(other.cleanup_)),
          logger_(std::move(other.logger_)), resource_name_(std::move(other.resource_name_)),
          released_(other.released_) {
        other.released_ = true;
    }
    
    ResourceGuard& operator=(ResourceGuard&& other) noexcept {
        if (this != &other) {
            if (!released_) {
                release();
            }
            resource_ = std::move(other.resource_);
            cleanup_ = std::move(other.cleanup_);
            logger_ = std::move(other.logger_);
            resource_name_ = std::move(other.resource_name_);
            released_ = other.released_;
            other.released_ = true;
        }
        return *this;
    }
    
    Resource& get() { return resource_; }
    const Resource& get() const { return resource_; }
    
    Resource& operator*() { return resource_; }
    const Resource& operator*() const { return resource_; }
    
    Resource* operator->() { return &resource_; }
    const Resource* operator->() const { return &resource_; }
    
    void release() {
        if (!released_) {
            try {
                cleanup_(resource_);
                if (logger_) {
                    logger_->debug("Released resource: " + resource_name_);
                }
            } catch (const std::exception& e) {
                if (logger_) {
                    logger_->error("Error releasing resource " + resource_name_ + ": " + e.what());
                }
            }
            released_ = true;
        }
    }
};

/**
 * @brief Utility functions for creating wrappers and guards
 */
namespace utils {

// Function wrapper creation
template<typename Func>
auto wrapFunction(Func func, 
                 std::shared_ptr<Logger> logger = nullptr,
                 const std::string& function_name = "unknown",
                 bool log_entry_exit = true,
                 bool handle_exceptions = true) {
    return FunctionWrapper<Func>(func, logger, function_name, log_entry_exit, handle_exceptions);
}

// Resource guard creation
template<typename Resource, typename Cleanup>
auto makeResourceGuard(Resource resource, Cleanup cleanup, 
                      const std::string& resource_name = "resource",
                      std::shared_ptr<Logger> logger = nullptr) {
    return ResourceGuard<Resource, Cleanup>(resource, cleanup, resource_name, logger);
}

// Performance monitoring
inline auto monitorPerformance(const std::string& operation_name,
                              std::shared_ptr<Logger> logger = nullptr,
                              std::chrono::milliseconds warning_threshold = std::chrono::milliseconds(1000)) {
    return PerformanceMonitor(operation_name, logger, warning_threshold);
}

// Retry with logging
template<typename Func, typename... Args>
auto retryWithLogging(Func&& func, const RetryConfig& config, 
                     std::shared_ptr<Logger> logger = nullptr,
                     const std::string& operation_name = "operation",
                     Args&&... args) -> decltype(func(args...)) {
    
    if (logger) {
        logger->info("Starting retry operation: " + operation_name);
    }
    
    try {
        return getGlobalErrorHandler().retry(std::forward<Func>(func), config, std::forward<Args>(args)...);
    } catch (const std::exception& e) {
        if (logger) {
            logger->error("Retry operation failed: " + operation_name + " - " + e.what());
        }
        throw;
    }
}

// Configuration-based logger setup
inline std::shared_ptr<Logger> createLoggerFromConfig(const std::string& logger_name = "default") {
    auto& config = getGlobalConfig();
    auto logger = Logger::getLogger(logger_name);
    
    // Configure console handler
    bool console_enabled = config.get<bool>("logging.console.enabled", true);
    if (console_enabled) {
        auto console_handler = std::make_unique<ConsoleHandler>();
        
        std::string console_level_str = config.get<std::string>("logging.console.level", "INFO");
        LogLevel console_level = LogLevel::INFO;
        if (console_level_str == "DEBUG") console_level = LogLevel::DEBUG;
        else if (console_level_str == "WARNING") console_level = LogLevel::WARNING;
        else if (console_level_str == "ERROR") console_level = LogLevel::ERROR;
        else if (console_level_str == "CRITICAL") console_level = LogLevel::CRITICAL;
        
        console_handler->setLevel(console_level);
        
        bool use_colors = config.get<bool>("logging.console.colors", true);
        bool use_json = config.get<bool>("logging.console.json", false);
        
        if (use_json) {
            console_handler->setFormatter(std::make_unique<JsonFormatter>());
        } else {
            console_handler->setFormatter(std::make_unique<TextFormatter>(use_colors));
        }
        
        logger->addHandler(std::move(console_handler));
    }
    
    // Configure file handler
    bool file_enabled = config.get<bool>("logging.file.enabled", false);
    if (file_enabled) {
        std::string filename = config.get<std::string>("logging.file.path", "logs/application.log");
        size_t max_size = config.get<int>("logging.file.max_size", 10 * 1024 * 1024);
        int backup_count = config.get<int>("logging.file.backup_count", 5);
        
        auto file_handler = std::make_unique<FileHandler>(filename, max_size, backup_count);
        
        std::string file_level_str = config.get<std::string>("logging.file.level", "DEBUG");
        LogLevel file_level = LogLevel::DEBUG;
        if (file_level_str == "INFO") file_level = LogLevel::INFO;
        else if (file_level_str == "WARNING") file_level = LogLevel::WARNING;
        else if (file_level_str == "ERROR") file_level = LogLevel::ERROR;
        else if (file_level_str == "CRITICAL") file_level = LogLevel::CRITICAL;
        
        file_handler->setLevel(file_level);
        
        bool use_json = config.get<bool>("logging.file.json", false);
        if (use_json) {
            file_handler->setFormatter(std::make_unique<JsonFormatter>());
        } else {
            file_handler->setFormatter(std::make_unique<TextFormatter>(false));
        }
        
        logger->addHandler(std::move(file_handler));
    }
    
    return logger;
}

// Configuration-based error handler setup
inline void setupErrorHandlerFromConfig() {
    auto& config = getGlobalConfig();
    auto& error_handler = getGlobalErrorHandler();
    
    // Set up logger for error handler
    auto logger = createLoggerFromConfig("error_handler");
    error_handler.setLogger(logger);
    
    // Add notifiers
    bool email_notifications = config.get<bool>("error_handling.notifications.email.enabled", false);
    if (email_notifications) {
        std::string email = config.get<std::string>("error_handling.notifications.email.address", "");
        if (!email.empty()) {
            error_handler.addNotifier(std::make_unique<EmailNotifier>(email));
        }
    }
    
    bool log_notifications = config.get<bool>("error_handling.notifications.log.enabled", true);
    if (log_notifications) {
        error_handler.addNotifier(std::make_unique<LogNotifier>(logger));
    }
}

} // namespace utils

} // namespace utilities

// Convenience macros for integration
#define MONITOR_PERFORMANCE(name) \
    auto _perf_monitor = utilities::utils::monitorPerformance(name, utilities::getLogger())

#define MONITOR_PERFORMANCE_WITH_THRESHOLD(name, threshold_ms) \
    auto _perf_monitor = utilities::utils::monitorPerformance(name, utilities::getLogger(), \
                                                             std::chrono::milliseconds(threshold_ms))

#define WRAP_FUNCTION(func, name) \
    utilities::utils::wrapFunction(func, utilities::getLogger(), name)

#define GUARD_RESOURCE(resource, cleanup, name) \
    auto _resource_guard = utilities::utils::makeResourceGuard(resource, cleanup, name, utilities::getLogger())

#define RETRY_WITH_LOGGING(func, config, name, ...) \
    utilities::utils::retryWithLogging(func, config, utilities::getLogger(), name, __VA_ARGS__)

#define SETUP_LOGGING_FROM_CONFIG() \
    auto _logger = utilities::utils::createLoggerFromConfig(); \
    utilities::utils::setupErrorHandlerFromConfig()

#endif // UTILITIES_INTEGRATION_H
