#ifndef UTILITIES_LOGGER_H
#define UTILITIES_LOGGER_H

#include <iostream>
#include <fstream>
#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <vector>
#include <map>
#include <functional>
#include <thread>
#include <filesystem>

namespace utilities {

/**
 * @brief Log levels enumeration
 */
enum class LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3,
    CRITICAL = 4
};

/**
 * @brief ANSI color codes for console output
 */
namespace Colors {
    const std::string RESET = "\033[0m";
    const std::string DEBUG = "\033[36m";    // Cyan
    const std::string INFO = "\033[32m";     // Green
    const std::string WARNING = "\033[33m";  // Yellow
    const std::string ERROR = "\033[31m";    // Red
    const std::string CRITICAL = "\033[41m\033[37m\033[1m"; // Red background, white text, bold
}

/**
 * @brief Log entry structure for structured logging
 */
struct LogEntry {
    std::chrono::system_clock::time_point timestamp;
    LogLevel level;
    std::string logger_name;
    std::string module;
    std::string function;
    int line;
    std::string message;
    std::thread::id thread_id;
    std::map<std::string, std::string> extra_fields;
};

/**
 * @brief Log filter interface for custom filtering
 */
class LogFilter {
public:
    virtual ~LogFilter() = default;
    virtual bool shouldLog(const LogEntry& entry) const = 0;
};

/**
 * @brief Module-based log filter
 */
class ModuleFilter : public LogFilter {
private:
    std::vector<std::string> allowed_modules_;
    
public:
    explicit ModuleFilter(const std::vector<std::string>& modules) 
        : allowed_modules_(modules) {}
    
    bool shouldLog(const LogEntry& entry) const override {
        if (allowed_modules_.empty()) return true;
        for (const auto& module : allowed_modules_) {
            if (entry.module == module) return true;
        }
        return false;
    }
};

/**
 * @brief Function-based log filter
 */
class FunctionFilter : public LogFilter {
private:
    std::vector<std::string> allowed_functions_;
    
public:
    explicit FunctionFilter(const std::vector<std::string>& functions) 
        : allowed_functions_(functions) {}
    
    bool shouldLog(const LogEntry& entry) const override {
        if (allowed_functions_.empty()) return true;
        for (const auto& function : allowed_functions_) {
            if (entry.function == function) return true;
        }
        return false;
    }
};

/**
 * @brief Custom log filter using lambda functions
 */
class CustomFilter : public LogFilter {
private:
    std::function<bool(const LogEntry&)> filter_func_;
    
public:
    explicit CustomFilter(std::function<bool(const LogEntry&)> func) 
        : filter_func_(func) {}
    
    bool shouldLog(const LogEntry& entry) const override {
        return filter_func_(entry);
    }
};

/**
 * @brief Log formatter interface
 */
class LogFormatter {
public:
    virtual ~LogFormatter() = default;
    virtual std::string format(const LogEntry& entry) const = 0;
};

/**
 * @brief Standard text formatter
 */
class TextFormatter : public LogFormatter {
private:
    bool use_colors_;
    std::string timestamp_format_;
    
public:
    explicit TextFormatter(bool use_colors = true, 
                          const std::string& timestamp_format = "%Y-%m-%d %H:%M:%S")
        : use_colors_(use_colors), timestamp_format_(timestamp_format) {}
    
    std::string format(const LogEntry& entry) const override;
    
private:
    std::string levelToString(LogLevel level) const;
    std::string levelToColor(LogLevel level) const;
    std::string formatTimestamp(const std::chrono::system_clock::time_point& tp) const;
};

/**
 * @brief JSON formatter for structured logging
 */
class JsonFormatter : public LogFormatter {
public:
    std::string format(const LogEntry& entry) const override;
    
private:
    std::string escapeJson(const std::string& str) const;
    std::string formatTimestamp(const std::chrono::system_clock::time_point& tp) const;
};

/**
 * @brief Log handler interface
 */
class LogHandler {
public:
    virtual ~LogHandler() = default;
    virtual void handle(const LogEntry& entry) = 0;
    virtual void setLevel(LogLevel level) { min_level_ = level; }
    virtual LogLevel getLevel() const { return min_level_; }
    virtual void setFormatter(std::unique_ptr<LogFormatter> formatter) {
        formatter_ = std::move(formatter);
    }
    
protected:
    LogLevel min_level_ = LogLevel::DEBUG;
    std::unique_ptr<LogFormatter> formatter_;
    
    bool shouldHandle(LogLevel level) const {
        return static_cast<int>(level) >= static_cast<int>(min_level_);
    }
};

/**
 * @brief Console log handler
 */
class ConsoleHandler : public LogHandler {
private:
    std::ostream& output_stream_;
    mutable std::mutex mutex_;
    
public:
    explicit ConsoleHandler(std::ostream& stream = std::cout);
    void handle(const LogEntry& entry) override;
};

/**
 * @brief File log handler with rotation support
 */
class FileHandler : public LogHandler {
private:
    std::string filename_;
    std::ofstream file_stream_;
    size_t max_file_size_;
    int backup_count_;
    size_t current_size_;
    mutable std::mutex mutex_;
    
public:
    FileHandler(const std::string& filename, 
                size_t max_file_size = 10 * 1024 * 1024, // 10MB
                int backup_count = 5);
    
    ~FileHandler();
    void handle(const LogEntry& entry) override;
    
private:
    void rotateFile();
    void ensureDirectoryExists(const std::string& filepath);
};

/**
 * @brief Main Logger class
 */
class Logger {
private:
    std::string name_;
    std::vector<std::unique_ptr<LogHandler>> handlers_;
    std::vector<std::unique_ptr<LogFilter>> filters_;
    LogLevel min_level_;
    mutable std::mutex mutex_;
    
    // Singleton management
    static std::map<std::string, std::shared_ptr<Logger>> instances_;
    static std::mutex instances_mutex_;
    
public:
    explicit Logger(const std::string& name, LogLevel min_level = LogLevel::DEBUG);
    
    // Singleton access
    static std::shared_ptr<Logger> getLogger(const std::string& name = "default");
    
    // Handler management
    void addHandler(std::unique_ptr<LogHandler> handler);
    void removeAllHandlers();
    
    // Filter management
    void addFilter(std::unique_ptr<LogFilter> filter);
    void removeAllFilters();
    
    // Configuration
    void setLevel(LogLevel level) { min_level_ = level; }
    LogLevel getLevel() const { return min_level_; }
    const std::string& getName() const { return name_; }
    
    // Logging methods
    void log(LogLevel level, const std::string& message, 
             const std::string& module = "", const std::string& function = "", 
             int line = 0, const std::map<std::string, std::string>& extra = {});
    
    void debug(const std::string& message, const std::string& module = "", 
               const std::string& function = "", int line = 0);
    void info(const std::string& message, const std::string& module = "", 
              const std::string& function = "", int line = 0);
    void warning(const std::string& message, const std::string& module = "", 
                 const std::string& function = "", int line = 0);
    void error(const std::string& message, const std::string& module = "", 
               const std::string& function = "", int line = 0);
    void critical(const std::string& message, const std::string& module = "", 
                  const std::string& function = "", int line = 0);
    
private:
    bool shouldLog(LogLevel level) const;
    bool passesFilters(const LogEntry& entry) const;
};

// Convenience function for getting default logger
std::shared_ptr<Logger> getLogger(const std::string& name = "default");

} // namespace utilities

// Convenience macros for easy logging with automatic module, function, and line info
#define LOG_DEBUG(logger, message) \
    (logger)->debug((message), __FILE__, __FUNCTION__, __LINE__)

#define LOG_INFO(logger, message) \
    (logger)->info((message), __FILE__, __FUNCTION__, __LINE__)

#define LOG_WARNING(logger, message) \
    (logger)->warning((message), __FILE__, __FUNCTION__, __LINE__)

#define LOG_ERROR(logger, message) \
    (logger)->error((message), __FILE__, __FUNCTION__, __LINE__)

#define LOG_CRITICAL(logger, message) \
    (logger)->critical((message), __FILE__, __FUNCTION__, __LINE__)

#endif // UTILITIES_LOGGER_H
