/**
 * @file simple_tests.cpp
 * @brief Simple unit tests for the utilities library
 */

#include "../Logger.h"
#include "../ErrorHandler.h"
#include "../Config.h"
#include "../Integration.h"
#include <iostream>
#include <sstream>
#include <cassert>
#include <fstream>
#include <filesystem>

using namespace utilities;

// Simple test counter
int tests_passed = 0;
int tests_total = 0;

#define TEST(name) \
    void test_##name(); \
    void run_test_##name() { \
        tests_total++; \
        try { \
            test_##name(); \
            tests_passed++; \
            std::cout << "[PASS] " << #name << std::endl; \
        } catch (const std::exception& e) { \
            std::cout << "[FAIL] " << #name << " - " << e.what() << std::endl; \
        } catch (...) { \
            std::cout << "[FAIL] " << #name << " - Unknown exception" << std::endl; \
        } \
    } \
    void test_##name()

#define ASSERT(condition) \
    if (!(condition)) { \
        throw std::runtime_error("Assertion failed: " #condition); \
    }

#define ASSERT_EQ(expected, actual) \
    if ((expected) != (actual)) { \
        throw std::runtime_error("Assertion failed: expected " + std::to_string(expected) + \
                               ", got " + std::to_string(actual)); \
    }

#define ASSERT_STR_EQ(expected, actual) \
    if ((expected) != (actual)) { \
        throw std::runtime_error("Assertion failed: expected '" + expected + \
                               "', got '" + actual + "'"); \
    }

// Custom stream handler for testing
class TestStreamHandler : public LogHandler {
private:
    std::ostringstream& stream_;
    
public:
    explicit TestStreamHandler(std::ostringstream& stream) : stream_(stream) {
        formatter_ = std::make_unique<TextFormatter>(false);
    }
    
    void handle(const LogEntry& entry) override {
        if (!shouldHandle(entry.level)) return;
        stream_ << formatter_->format(entry) << std::endl;
    }
};

TEST(logger_creation) {
    auto logger = Logger::getLogger("test_logger");
    ASSERT(logger != nullptr);
    ASSERT_STR_EQ("test_logger", logger->getName());
}

TEST(log_levels) {
    auto logger = Logger::getLogger("level_test");
    logger->removeAllHandlers();
    
    std::ostringstream output;
    auto handler = std::make_unique<TestStreamHandler>(output);
    handler->setLevel(LogLevel::WARNING);
    logger->addHandler(std::move(handler));
    
    logger->debug("Debug message");
    logger->info("Info message");
    logger->warning("Warning message");
    logger->error("Error message");
    
    std::string result = output.str();
    
    ASSERT(result.find("Debug message") == std::string::npos);
    ASSERT(result.find("Info message") == std::string::npos);
    ASSERT(result.find("Warning message") != std::string::npos);
    ASSERT(result.find("Error message") != std::string::npos);
}

TEST(file_logging) {
    const std::string test_file = "test_log.txt";
    
    if (std::filesystem::exists(test_file)) {
        std::filesystem::remove(test_file);
    }
    
    auto logger = Logger::getLogger("file_test");
    logger->removeAllHandlers();
    
    auto file_handler = std::make_unique<FileHandler>(test_file, 1024, 2);
    logger->addHandler(std::move(file_handler));
    
    logger->info("Test message 1");
    logger->error("Test message 2");
    
    // Force flush and wait
    logger.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    ASSERT(std::filesystem::exists(test_file));
    
    std::ifstream file(test_file);
    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());
    
    ASSERT(content.find("Test message 1") != std::string::npos);
    ASSERT(content.find("Test message 2") != std::string::npos);
    
    std::filesystem::remove(test_file);
}

TEST(custom_exceptions) {
    try {
        THROW_VALIDATION_ERROR("Test validation error");
        ASSERT(false); // Should not reach here
    } catch (const ValidationError& e) {
        ASSERT_STR_EQ("Test validation error", e.getMessage());
        ASSERT(e.getCategory() == ErrorCategory::VALIDATION);
    }
    
    try {
        THROW_NETWORK_ERROR("Test network error");
        ASSERT(false); // Should not reach here
    } catch (const NetworkError& e) {
        ASSERT_STR_EQ("Test network error", e.getMessage());
        ASSERT(e.getCategory() == ErrorCategory::NETWORK);
    }
}

TEST(error_handler_basic) {
    auto& error_handler = getGlobalErrorHandler();
    
    // Reset error counts
    error_handler.clearErrorCounts();
    
    try {
        THROW_PROCESSING_ERROR("Test processing error");
    } catch (const BaseCustomException& e) {
        error_handler.handleError(e);
    }
    
    auto error_counts = error_handler.getErrorCounts();
    ASSERT(error_counts[ErrorCategory::PROCESSING] == 1);
}

TEST(config_basic) {
    ConfigManager config;
    
    // Test setting and getting values
    config.set("test.string", std::string("hello"));
    config.set("test.int", 42);
    config.set("test.bool", true);
    config.set("test.double", 3.14);
    
    ASSERT_STR_EQ("hello", config.get<std::string>("test.string"));
    ASSERT_EQ(42, config.get<int>("test.int"));
    ASSERT(config.get<bool>("test.bool") == true);
    ASSERT(config.get<double>("test.double") == 3.14);
}

TEST(config_defaults) {
    ConfigManager config;
    
    config.setDefault("default.value", std::string("default"));
    
    // Should return default when key doesn't exist
    ASSERT_STR_EQ("default", config.get<std::string>("default.value"));
    
    // Should return actual value when key exists
    config.set("default.value", std::string("actual"));
    ASSERT_STR_EQ("actual", config.get<std::string>("default.value"));
}

TEST(config_type_conversion) {
    ConfigManager config;
    
    config.set("number", std::string("123"));
    ASSERT_EQ(123, config.get<int>("number"));
    
    config.set("boolean", std::string("true"));
    ASSERT(config.get<bool>("boolean") == true);
    
    config.set("boolean2", std::string("false"));
    ASSERT(config.get<bool>("boolean2") == false);
}

TEST(performance_monitor) {
    std::ostringstream output;
    auto logger = Logger::getLogger("perf_test");
    logger->removeAllHandlers();
    
    auto handler = std::make_unique<TestStreamHandler>(output);
    logger->addHandler(std::move(handler));
    
    {
        PerformanceMonitor monitor("test_operation", logger, std::chrono::milliseconds(10));
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    
    std::string result = output.str();
    ASSERT(result.find("test_operation") != std::string::npos);
    ASSERT(result.find("exceeded threshold") != std::string::npos);
}

TEST(integration_macros) {
    // Test that macros compile and work
    MONITOR_PERFORMANCE("test_macro");
    
    auto wrapped_func = WRAP_FUNCTION([](int x) { return x * 2; }, "multiply_by_two");
    int result = wrapped_func(5);
    ASSERT_EQ(10, result);
}

void print_summary() {
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "Tests run: " << tests_total << std::endl;
    std::cout << "Tests passed: " << tests_passed << std::endl;
    std::cout << "Tests failed: " << (tests_total - tests_passed) << std::endl;
    std::cout << "Success rate: " << (tests_passed * 100.0 / tests_total) << "%" << std::endl;
}

int main() {
    std::cout << "=== Simple Utilities Tests ===" << std::endl;
    
    // Run all tests
    run_test_logger_creation();
    run_test_log_levels();
    run_test_file_logging();
    run_test_custom_exceptions();
    run_test_error_handler_basic();
    run_test_config_basic();
    run_test_config_defaults();
    run_test_config_type_conversion();
    run_test_performance_monitor();
    run_test_integration_macros();
    
    print_summary();
    
    return (tests_passed == tests_total) ? 0 : 1;
}
