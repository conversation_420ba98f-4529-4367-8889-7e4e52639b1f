/**
 * @file test_logger.cpp
 * @brief Unit tests for the Logger class
 */

#include "../Logger.h"
#include <cassert>
#include <iostream>
#include <sstream>
#include <fstream>
#include <filesystem>
#include <thread>
#include <vector>

using namespace utilities;

/**
 * @brief Simple test framework
 */
class TestFramework {
private:
    int tests_run_ = 0;
    int tests_passed_ = 0;
    std::string current_test_;
    
public:
    void run_test(const std::string& name, std::function<void()> test_func) {
        current_test_ = name;
        tests_run_++;
        
        try {
            test_func();
            tests_passed_++;
            std::cout << "[PASS] " << name << std::endl;
        } catch (const std::exception& e) {
            std::cout << "[FAIL] " << name << " - " << e.what() << std::endl;
        } catch (...) {
            std::cout << "[FAIL] " << name << " - Unknown exception" << std::endl;
        }
    }
    
    void assert_true(bool condition, const std::string& message = "") {
        if (!condition) {
            throw std::runtime_error("Assertion failed: " + message);
        }
    }
    
    void assert_equals(const std::string& expected, const std::string& actual, const std::string& message = "") {
        if (expected != actual) {
            throw std::runtime_error("Assertion failed: " + message + 
                                   " (expected: '" + expected + "', actual: '" + actual + "')");
        }
    }
    
    void print_summary() {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Tests run: " << tests_run_ << std::endl;
        std::cout << "Tests passed: " << tests_passed_ << std::endl;
        std::cout << "Tests failed: " << (tests_run_ - tests_passed_) << std::endl;
        std::cout << "Success rate: " << (tests_passed_ * 100.0 / tests_run_) << "%" << std::endl;
    }
    
    bool all_passed() const {
        return tests_passed_ == tests_run_;
    }
};

/**
 * @brief Custom stream handler for testing
 */
class TestStreamHandler : public LogHandler {
private:
    std::ostringstream& stream_;
    mutable std::mutex mutex_;
    
public:
    explicit TestStreamHandler(std::ostringstream& stream) : stream_(stream) {
        formatter_ = std::make_unique<TextFormatter>(false); // No colors for testing
    }
    
    void handle(const LogEntry& entry) override {
        if (!shouldHandle(entry.level)) return;
        
        std::lock_guard<std::mutex> lock(mutex_);
        stream_ << formatter_->format(entry) << std::endl;
    }
};

void test_logger_creation(TestFramework& tf) {
    auto logger = Logger::getLogger("test_logger");
    tf.assert_true(logger != nullptr, "Logger should not be null");
    tf.assert_equals("test_logger", logger->getName(), "Logger name should match");
}

void test_log_levels(TestFramework& tf) {
    auto logger = Logger::getLogger("level_test");
    logger->removeAllHandlers();
    
    std::ostringstream output;
    auto handler = std::make_unique<TestStreamHandler>(output);
    handler->setLevel(LogLevel::WARNING);
    logger->addHandler(std::move(handler));
    
    logger->debug("Debug message");
    logger->info("Info message");
    logger->warning("Warning message");
    logger->error("Error message");
    
    std::string result = output.str();
    
    // Should only contain warning and error messages
    tf.assert_true(result.find("Debug message") == std::string::npos, "Debug message should be filtered");
    tf.assert_true(result.find("Info message") == std::string::npos, "Info message should be filtered");
    tf.assert_true(result.find("Warning message") != std::string::npos, "Warning message should pass");
    tf.assert_true(result.find("Error message") != std::string::npos, "Error message should pass");
}

void test_file_logging(TestFramework& tf) {
    const std::string test_file = "test_log.txt";
    
    // Clean up any existing test file
    if (std::filesystem::exists(test_file)) {
        std::filesystem::remove(test_file);
    }
    
    auto logger = Logger::getLogger("file_test");
    logger->removeAllHandlers();
    
    auto file_handler = std::make_unique<FileHandler>(test_file, 1024, 2);
    logger->addHandler(std::move(file_handler));
    
    logger->info("Test message 1");
    logger->error("Test message 2");
    
    // Give some time for file operations
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Check if file was created and contains messages
    tf.assert_true(std::filesystem::exists(test_file), "Log file should be created");

    std::ifstream file(test_file);
    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());

    tf.assert_true(content.find("Test message 1") != std::string::npos, "File should contain first message");
    tf.assert_true(content.find("Test message 2") != std::string::npos, "File should contain second message");

    // Clean up
    std::filesystem::remove(test_file);
}

void test_log_filtering(TestFramework& tf) {
    auto logger = Logger::getLogger("filter_test");
    logger->removeAllHandlers();
    
    std::ostringstream output;
    auto handler = std::make_unique<TestStreamHandler>(output);
    logger->addHandler(std::move(handler));
    
    // Add module filter
    auto module_filter = std::make_unique<ModuleFilter>(std::vector<std::string>{"allowed_module"});
    logger->addFilter(std::move(module_filter));
    
    logger->info("Should be filtered", "blocked_module");
    logger->info("Should pass", "allowed_module");
    
    std::string result = output.str();
    
    assert(result.find("Should be filtered") == std::string::npos);
    assert(result.find("Should pass") != std::string::npos);
}

void test_json_formatter() {
    auto logger = Logger::getLogger("json_test");
    logger->removeAllHandlers();
    
    std::ostringstream output;
    auto handler = std::make_unique<TestStreamHandler>(output);
    handler->setFormatter(std::make_unique<JsonFormatter>());
    logger->addHandler(std::move(handler));
    
    logger->info("JSON test message", "test_module", "test_function", 42);
    
    std::string result = output.str();
    
    // Check for JSON structure
    assert(result.find("{") != std::string::npos);
    assert(result.find("}") != std::string::npos);
    assert(result.find("\"message\":\"JSON test message\"") != std::string::npos);
    assert(result.find("\"module\":\"test_module\"") != std::string::npos);
    assert(result.find("\"function\":\"test_function\"") != std::string::npos);
    assert(result.find("\"line\":42") != std::string::npos);
}

void test_thread_safety() {
    auto logger = Logger::getLogger("thread_test");
    logger->removeAllHandlers();
    
    std::ostringstream output;
    auto handler = std::make_unique<TestStreamHandler>(output);
    logger->addHandler(std::move(handler));
    
    const int num_threads = 10;
    const int messages_per_thread = 100;
    std::vector<std::thread> threads;
    
    // Launch multiple threads that log simultaneously
    for (int i = 0; i < num_threads; i++) {
        threads.emplace_back([&logger, i, messages_per_thread]() {
            for (int j = 0; j < messages_per_thread; j++) {
                logger->info("Thread " + std::to_string(i) + " message " + std::to_string(j));
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    std::string result = output.str();
    
    // Count the number of log lines
    int line_count = std::count(result.begin(), result.end(), '\n');
    assert(line_count == num_threads * messages_per_thread);
}

void test_custom_filter() {
    auto logger = Logger::getLogger("custom_filter_test");
    logger->removeAllHandlers();
    
    std::ostringstream output;
    auto handler = std::make_unique<TestStreamHandler>(output);
    logger->addHandler(std::move(handler));
    
    // Add custom filter that only allows messages containing "important"
    auto custom_filter = std::make_unique<CustomFilter>(
        [](const LogEntry& entry) {
            return entry.message.find("important") != std::string::npos;
        }
    );
    logger->addFilter(std::move(custom_filter));
    
    logger->info("This is not important");
    logger->info("This is important information");
    logger->error("Important error occurred");
    
    std::string result = output.str();
    
    assert(result.find("This is not important") == std::string::npos);
    assert(result.find("This is important information") != std::string::npos);
    assert(result.find("Important error occurred") != std::string::npos);
}

void test_singleton_behavior() {
    auto logger1 = Logger::getLogger("singleton_test");
    auto logger2 = Logger::getLogger("singleton_test");
    
    // Should be the same instance
    assert(logger1.get() == logger2.get());
    
    auto logger3 = Logger::getLogger("different_name");
    
    // Should be different instance
    assert(logger1.get() != logger3.get());
}

void test_macros() {
    auto logger = Logger::getLogger("macro_test");
    logger->removeAllHandlers();
    
    std::ostringstream output;
    auto handler = std::make_unique<TestStreamHandler>(output);
    logger->addHandler(std::move(handler));
    
    LOG_INFO(logger, "Macro test message");
    LOG_ERROR(logger, "Macro error message");
    
    std::string result = output.str();
    
    assert(result.find("Macro test message") != std::string::npos);
    assert(result.find("Macro error message") != std::string::npos);
    assert(result.find(__FILE__) != std::string::npos); // Should contain file name
}

int main() {
    std::cout << "=== Logger Unit Tests ===" << std::endl;

    TestFramework test_framework;

    test_framework.run_test("Logger Creation", [&]() { test_logger_creation(test_framework); });
    test_framework.run_test("Log Levels", [&]() { test_log_levels(test_framework); });
    test_framework.run_test("File Logging", [&]() { test_file_logging(test_framework); });
    test_framework.run_test("Log Filtering", [&]() { test_log_filtering(test_framework); });

    test_framework.print_summary();

    return test_framework.all_passed() ? 0 : 1;
}
