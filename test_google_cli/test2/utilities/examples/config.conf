# Example Configuration File for C++ Utilities Library
# This file demonstrates various configuration options

# Database Configuration
database.host=localhost
database.port=5432
database.name=example_db
database.username=user
database.password=secret
database.timeout=30
database.pool_size=10
database.ssl=true

# Logging Configuration
logging.console.enabled=true
logging.console.level=INFO
logging.console.colors=true
logging.console.json=false

logging.file.enabled=true
logging.file.path=logs/application.log
logging.file.level=DEBUG
logging.file.max_size=10485760
logging.file.backup_count=5
logging.file.json=false

# Error Handling Configuration
error_handling.notifications.email.enabled=false
error_handling.notifications.email.address=<EMAIL>
error_handling.notifications.log.enabled=true

error_handling.strategies.network=RETRY
error_handling.strategies.validation=ABORT
error_handling.strategies.processing=FALLBACK
error_handling.strategies.system=ESCALATE

# Retry Configuration
retry.max_attempts=3
retry.initial_delay=100
retry.backoff_multiplier=2.0
retry.max_delay=5000
retry.exponential_backoff=true

# Performance Monitoring
performance.monitoring.enabled=true
performance.monitoring.warning_threshold=1000
performance.monitoring.log_slow_operations=true

# Application-Specific Settings
app.name=Example Application
app.version=1.0.0
app.debug=false
app.max_connections=100
app.request_timeout=30000

# Feature Flags
features.user_registration=true
features.email_verification=true
features.two_factor_auth=false
features.advanced_logging=true

# Cache Configuration
cache.enabled=true
cache.ttl=3600
cache.max_size=1000
cache.cleanup_interval=300

# Security Settings
security.encryption.enabled=true
security.encryption.algorithm=AES256
security.session.timeout=1800
security.password.min_length=8
security.password.require_special_chars=true

# Network Configuration
network.timeout=5000
network.retries=3
network.keep_alive=true
network.compression=true

# Monitoring and Metrics
monitoring.enabled=true
monitoring.interval=60
monitoring.metrics.cpu=true
monitoring.metrics.memory=true
monitoring.metrics.disk=true
monitoring.metrics.network=true
