/**
 * @file example_app.cpp
 * @brief Comprehensive example demonstrating the utilities library
 * 
 * This example shows how to use all major features of the utilities library:
 * - Logging with different levels and handlers
 * - Error handling with custom exceptions and strategies
 * - Configuration management from multiple sources
 * - Integration utilities and performance monitoring
 */

#include "../Logger.h"
#include "../ErrorHandler.h"
#include "../Config.h"
#include "../Integration.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <random>

using namespace utilities;

/**
 * @brief Example class demonstrating integrated logging and error handling
 */
class DatabaseConnection {
private:
    std::shared_ptr<Logger> logger_;
    std::string connection_string_;
    bool connected_;
    std::mt19937 rng_;
    
public:
    DatabaseConnection(const std::string& connection_string) 
        : logger_(Logger::getLogger("DatabaseConnection")),
          connection_string_(connection_string),
          connected_(false),
          rng_(std::random_device{}()) {
        
        // Add console handler with colors
        auto console_handler = std::make_unique<ConsoleHandler>();
        console_handler->setLevel(LogLevel::INFO);
        logger_->addHandler(std::move(console_handler));
        
        LOG_INFO(logger_, "DatabaseConnection created for: " + connection_string_);
    }
    
    void connect() {
        MONITOR_PERFORMANCE("database_connect");
        
        LOG_INFO(logger_, "Attempting to connect to database...");
        
        // Simulate connection with potential failure
        std::uniform_int_distribution<int> dist(1, 10);
        if (dist(rng_) <= 3) { // 30% chance of failure
            THROW_NETWORK_ERROR("Failed to connect to database: Connection timeout");
        }
        
        // Simulate connection time
        std::this_thread::sleep_for(std::chrono::milliseconds(100 + dist(rng_) * 50));
        
        connected_ = true;
        LOG_INFO(logger_, "Successfully connected to database");
    }
    
    std::string executeQuery(const std::string& query) {
        if (!connected_) {
            THROW_SYSTEM_ERROR("Database not connected");
        }
        
        MONITOR_PERFORMANCE("database_query");
        
        LOG_DEBUG(logger_, "Executing query: " + query);
        
        // Simulate query execution
        std::uniform_int_distribution<int> dist(1, 20);
        int delay = dist(rng_);
        
        if (delay > 18) { // 10% chance of query failure
            THROW_PROCESSING_ERROR("Query execution failed: Syntax error");
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(delay * 10));
        
        std::string result = "Query result for: " + query;
        LOG_DEBUG(logger_, "Query completed successfully");
        
        return result;
    }
    
    void disconnect() {
        if (connected_) {
            LOG_INFO(logger_, "Disconnecting from database");
            connected_ = false;
        }
    }
    
    ~DatabaseConnection() {
        disconnect();
        LOG_INFO(logger_, "DatabaseConnection destroyed");
    }
};

/**
 * @brief Example service class with comprehensive error handling
 */
class UserService {
private:
    std::shared_ptr<Logger> logger_;
    std::unique_ptr<DatabaseConnection> db_;
    
public:
    UserService(std::unique_ptr<DatabaseConnection> db) 
        : logger_(Logger::getLogger("UserService")), db_(std::move(db)) {
        
        // Add file handler for service logs
        auto file_handler = std::make_unique<FileHandler>("logs/user_service.log");
        file_handler->setLevel(LogLevel::DEBUG);
        logger_->addHandler(std::move(file_handler));
        
        LOG_INFO(logger_, "UserService initialized");
    }
    
    std::string getUserById(int user_id) {
        LOG_INFO(logger_, "Fetching user with ID: " + std::to_string(user_id));
        
        // Validate input
        if (user_id <= 0) {
            THROW_VALIDATION_ERROR("Invalid user ID: " + std::to_string(user_id));
        }
        
        // Use retry mechanism for database operations
        RetryConfig retry_config;
        retry_config.max_attempts = 3;
        retry_config.initial_delay = std::chrono::milliseconds(100);
        retry_config.exponential_backoff = true;
        retry_config.should_retry = [](const std::exception& e) {
            // Retry on network errors, but not on validation errors
            return dynamic_cast<const NetworkError*>(&e) != nullptr;
        };
        
        try {
            return RETRY_WITH_LOGGING(
                [this, user_id]() {
                    return db_->executeQuery("SELECT * FROM users WHERE id = " + std::to_string(user_id));
                },
                retry_config,
                "get_user_query"
            );
        } catch (const BaseCustomException& e) {
            LOG_ERROR(logger_, "Failed to fetch user: " + e.getMessage());
            throw;
        }
    }
    
    void createUser(const std::string& username, const std::string& email) {
        LOG_INFO(logger_, "Creating user: " + username + " (" + email + ")");
        
        // Validate inputs
        if (username.empty()) {
            THROW_VALIDATION_ERROR("Username cannot be empty");
        }
        
        if (email.find('@') == std::string::npos) {
            THROW_VALIDATION_ERROR("Invalid email format: " + email);
        }
        
        // Use fallback mechanism
        try {
            auto result = getGlobalErrorHandler().withFallback<std::string>(
                [this, &username, &email]() {
                    return db_->executeQuery("INSERT INTO users (username, email) VALUES ('" + 
                                            username + "', '" + email + "')");
                },
                [this, &username]() {
                    LOG_WARNING(logger_, "Primary database failed, using cache for user: " + username);
                    return std::string("User cached locally: " + username);
                }
            );
            
            LOG_INFO(logger_, "User created successfully: " + result);
            
        } catch (const BaseCustomException& e) {
            LOG_ERROR(logger_, "Failed to create user: " + e.getMessage());
            throw;
        }
    }
};

/**
 * @brief Setup configuration from multiple sources
 */
void setupConfiguration(int argc, char* argv[]) {
    auto& config = getGlobalConfig();
    
    // Add configuration sources
    config.addSource(std::make_unique<FileConfigSource>("config.conf", true));
    config.addSource(std::make_unique<EnvironmentConfigSource>("EXAMPLE_"));
    config.addSource(std::make_unique<CommandLineConfigSource>(argc, argv));
    
    // Set defaults
    config.setDefault("database.host", std::string("localhost"));
    config.setDefault("database.port", 5432);
    config.setDefault("database.timeout", 30);
    config.setDefault("logging.level", std::string("INFO"));
    config.setDefault("retry.max_attempts", 3);
    config.setDefault("retry.initial_delay", 100);
    
    // Add validators
    config.addValidator("database.port", [](const std::string& key, const ConfigValue& value) {
        int port = ConfigManager::toInt(value);
        return port > 0 && port <= 65535;
    });
    
    config.addValidator("retry.max_attempts", [](const std::string& key, const ConfigValue& value) {
        int attempts = ConfigManager::toInt(value);
        return attempts > 0 && attempts <= 10;
    });
    
    // Add change callback
    config.addChangeCallback([](const std::string& key, const ConfigValue& old_val, const ConfigValue& new_val) {
        std::cout << "Configuration changed: " << key << " = " << ConfigManager::toString(new_val) << std::endl;
    });
    
    // Load configuration
    config.loadFromSources();
    
    std::cout << "Configuration loaded successfully!" << std::endl;
    std::cout << "Database host: " << config.get<std::string>("database.host") << std::endl;
    std::cout << "Database port: " << config.get<int>("database.port") << std::endl;
    std::cout << "Logging level: " << config.get<std::string>("logging.level") << std::endl;
}

/**
 * @brief Setup error handling with custom strategies and callbacks
 */
void setupErrorHandling() {
    auto& error_handler = getGlobalErrorHandler();
    auto logger = Logger::getLogger("ErrorHandler");
    
    // Set logger for error handler
    error_handler.setLogger(logger);
    
    // Add log notifier
    error_handler.addNotifier(std::make_unique<LogNotifier>(logger));
    
    // Set custom strategies for different error categories
    error_handler.setStrategyForCategory(ErrorCategory::NETWORK, ErrorStrategy::RETRY);
    error_handler.setStrategyForCategory(ErrorCategory::VALIDATION, ErrorStrategy::ABORT);
    error_handler.setStrategyForCategory(ErrorCategory::PROCESSING, ErrorStrategy::FALLBACK);
    
    // Add custom callback for network errors
    error_handler.setCallbackForCategory(ErrorCategory::NETWORK, 
        [](const BaseCustomException& error) {
            std::cout << "NETWORK ERROR CALLBACK: " << error.getMessage() << std::endl;
            // In a real application, this might trigger a reconnection attempt
        });
    
    // Add custom callback for critical errors
    error_handler.setCallbackForCategory(ErrorCategory::SYSTEM,
        [](const BaseCustomException& error) {
            std::cout << "CRITICAL SYSTEM ERROR: " << error.getMessage() << std::endl;
            // In a real application, this might send alerts to administrators
        });
    
    std::cout << "Error handling configured successfully!" << std::endl;
}

/**
 * @brief Demonstrate the utilities in action
 */
void runExample() {
    auto& config = getGlobalConfig();
    
    // Create database connection
    std::string db_host = config.get<std::string>("database.host");
    int db_port = config.get<int>("database.port");
    std::string connection_string = db_host + ":" + std::to_string(db_port);
    
    auto db = std::make_unique<DatabaseConnection>(connection_string);
    auto user_service = std::make_unique<UserService>(std::move(db));
    
    std::cout << "\n=== Running Example Operations ===" << std::endl;
    
    // Test operations with various scenarios
    for (int i = 1; i <= 5; i++) {
        try {
            std::cout << "\n--- Operation " << i << " ---" << std::endl;
            
            // Connect to database (may fail)
            user_service->getUserById(0)->connect(); // This will trigger validation error
            
        } catch (const ValidationError& e) {
            std::cout << "Validation Error: " << e.getMessage() << std::endl;
        } catch (const NetworkError& e) {
            std::cout << "Network Error: " << e.getMessage() << std::endl;
        } catch (const BaseCustomException& e) {
            std::cout << "Custom Error: " << e.getMessage() << std::endl;
        }
        
        try {
            // Try to get a user
            auto result = user_service->getUserById(i);
            std::cout << "Success: " << result << std::endl;
            
        } catch (const BaseCustomException& e) {
            std::cout << "Error getting user: " << e.getMessage() << std::endl;
        }
        
        try {
            // Try to create a user
            user_service->createUser("user" + std::to_string(i), "user" + std::to_string(i) + "@example.com");
            
        } catch (const BaseCustomException& e) {
            std::cout << "Error creating user: " << e.getMessage() << std::endl;
        }
        
        // Small delay between operations
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    // Display error statistics
    auto& error_handler = getGlobalErrorHandler();
    auto error_counts = error_handler.getErrorCounts();
    
    std::cout << "\n=== Error Statistics ===" << std::endl;
    for (const auto& [category, count] : error_counts) {
        std::cout << "Category " << static_cast<int>(category) << ": " << count << " errors" << std::endl;
    }
}

/**
 * @brief Main function demonstrating the complete utilities library
 */
int main(int argc, char* argv[]) {
    std::cout << "=== C++ Utilities Library Example ===" << std::endl;
    
    try {
        // Setup configuration
        std::cout << "\n1. Setting up configuration..." << std::endl;
        setupConfiguration(argc, argv);
        
        // Setup error handling
        std::cout << "\n2. Setting up error handling..." << std::endl;
        setupErrorHandling();
        
        // Run the example
        std::cout << "\n3. Running example operations..." << std::endl;
        runExample();
        
        std::cout << "\n=== Example completed successfully! ===" << std::endl;
        
    } catch (const BaseCustomException& e) {
        std::cerr << "Fatal error: " << e.toString() << std::endl;
        return 1;
    } catch (const std::exception& e) {
        std::cerr << "Unexpected error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
