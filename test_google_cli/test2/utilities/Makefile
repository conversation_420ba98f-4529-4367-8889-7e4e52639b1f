# Simple Makefile for building the utilities library
# For more advanced builds, use CMake

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -Wpedantic -O2
INCLUDES = -I.
LIBS = -lpthread

# Platform-specific settings
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
    LIBS += -ldl
endif
ifeq ($(UNAME_S),Darwin)
    LIBS += -ldl
endif
ifeq ($(OS),Windows_NT)
    LIBS += -ldbghelp
endif

# Source files
SOURCES = Logger.cpp ErrorHandler.cpp Config.cpp
OBJECTS = $(SOURCES:.cpp=.o)
LIBRARY = libutilities.a

# Test files
TEST_SOURCES = tests/simple_tests.cpp
TEST_OBJECTS = $(TEST_SOURCES:.cpp=.o)
TEST_EXECUTABLE = test_utilities

# Example files
EXAMPLE_SOURCES = examples/example_app.cpp
EXAMPLE_OBJECTS = $(EXAMPLE_SOURCES:.cpp=.o)
EXAMPLE_EXECUTABLE = example_app

# Default target
all: $(LIBRARY) $(TEST_EXECUTABLE) $(EXAMPLE_EXECUTABLE)

# Build the static library
$(LIBRARY): $(OBJECTS)
	ar rcs $@ $^
	@echo "Built static library: $@"

# Build object files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Build test executable
$(TEST_EXECUTABLE): $(TEST_OBJECTS) $(LIBRARY)
	$(CXX) $(CXXFLAGS) $(INCLUDES) $^ -o $@ $(LIBS)
	@echo "Built test executable: $@"

# Build example executable
$(EXAMPLE_EXECUTABLE): $(EXAMPLE_OBJECTS) $(LIBRARY)
	$(CXX) $(CXXFLAGS) $(INCLUDES) $^ -o $@ $(LIBS)
	@echo "Built example executable: $@"

# Run tests
test: $(TEST_EXECUTABLE)
	@echo "Running tests..."
	@mkdir -p logs
	./$(TEST_EXECUTABLE)

# Run example
run-example: $(EXAMPLE_EXECUTABLE)
	@echo "Running example..."
	@mkdir -p logs
	./$(EXAMPLE_EXECUTABLE)

# Clean build artifacts
clean:
	rm -f $(OBJECTS) $(TEST_OBJECTS) $(EXAMPLE_OBJECTS)
	rm -f $(LIBRARY) $(TEST_EXECUTABLE) $(EXAMPLE_EXECUTABLE)
	rm -f *.log logs/*.log
	@echo "Cleaned build artifacts"

# Install (simple version)
install: $(LIBRARY)
	@echo "Installing utilities library..."
	mkdir -p /usr/local/lib
	mkdir -p /usr/local/include/utilities
	cp $(LIBRARY) /usr/local/lib/
	cp *.h /usr/local/include/utilities/
	@echo "Installation complete"

# Uninstall
uninstall:
	@echo "Uninstalling utilities library..."
	rm -f /usr/local/lib/$(LIBRARY)
	rm -rf /usr/local/include/utilities
	@echo "Uninstallation complete"

# Debug build
debug: CXXFLAGS += -g -DDEBUG -O0
debug: clean all

# Release build
release: CXXFLAGS += -DNDEBUG -O3
release: clean all

# Format code (requires clang-format)
format:
	@if command -v clang-format >/dev/null 2>&1; then \
		echo "Formatting code..."; \
		clang-format -i *.h *.cpp tests/*.cpp examples/*.cpp; \
		echo "Code formatting complete"; \
	else \
		echo "clang-format not found, skipping formatting"; \
	fi

# Static analysis (requires cppcheck)
analyze:
	@if command -v cppcheck >/dev/null 2>&1; then \
		echo "Running static analysis..."; \
		cppcheck --enable=all --std=c++17 --suppress=missingIncludeSystem *.cpp; \
		echo "Static analysis complete"; \
	else \
		echo "cppcheck not found, skipping analysis"; \
	fi

# Generate documentation (requires doxygen)
docs:
	@if command -v doxygen >/dev/null 2>&1; then \
		echo "Generating documentation..."; \
		doxygen docs/Doxyfile; \
		echo "Documentation generated in docs/html/"; \
	else \
		echo "doxygen not found, skipping documentation generation"; \
	fi

# Help target
help:
	@echo "Available targets:"
	@echo "  all          - Build library, tests, and examples"
	@echo "  $(LIBRARY)   - Build static library only"
	@echo "  test         - Build and run tests"
	@echo "  run-example  - Build and run example application"
	@echo "  clean        - Remove build artifacts"
	@echo "  install      - Install library system-wide (requires sudo)"
	@echo "  uninstall    - Remove installed library (requires sudo)"
	@echo "  debug        - Build with debug symbols"
	@echo "  release      - Build optimized release version"
	@echo "  format       - Format code with clang-format"
	@echo "  analyze      - Run static analysis with cppcheck"
	@echo "  docs         - Generate documentation with doxygen"
	@echo "  help         - Show this help message"

.PHONY: all test run-example clean install uninstall debug release format analyze docs help
