cmake_minimum_required(VERSION 3.10)
project(UtilitiesLibrary VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-g -O0)
    else()
        add_compile_options(-O3 -DNDEBUG)
    endif()
endif()

# Find required packages
find_package(Threads REQUIRED)

# Create the utilities library
set(UTILITIES_SOURCES
    Logger.cpp
    ErrorHandler.cpp
    Config.cpp
)

set(UTILITIES_HEADERS
    Logger.h
    ErrorHandler.h
    Config.h
    Integration.h
)

# Create static library
add_library(utilities STATIC ${UTILITIES_SOURCES})

# Set include directories
target_include_directories(utilities
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<INSTALL_INTERFACE:include>
)

# Link required libraries
target_link_libraries(utilities
    PUBLIC
        Threads::Threads
)

# Platform-specific libraries
if(WIN32)
    target_link_libraries(utilities PRIVATE dbghelp)
endif()

# Set target properties
set_target_properties(utilities PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    PUBLIC_HEADER "${UTILITIES_HEADERS}"
)

# Create example executable
add_executable(example_app examples/example_app.cpp)
target_link_libraries(example_app utilities)

# Create logs directory for examples
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/logs)

# Copy configuration file to build directory
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/examples/config.conf
    ${CMAKE_CURRENT_BINARY_DIR}/config.conf
    COPYONLY
)

# Installation rules
include(GNUInstallDirs)

install(TARGETS utilities
    EXPORT UtilitiesTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/utilities
)

install(EXPORT UtilitiesTargets
    FILE UtilitiesTargets.cmake
    NAMESPACE Utilities::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Utilities
)

# Create config file
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/UtilitiesConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/UtilitiesConfig.cmake"
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Utilities
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/UtilitiesConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/UtilitiesConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/UtilitiesConfigVersion.cmake"
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Utilities
)

# Testing (optional)
option(BUILD_TESTS "Build unit tests" OFF)

if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Documentation (optional)
option(BUILD_DOCS "Build documentation" OFF)

if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    else()
        message(WARNING "Doxygen not found, documentation will not be built")
    endif()
endif()

# Print configuration summary
message(STATUS "")
message(STATUS "Configuration Summary:")
message(STATUS "  Project: ${PROJECT_NAME} ${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  Build Tests: ${BUILD_TESTS}")
message(STATUS "  Build Docs: ${BUILD_DOCS}")
message(STATUS "")
