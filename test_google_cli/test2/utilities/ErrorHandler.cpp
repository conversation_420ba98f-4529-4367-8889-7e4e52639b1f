#include "ErrorHandler.h"
#include <cstdlib>
#include <cstring>
#include <algorithm>
#include <random>

#ifdef _WIN32
#include <windows.h>
#include <dbghelp.h>
#else
#include <execinfo.h>
#include <cxxabi.h>
#include <unistd.h>
#endif

namespace utilities {

// BaseCustomException implementation
BaseCustomException::BaseCustomException(const std::string& message, 
                                       ErrorSeverity severity,
                                       ErrorCategory category,
                                       const std::string& error_code)
    : message_(message), severity_(severity), category_(category), error_code_(error_code) {
    
    // Capture stack trace
    context_.stack_trace = ErrorHandler::getStackTrace();
    
    // Capture environment info
    context_.environment_info = ErrorHandler::getEnvironmentInfo();
}

void BaseCustomException::setContext(const std::string& module, const std::string& function, int line) {
    context_.module = module;
    context_.function = function;
    context_.line = line;
}

void BaseCustomException::addContextData(const std::string& key, const std::string& value) {
    context_.custom_data[key] = value;
}

void BaseCustomException::addEnvironmentInfo(const std::string& key, const std::string& value) {
    context_.environment_info[key] = value;
}

std::string BaseCustomException::toString() const {
    std::ostringstream oss;
    oss << "Error: " << message_ << "\n";
    oss << "Code: " << error_code_ << "\n";
    oss << "Severity: " << static_cast<int>(severity_) << "\n";
    oss << "Category: " << static_cast<int>(category_) << "\n";
    
    if (!context_.module.empty()) {
        oss << "Module: " << context_.module << "\n";
    }
    
    if (!context_.function.empty()) {
        oss << "Function: " << context_.function;
        if (context_.line > 0) {
            oss << ":" << context_.line;
        }
        oss << "\n";
    }
    
    oss << "Thread ID: " << context_.thread_id << "\n";
    
    if (!context_.custom_data.empty()) {
        oss << "Custom Data:\n";
        for (const auto& [key, value] : context_.custom_data) {
            oss << "  " << key << ": " << value << "\n";
        }
    }
    
    if (!context_.stack_trace.empty()) {
        oss << "Stack Trace:\n" << context_.stack_trace << "\n";
    }
    
    return oss.str();
}

std::string BaseCustomException::toJson() const {
    std::ostringstream oss;
    oss << "{";
    oss << "\"message\":\"" << message_ << "\",";
    oss << "\"error_code\":\"" << error_code_ << "\",";
    oss << "\"severity\":" << static_cast<int>(severity_) << ",";
    oss << "\"category\":" << static_cast<int>(category_) << ",";
    oss << "\"module\":\"" << context_.module << "\",";
    oss << "\"function\":\"" << context_.function << "\",";
    oss << "\"line\":" << context_.line << ",";
    oss << "\"thread_id\":\"" << context_.thread_id << "\"";
    
    if (!context_.custom_data.empty()) {
        oss << ",\"custom_data\":{";
        bool first = true;
        for (const auto& [key, value] : context_.custom_data) {
            if (!first) oss << ",";
            oss << "\"" << key << "\":\"" << value << "\"";
            first = false;
        }
        oss << "}";
    }
    
    oss << "}";
    return oss.str();
}

// EmailNotifier implementation
void EmailNotifier::notify(const BaseCustomException& error) {
    // Placeholder implementation - in a real system, this would send an email
    std::cout << "EMAIL NOTIFICATION to " << email_address_ << ":\n";
    std::cout << error.toString() << std::endl;
}

// LogNotifier implementation
void LogNotifier::notify(const BaseCustomException& error) {
    if (logger_) {
        logger_->error("Error notification: " + error.getMessage(), 
                      error.getContext().module, 
                      error.getContext().function, 
                      error.getContext().line);
    }
}

// ErrorHandler implementation
ErrorHandler::ErrorHandler(std::shared_ptr<Logger> logger) : logger_(logger) {
    // Set default strategies
    category_strategies_[ErrorCategory::VALIDATION] = ErrorStrategy::ABORT;
    category_strategies_[ErrorCategory::CONFIGURATION] = ErrorStrategy::ABORT;
    category_strategies_[ErrorCategory::NETWORK] = ErrorStrategy::RETRY;
    category_strategies_[ErrorCategory::PROCESSING] = ErrorStrategy::RETRY;
    category_strategies_[ErrorCategory::SYSTEM] = ErrorStrategy::ESCALATE;
    category_strategies_[ErrorCategory::SECURITY] = ErrorStrategy::ABORT;
    category_strategies_[ErrorCategory::BUSINESS_LOGIC] = ErrorStrategy::FALLBACK;
    category_strategies_[ErrorCategory::UNKNOWN] = ErrorStrategy::ABORT;
}

void ErrorHandler::addNotifier(std::unique_ptr<ErrorNotifier> notifier) {
    std::lock_guard<std::mutex> lock(mutex_);
    notifiers_.push_back(std::move(notifier));
}

void ErrorHandler::setStrategyForCategory(ErrorCategory category, ErrorStrategy strategy) {
    std::lock_guard<std::mutex> lock(mutex_);
    category_strategies_[category] = strategy;
}

void ErrorHandler::setCallbackForCategory(ErrorCategory category, ErrorCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    category_callbacks_[category] = callback;
}

void ErrorHandler::setCallbackForErrorCode(const std::string& error_code, ErrorCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    error_code_callbacks_[error_code] = callback;
}

void ErrorHandler::handleError(const BaseCustomException& error) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Log the error
    logError(error);
    
    // Update statistics
    updateStatistics(error);
    
    // Send notifications
    notifyError(error);
    
    // Execute callbacks
    executeCallback(error);
    
    // Execute strategy
    auto strategy_it = category_strategies_.find(error.getCategory());
    if (strategy_it != category_strategies_.end()) {
        executeStrategy(error, strategy_it->second);
    }
}

void ErrorHandler::handleError(const std::exception& error, const std::string& module, 
                              const std::string& function, int line) {
    // Convert standard exception to custom exception
    BaseCustomException custom_error(error.what(), ErrorSeverity::MEDIUM, ErrorCategory::UNKNOWN);
    custom_error.setContext(module, function, line);
    
    handleError(custom_error);
}

template<typename Func, typename... Args>
auto ErrorHandler::retry(Func&& func, const RetryConfig& config, Args&&... args) 
    -> decltype(func(args...)) {
    
    int attempt = 0;
    std::chrono::milliseconds delay = config.initial_delay;
    
    while (attempt < config.max_attempts) {
        try {
            return func(args...);
        } catch (const std::exception& e) {
            attempt++;
            
            // Check if we should retry this exception
            if (config.should_retry && !config.should_retry(e)) {
                throw;
            }
            
            // If this was the last attempt, re-throw
            if (attempt >= config.max_attempts) {
                throw;
            }
            
            // Log retry attempt
            if (logger_) {
                logger_->warning("Retry attempt " + std::to_string(attempt) + 
                               " of " + std::to_string(config.max_attempts) + 
                               " for error: " + e.what());
            }
            
            // Wait before retrying
            std::this_thread::sleep_for(delay);
            
            // Calculate next delay
            if (config.exponential_backoff) {
                delay = std::chrono::milliseconds(
                    static_cast<long long>(delay.count() * config.backoff_multiplier));
                if (delay > config.max_delay) {
                    delay = config.max_delay;
                }
            }
        }
    }
    
    // This should never be reached, but just in case
    throw std::runtime_error("Retry mechanism failed unexpectedly");
}

template<typename T, typename Func, typename FallbackFunc>
T ErrorHandler::withFallback(Func&& func, FallbackFunc&& fallback_func) {
    try {
        return func();
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->warning("Primary function failed, using fallback: " + std::string(e.what()));
        }
        return fallback_func();
    }
}

std::map<ErrorCategory, int> ErrorHandler::getErrorCounts() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return error_counts_;
}

std::map<std::string, int> ErrorHandler::getErrorCodeCounts() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return error_code_counts_;
}

void ErrorHandler::resetStatistics() {
    std::lock_guard<std::mutex> lock(mutex_);
    error_counts_.clear();
    error_code_counts_.clear();
}

std::string ErrorHandler::getStackTrace() {
    std::string stack_trace;
    
#ifdef _WIN32
    // Windows implementation using SymFromAddr
    void* stack[100];
    unsigned short frames = CaptureStackBackTrace(0, 100, stack, NULL);
    
    HANDLE process = GetCurrentProcess();
    SymInitialize(process, NULL, TRUE);
    
    for (unsigned int i = 0; i < frames; i++) {
        DWORD64 address = (DWORD64)(stack[i]);
        
        char buffer[sizeof(SYMBOL_INFO) + MAX_SYM_NAME * sizeof(TCHAR)];
        PSYMBOL_INFO symbol = (PSYMBOL_INFO)buffer;
        symbol->SizeOfStruct = sizeof(SYMBOL_INFO);
        symbol->MaxNameLen = MAX_SYM_NAME;
        
        if (SymFromAddr(process, address, NULL, symbol)) {
            stack_trace += std::to_string(i) + ": " + symbol->Name + "\n";
        }
    }
    
    SymCleanup(process);
#else
    // Unix/Linux implementation using backtrace
    void* array[100];
    size_t size = backtrace(array, 100);
    char** strings = backtrace_symbols(array, size);
    
    if (strings != nullptr) {
        for (size_t i = 0; i < size; i++) {
            std::string line = strings[i];
            
            // Try to demangle C++ names
            size_t start = line.find('(');
            size_t end = line.find('+');
            if (start != std::string::npos && end != std::string::npos && start < end) {
                std::string mangled = line.substr(start + 1, end - start - 1);
                int status;
                char* demangled = abi::__cxa_demangle(mangled.c_str(), 0, 0, &status);
                if (status == 0 && demangled) {
                    line = line.substr(0, start + 1) + demangled + line.substr(end);
                    free(demangled);
                }
            }
            
            stack_trace += std::to_string(i) + ": " + line + "\n";
        }
        free(strings);
    }
#endif
    
    return stack_trace;
}

std::map<std::string, std::string> ErrorHandler::getEnvironmentInfo() {
    std::map<std::string, std::string> env_info;

    // Get process ID
    env_info["pid"] = std::to_string(getpid());

    // Get some environment variables
    const char* env_vars[] = {"PATH", "HOME", "USER", "HOSTNAME", nullptr};
    for (int i = 0; env_vars[i] != nullptr; i++) {
        const char* value = std::getenv(env_vars[i]);
        if (value) {
            env_info[env_vars[i]] = value;
        }
    }

    return env_info;
}

void ErrorHandler::logError(const BaseCustomException& error) {
    if (logger_) {
        std::string log_message = "Error occurred: " + error.getMessage() +
                                 " [Code: " + error.getErrorCode() +
                                 ", Severity: " + severityToString(error.getSeverity()) +
                                 ", Category: " + categoryToString(error.getCategory()) + "]";

        LogLevel log_level;
        switch (error.getSeverity()) {
            case ErrorSeverity::LOW: log_level = LogLevel::INFO; break;
            case ErrorSeverity::MEDIUM: log_level = LogLevel::WARNING; break;
            case ErrorSeverity::HIGH: log_level = LogLevel::ERROR; break;
            case ErrorSeverity::CRITICAL: log_level = LogLevel::CRITICAL; break;
            default: log_level = LogLevel::ERROR; break;
        }

        logger_->log(log_level, log_message,
                    error.getContext().module,
                    error.getContext().function,
                    error.getContext().line);
    }
}

void ErrorHandler::notifyError(const BaseCustomException& error) {
    for (auto& notifier : notifiers_) {
        try {
            notifier->notify(error);
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->error("Failed to send error notification: " + std::string(e.what()));
            }
        }
    }
}

void ErrorHandler::executeStrategy(const BaseCustomException& error, ErrorStrategy strategy) {
    if (logger_) {
        logger_->debug("Executing strategy: " + strategyToString(strategy) +
                      " for error: " + error.getMessage());
    }

    switch (strategy) {
        case ErrorStrategy::RETRY:
            // Strategy implementation would depend on context
            break;
        case ErrorStrategy::FALLBACK:
            // Strategy implementation would depend on context
            break;
        case ErrorStrategy::ABORT:
            if (logger_) {
                logger_->critical("Aborting due to error: " + error.getMessage());
            }
            std::abort();
            break;
        case ErrorStrategy::IGNORE:
            if (logger_) {
                logger_->info("Ignoring error: " + error.getMessage());
            }
            break;
        case ErrorStrategy::ESCALATE:
            if (logger_) {
                logger_->critical("Escalating error: " + error.getMessage());
            }
            // In a real system, this might send alerts to administrators
            break;
    }
}

void ErrorHandler::executeCallback(const BaseCustomException& error) {
    // Check for error code specific callback
    auto code_callback_it = error_code_callbacks_.find(error.getErrorCode());
    if (code_callback_it != error_code_callbacks_.end()) {
        try {
            code_callback_it->second(error);
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->error("Error in error code callback: " + std::string(e.what()));
            }
        }
    }

    // Check for category specific callback
    auto category_callback_it = category_callbacks_.find(error.getCategory());
    if (category_callback_it != category_callbacks_.end()) {
        try {
            category_callback_it->second(error);
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->error("Error in category callback: " + std::string(e.what()));
            }
        }
    }
}

void ErrorHandler::updateStatistics(const BaseCustomException& error) {
    error_counts_[error.getCategory()]++;
    error_code_counts_[error.getErrorCode()]++;
}

std::string ErrorHandler::severityToString(ErrorSeverity severity) const {
    switch (severity) {
        case ErrorSeverity::LOW: return "LOW";
        case ErrorSeverity::MEDIUM: return "MEDIUM";
        case ErrorSeverity::HIGH: return "HIGH";
        case ErrorSeverity::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

std::string ErrorHandler::categoryToString(ErrorCategory category) const {
    switch (category) {
        case ErrorCategory::VALIDATION: return "VALIDATION";
        case ErrorCategory::CONFIGURATION: return "CONFIGURATION";
        case ErrorCategory::NETWORK: return "NETWORK";
        case ErrorCategory::PROCESSING: return "PROCESSING";
        case ErrorCategory::SYSTEM: return "SYSTEM";
        case ErrorCategory::SECURITY: return "SECURITY";
        case ErrorCategory::BUSINESS_LOGIC: return "BUSINESS_LOGIC";
        case ErrorCategory::UNKNOWN: return "UNKNOWN";
        default: return "UNKNOWN";
    }
}

std::string ErrorHandler::strategyToString(ErrorStrategy strategy) const {
    switch (strategy) {
        case ErrorStrategy::RETRY: return "RETRY";
        case ErrorStrategy::FALLBACK: return "FALLBACK";
        case ErrorStrategy::ABORT: return "ABORT";
        case ErrorStrategy::IGNORE: return "IGNORE";
        case ErrorStrategy::ESCALATE: return "ESCALATE";
        default: return "UNKNOWN";
    }
}

// Global error handler instance
ErrorHandler& getGlobalErrorHandler() {
    static ErrorHandler instance;
    return instance;
}

} // namespace utilities
