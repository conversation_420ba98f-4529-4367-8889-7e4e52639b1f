#include "Config.h"
#include <algorithm>
#include <cctype>
#include <cstdlib>
#include <regex>
#include <unistd.h>

namespace utilities {

// FileConfigSource implementation
std::map<std::string, ConfigValue> FileConfigSource::load() {
    std::ifstream file(filename_);
    if (!file.is_open()) {
        THROW_CONFIG_ERROR("Cannot open config file: " + filename_);
    }
    
    std::map<std::string, ConfigValue> config;
    std::string line;
    int line_number = 0;
    
    while (std::getline(file, line)) {
        line_number++;
        line = trim(line);
        
        // Skip empty lines and comments
        if (line.empty() || line[0] == '#' || line[0] == ';') {
            continue;
        }
        
        // Find the '=' separator
        size_t pos = line.find('=');
        if (pos == std::string::npos) {
            THROW_CONFIG_ERROR("Invalid config line " + std::to_string(line_number) + 
                             " in file " + filename_ + ": " + line);
        }
        
        std::string key = trim(line.substr(0, pos));
        std::string value = trim(line.substr(pos + 1));
        
        if (key.empty()) {
            THROW_CONFIG_ERROR("Empty key on line " + std::to_string(line_number) + 
                             " in file " + filename_);
        }
        
        config[key] = parseValue(value);
    }
    
    return config;
}

void FileConfigSource::save(const std::map<std::string, ConfigValue>& config) {
    if (!writable_) {
        THROW_CONFIG_ERROR("Config source is not writable: " + filename_);
    }
    
    std::ofstream file(filename_);
    if (!file.is_open()) {
        THROW_CONFIG_ERROR("Cannot write to config file: " + filename_);
    }
    
    file << "# Configuration file generated automatically\n";
    file << "# " << std::chrono::system_clock::now().time_since_epoch().count() << "\n\n";
    
    for (const auto& [key, value] : config) {
        file << key << "=" << valueToString(value) << "\n";
    }
}

ConfigValue FileConfigSource::parseValue(const std::string& value) {
    if (value.empty()) {
        return std::string("");
    }
    
    // Try to parse as boolean
    std::string lower_value = value;
    std::transform(lower_value.begin(), lower_value.end(), lower_value.begin(), ::tolower);
    if (lower_value == "true" || lower_value == "yes" || lower_value == "on" || lower_value == "1") {
        return true;
    }
    if (lower_value == "false" || lower_value == "no" || lower_value == "off" || lower_value == "0") {
        return false;
    }
    
    // Try to parse as integer
    try {
        size_t pos;
        int int_value = std::stoi(value, &pos);
        if (pos == value.length()) {
            return int_value;
        }
    } catch (...) {}
    
    // Try to parse as double
    try {
        size_t pos;
        double double_value = std::stod(value, &pos);
        if (pos == value.length()) {
            return double_value;
        }
    } catch (...) {}
    
    // Default to string
    return value;
}

std::string FileConfigSource::valueToString(const ConfigValue& value) {
    return std::visit([](const auto& v) -> std::string {
        using T = std::decay_t<decltype(v)>;
        if constexpr (std::is_same_v<T, std::string>) {
            return v;
        } else if constexpr (std::is_same_v<T, bool>) {
            return v ? "true" : "false";
        } else {
            return std::to_string(v);
        }
    }, value);
}

std::string FileConfigSource::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

// EnvironmentConfigSource implementation
std::map<std::string, ConfigValue> EnvironmentConfigSource::load() {
    std::map<std::string, ConfigValue> config;
    
    // Get all environment variables
    extern char **environ;
    for (char **env = environ; *env != nullptr; env++) {
        std::string env_var(*env);
        size_t pos = env_var.find('=');
        if (pos != std::string::npos) {
            std::string name = env_var.substr(0, pos);
            std::string value = env_var.substr(pos + 1);
            
            // Check if it matches our prefix
            if (prefix_.empty() || name.substr(0, prefix_.length()) == prefix_) {
                std::string key = envVarToKey(name);
                config[key] = parseValue(value);
            }
        }
    }
    
    return config;
}

void EnvironmentConfigSource::save(const std::map<std::string, ConfigValue>& config) {
    for (const auto& [key, value] : config) {
        std::string env_var = keyToEnvVar(key);
        std::string value_str = ConfigManager::toString(value);
        
        if (setenv(env_var.c_str(), value_str.c_str(), 1) != 0) {
            THROW_CONFIG_ERROR("Failed to set environment variable: " + env_var);
        }
    }
}

std::string EnvironmentConfigSource::keyToEnvVar(const std::string& key) {
    std::string env_var = prefix_ + key;
    std::transform(env_var.begin(), env_var.end(), env_var.begin(), ::toupper);
    std::replace(env_var.begin(), env_var.end(), '.', '_');
    return env_var;
}

std::string EnvironmentConfigSource::envVarToKey(const std::string& env_var) {
    std::string key = env_var;
    if (!prefix_.empty() && key.substr(0, prefix_.length()) == prefix_) {
        key = key.substr(prefix_.length());
    }
    std::transform(key.begin(), key.end(), key.begin(), ::tolower);
    std::replace(key.begin(), key.end(), '_', '.');
    return key;
}

ConfigValue EnvironmentConfigSource::parseValue(const std::string& value) {
    // Same parsing logic as FileConfigSource
    if (value.empty()) {
        return std::string("");
    }
    
    std::string lower_value = value;
    std::transform(lower_value.begin(), lower_value.end(), lower_value.begin(), ::tolower);
    if (lower_value == "true" || lower_value == "yes" || lower_value == "on" || lower_value == "1") {
        return true;
    }
    if (lower_value == "false" || lower_value == "no" || lower_value == "off" || lower_value == "0") {
        return false;
    }
    
    try {
        size_t pos;
        int int_value = std::stoi(value, &pos);
        if (pos == value.length()) {
            return int_value;
        }
    } catch (...) {}
    
    try {
        size_t pos;
        double double_value = std::stod(value, &pos);
        if (pos == value.length()) {
            return double_value;
        }
    } catch (...) {}
    
    return value;
}

// CommandLineConfigSource implementation
std::map<std::string, ConfigValue> CommandLineConfigSource::load() {
    std::map<std::string, ConfigValue> config;
    
    for (size_t i = 0; i < args_.size(); i++) {
        const std::string& arg = args_[i];
        
        // Check if it starts with our prefix
        if (arg.substr(0, prefix_.length()) != prefix_) {
            continue;
        }
        
        std::string key_value = arg.substr(prefix_.length());
        
        // Check for key=value format
        size_t pos = key_value.find('=');
        if (pos != std::string::npos) {
            std::string key = key_value.substr(0, pos);
            std::string value = key_value.substr(pos + 1);
            config[key] = parseValue(value);
        } else {
            // Check if next argument is the value
            if (i + 1 < args_.size() && args_[i + 1].substr(0, prefix_.length()) != prefix_) {
                config[key_value] = parseValue(args_[i + 1]);
                i++; // Skip the value argument
            } else {
                // Boolean flag
                config[key_value] = true;
            }
        }
    }
    
    return config;
}

void CommandLineConfigSource::save(const std::map<std::string, ConfigValue>& config) {
    THROW_CONFIG_ERROR("Command line config source is not writable");
}

ConfigValue CommandLineConfigSource::parseValue(const std::string& value) {
    // Same parsing logic as other sources
    if (value.empty()) {
        return std::string("");
    }
    
    std::string lower_value = value;
    std::transform(lower_value.begin(), lower_value.end(), lower_value.begin(), ::tolower);
    if (lower_value == "true" || lower_value == "yes" || lower_value == "on" || lower_value == "1") {
        return true;
    }
    if (lower_value == "false" || lower_value == "no" || lower_value == "off" || lower_value == "0") {
        return false;
    }
    
    try {
        size_t pos;
        int int_value = std::stoi(value, &pos);
        if (pos == value.length()) {
            return int_value;
        }
    } catch (...) {}
    
    try {
        size_t pos;
        double double_value = std::stod(value, &pos);
        if (pos == value.length()) {
            return double_value;
        }
    } catch (...) {}
    
    return value;
}

// ConfigManager implementation
ConfigManager::ConfigManager(bool auto_save, std::shared_ptr<Logger> logger)
    : auto_save_(auto_save), logger_(logger) {
}

void ConfigManager::addSource(std::unique_ptr<ConfigSource> source) {
    std::lock_guard<std::mutex> lock(mutex_);
    sources_.push_back(std::move(source));
}

void ConfigManager::loadFromSources() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (auto& source : sources_) {
        try {
            auto source_config = source->load();
            for (const auto& [key, value] : source_config) {
                config_[key] = value;
            }
            
            if (logger_) {
                logger_->info("Loaded configuration from: " + source->getSourceName());
            }
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->warning("Failed to load from source " + source->getSourceName() + 
                               ": " + e.what());
            }
        }
    }
}

void ConfigManager::saveToWritableSources() {
    std::lock_guard<std::mutex> lock(mutex_);

    for (auto& source : sources_) {
        if (source->isWritable()) {
            try {
                source->save(config_);
                if (logger_) {
                    logger_->info("Saved configuration to: " + source->getSourceName());
                }
            } catch (const std::exception& e) {
                if (logger_) {
                    logger_->error("Failed to save to source " + source->getSourceName() +
                                 ": " + e.what());
                }
            }
        }
    }
}

bool ConfigManager::has(const std::string& key) const {
    std::lock_guard<std::mutex> lock(mutex_);
    return config_.find(key) != config_.end() || defaults_.find(key) != defaults_.end();
}

void ConfigManager::remove(const std::string& key) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = config_.find(key);
    if (it != config_.end()) {
        ConfigValue old_value = it->second;
        config_.erase(it);
        notifyChange(key, old_value, ConfigValue{});

        if (auto_save_) {
            try {
                saveToWritableSources();
            } catch (const std::exception& e) {
                if (logger_) {
                    logger_->warning("Failed to auto-save after removal: " + std::string(e.what()));
                }
            }
        }
    }
}

void ConfigManager::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    config_.clear();

    if (auto_save_) {
        try {
            saveToWritableSources();
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->warning("Failed to auto-save after clear: " + std::string(e.what()));
            }
        }
    }
}

void ConfigManager::removeDefault(const std::string& key) {
    std::lock_guard<std::mutex> lock(mutex_);
    defaults_.erase(key);
}

void ConfigManager::addValidator(const std::string& key, ConfigValidator validator) {
    std::lock_guard<std::mutex> lock(mutex_);
    validators_[key] = validator;
}

void ConfigManager::removeValidator(const std::string& key) {
    std::lock_guard<std::mutex> lock(mutex_);
    validators_.erase(key);
}

bool ConfigManager::validate() const {
    std::lock_guard<std::mutex> lock(mutex_);

    for (const auto& [key, value] : config_) {
        if (!validate(key, value)) {
            return false;
        }
    }
    return true;
}

bool ConfigManager::validate(const std::string& key, const ConfigValue& value) const {
    auto validator_it = validators_.find(key);
    if (validator_it != validators_.end()) {
        return validator_it->second(key, value);
    }
    return true; // No validator means valid
}

void ConfigManager::addChangeCallback(ConfigChangeCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    change_callbacks_.push_back(callback);
}

void ConfigManager::removeAllChangeCallbacks() {
    std::lock_guard<std::mutex> lock(mutex_);
    change_callbacks_.clear();
}

std::vector<std::string> ConfigManager::getKeys() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<std::string> keys;

    for (const auto& [key, value] : config_) {
        keys.push_back(key);
    }

    for (const auto& [key, value] : defaults_) {
        if (config_.find(key) == config_.end()) {
            keys.push_back(key);
        }
    }

    return keys;
}

std::map<std::string, ConfigValue> ConfigManager::getAll() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::map<std::string, ConfigValue> result = defaults_;

    // Override defaults with actual config values
    for (const auto& [key, value] : config_) {
        result[key] = value;
    }

    return result;
}

void ConfigManager::merge(const std::map<std::string, ConfigValue>& other) {
    std::lock_guard<std::mutex> lock(mutex_);

    for (const auto& [key, value] : other) {
        if (!validate(key, value)) {
            THROW_CONFIG_ERROR("Validation failed for merged config key '" + key + "'");
        }

        ConfigValue old_value;
        bool had_old_value = false;

        auto it = config_.find(key);
        if (it != config_.end()) {
            old_value = it->second;
            had_old_value = true;
        }

        config_[key] = value;

        if (had_old_value) {
            notifyChange(key, old_value, value);
        } else {
            notifyChange(key, ConfigValue{}, value);
        }
    }

    if (auto_save_) {
        try {
            saveToWritableSources();
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->warning("Failed to auto-save after merge: " + std::string(e.what()));
            }
        }
    }
}

std::map<std::string, ConfigValue> ConfigManager::getSection(const std::string& section_prefix) const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::map<std::string, ConfigValue> section;

    std::string prefix = section_prefix;
    if (!prefix.empty() && prefix.back() != '.') {
        prefix += '.';
    }

    for (const auto& [key, value] : config_) {
        if (key.substr(0, prefix.length()) == prefix) {
            std::string section_key = key.substr(prefix.length());
            section[section_key] = value;
        }
    }

    return section;
}

void ConfigManager::setSection(const std::string& section_prefix, const std::map<std::string, ConfigValue>& section) {
    std::string prefix = section_prefix;
    if (!prefix.empty() && prefix.back() != '.') {
        prefix += '.';
    }

    for (const auto& [key, value] : section) {
        set(prefix + key, value);
    }
}

// Static utility methods
std::string ConfigManager::toString(const ConfigValue& value) {
    return std::visit([](const auto& v) -> std::string {
        using T = std::decay_t<decltype(v)>;
        if constexpr (std::is_same_v<T, std::string>) {
            return v;
        } else if constexpr (std::is_same_v<T, bool>) {
            return v ? "true" : "false";
        } else {
            return std::to_string(v);
        }
    }, value);
}

int ConfigManager::toInt(const ConfigValue& value) {
    return std::visit([](const auto& v) -> int {
        using T = std::decay_t<decltype(v)>;
        if constexpr (std::is_same_v<T, int>) {
            return v;
        } else if constexpr (std::is_same_v<T, double>) {
            return static_cast<int>(v);
        } else if constexpr (std::is_same_v<T, bool>) {
            return v ? 1 : 0;
        } else if constexpr (std::is_same_v<T, std::string>) {
            try {
                return std::stoi(v);
            } catch (...) {
                THROW_CONFIG_ERROR("Cannot convert string '" + v + "' to int");
            }
        }
        return 0;
    }, value);
}

double ConfigManager::toDouble(const ConfigValue& value) {
    return std::visit([](const auto& v) -> double {
        using T = std::decay_t<decltype(v)>;
        if constexpr (std::is_same_v<T, double>) {
            return v;
        } else if constexpr (std::is_same_v<T, int>) {
            return static_cast<double>(v);
        } else if constexpr (std::is_same_v<T, bool>) {
            return v ? 1.0 : 0.0;
        } else if constexpr (std::is_same_v<T, std::string>) {
            try {
                return std::stod(v);
            } catch (...) {
                THROW_CONFIG_ERROR("Cannot convert string '" + v + "' to double");
            }
        }
        return 0.0;
    }, value);
}

bool ConfigManager::toBool(const ConfigValue& value) {
    return std::visit([](const auto& v) -> bool {
        using T = std::decay_t<decltype(v)>;
        if constexpr (std::is_same_v<T, bool>) {
            return v;
        } else if constexpr (std::is_same_v<T, int>) {
            return v != 0;
        } else if constexpr (std::is_same_v<T, double>) {
            return v != 0.0;
        } else if constexpr (std::is_same_v<T, std::string>) {
            std::string lower_v = v;
            std::transform(lower_v.begin(), lower_v.end(), lower_v.begin(), ::tolower);
            return lower_v == "true" || lower_v == "yes" || lower_v == "on" || lower_v == "1";
        }
        return false;
    }, value);
}

void ConfigManager::notifyChange(const std::string& key, const ConfigValue& old_value, const ConfigValue& new_value) {
    for (auto& callback : change_callbacks_) {
        try {
            callback(key, old_value, new_value);
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->error("Error in config change callback: " + std::string(e.what()));
            }
        }
    }
}

// Global config manager
ConfigManager& getGlobalConfig() {
    static ConfigManager instance;
    return instance;
}

// ConfigBuilder implementation
ConfigBuilder::ConfigBuilder() : config_manager_(std::make_unique<ConfigManager>()) {}

ConfigBuilder& ConfigBuilder::withFileSource(const std::string& filename, bool writable) {
    config_manager_->addSource(std::make_unique<FileConfigSource>(filename, writable));
    return *this;
}

ConfigBuilder& ConfigBuilder::withEnvironmentSource(const std::string& prefix) {
    config_manager_->addSource(std::make_unique<EnvironmentConfigSource>(prefix));
    return *this;
}

ConfigBuilder& ConfigBuilder::withCommandLineSource(int argc, char* argv[], const std::string& prefix) {
    config_manager_->addSource(std::make_unique<CommandLineConfigSource>(argc, argv, prefix));
    return *this;
}

ConfigBuilder& ConfigBuilder::withCommandLineSource(const std::vector<std::string>& args, const std::string& prefix) {
    config_manager_->addSource(std::make_unique<CommandLineConfigSource>(args, prefix));
    return *this;
}

ConfigBuilder& ConfigBuilder::withAutoSave(bool auto_save) {
    config_manager_ = std::make_unique<ConfigManager>(auto_save, config_manager_->getLogger());
    return *this;
}

ConfigBuilder& ConfigBuilder::withLogger(std::shared_ptr<Logger> logger) {
    config_manager_->setLogger(logger);
    return *this;
}

ConfigBuilder& ConfigBuilder::withValidator(const std::string& key, ConfigValidator validator) {
    config_manager_->addValidator(key, validator);
    return *this;
}

ConfigBuilder& ConfigBuilder::withChangeCallback(ConfigChangeCallback callback) {
    config_manager_->addChangeCallback(callback);
    return *this;
}

std::unique_ptr<ConfigManager> ConfigBuilder::build() {
    return std::move(config_manager_);
}

} // namespace utilities
