#ifndef UTILITIES_SERIALIZATION_H
#define UTILITIES_SERIALIZATION_H

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <sstream>
#include <fstream>
#include <variant>
#include <optional>
#include <type_traits>
#include <iomanip>
#include "Logger.h"

namespace utilities {

/**
 * @brief JSON value types for serialization
 */
using JsonValue = std::variant<
    std::nullptr_t,
    bool,
    int64_t,
    double,
    std::string,
    std::vector<JsonValue>,
    std::unordered_map<std::string, JsonValue>
>;

/**
 * @brief JSON serialization and deserialization
 */
class JsonSerializer {
private:
    static std::string escapeString(const std::string& str);
    static std::string unescapeString(const std::string& str);
    static void skipWhitespace(const std::string& json, size_t& pos);
    static JsonValue parseValue(const std::string& json, size_t& pos);
    static JsonValue parseObject(const std::string& json, size_t& pos);
    static JsonValue parseArray(const std::string& json, size_t& pos);
    static JsonValue parseString(const std::string& json, size_t& pos);
    static JsonValue parseNumber(const std::string& json, size_t& pos);
    static JsonValue parseLiteral(const std::string& json, size_t& pos);
    
public:
    // Serialization
    static std::string serialize(const JsonValue& value, bool pretty = false, int indent = 0);
    static std::string serializeObject(const std::unordered_map<std::string, JsonValue>& obj, bool pretty = false, int indent = 0);
    static std::string serializeArray(const std::vector<JsonValue>& arr, bool pretty = false, int indent = 0);
    
    // Deserialization
    static JsonValue deserialize(const std::string& json);
    static std::optional<JsonValue> tryDeserialize(const std::string& json);
    
    // Type checking
    static bool isNull(const JsonValue& value);
    static bool isBool(const JsonValue& value);
    static bool isNumber(const JsonValue& value);
    static bool isString(const JsonValue& value);
    static bool isArray(const JsonValue& value);
    static bool isObject(const JsonValue& value);
    
    // Type conversion
    static bool toBool(const JsonValue& value);
    static int64_t toInt(const JsonValue& value);
    static double toDouble(const JsonValue& value);
    static std::string toString(const JsonValue& value);
    static std::vector<JsonValue> toArray(const JsonValue& value);
    static std::unordered_map<std::string, JsonValue> toObject(const JsonValue& value);
    
    // Utility methods
    static JsonValue fromString(const std::string& str);
    static JsonValue fromInt(int64_t value);
    static JsonValue fromDouble(double value);
    static JsonValue fromBool(bool value);
    static JsonValue fromNull();
    
    // File operations
    static bool saveToFile(const JsonValue& value, const std::string& filename, bool pretty = true);
    static std::optional<JsonValue> loadFromFile(const std::string& filename);
};

/**
 * @brief Binary serialization for efficient storage
 */
class BinarySerializer {
private:
    enum class TypeTag : uint8_t {
        Null = 0,
        Bool = 1,
        Int8 = 2,
        Int16 = 3,
        Int32 = 4,
        Int64 = 5,
        Float = 6,
        Double = 7,
        String = 8,
        Vector = 9,
        Map = 10
    };
    
    static void writeTypeTag(std::vector<uint8_t>& buffer, TypeTag tag);
    static TypeTag readTypeTag(const std::vector<uint8_t>& buffer, size_t& pos);
    static void writeString(std::vector<uint8_t>& buffer, const std::string& str);
    static std::string readString(const std::vector<uint8_t>& buffer, size_t& pos);
    
public:
    // Basic type serialization
    static std::vector<uint8_t> serialize(bool value);
    static std::vector<uint8_t> serialize(int8_t value);
    static std::vector<uint8_t> serialize(int16_t value);
    static std::vector<uint8_t> serialize(int32_t value);
    static std::vector<uint8_t> serialize(int64_t value);
    static std::vector<uint8_t> serialize(float value);
    static std::vector<uint8_t> serialize(double value);
    static std::vector<uint8_t> serialize(const std::string& value);
    
    // Container serialization
    template<typename T>
    static std::vector<uint8_t> serialize(const std::vector<T>& vec) {
        std::vector<uint8_t> buffer;
        writeTypeTag(buffer, TypeTag::Vector);
        
        // Write size
        uint32_t size = static_cast<uint32_t>(vec.size());
        buffer.insert(buffer.end(), reinterpret_cast<const uint8_t*>(&size), 
                     reinterpret_cast<const uint8_t*>(&size) + sizeof(size));
        
        // Write elements
        for (const auto& item : vec) {
            auto item_data = serialize(item);
            buffer.insert(buffer.end(), item_data.begin(), item_data.end());
        }
        
        return buffer;
    }
    
    template<typename K, typename V>
    static std::vector<uint8_t> serialize(const std::unordered_map<K, V>& map) {
        std::vector<uint8_t> buffer;
        writeTypeTag(buffer, TypeTag::Map);
        
        // Write size
        uint32_t size = static_cast<uint32_t>(map.size());
        buffer.insert(buffer.end(), reinterpret_cast<const uint8_t*>(&size), 
                     reinterpret_cast<const uint8_t*>(&size) + sizeof(size));
        
        // Write key-value pairs
        for (const auto& [key, value] : map) {
            auto key_data = serialize(key);
            auto value_data = serialize(value);
            buffer.insert(buffer.end(), key_data.begin(), key_data.end());
            buffer.insert(buffer.end(), value_data.begin(), value_data.end());
        }
        
        return buffer;
    }
    
    // Deserialization
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, bool& value);
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, int8_t& value);
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, int16_t& value);
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, int32_t& value);
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, int64_t& value);
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, float& value);
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, double& value);
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, std::string& value);
    
    template<typename T>
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, std::vector<T>& vec) {
        TypeTag tag = readTypeTag(buffer, pos);
        if (tag != TypeTag::Vector) return false;
        
        // Read size
        if (pos + sizeof(uint32_t) > buffer.size()) return false;
        uint32_t size;
        std::memcpy(&size, buffer.data() + pos, sizeof(size));
        pos += sizeof(size);
        
        // Read elements
        vec.clear();
        vec.reserve(size);
        for (uint32_t i = 0; i < size; ++i) {
            T item;
            if (!deserialize(buffer, pos, item)) return false;
            vec.push_back(std::move(item));
        }
        
        return true;
    }
    
    template<typename K, typename V>
    static bool deserialize(const std::vector<uint8_t>& buffer, size_t& pos, std::unordered_map<K, V>& map) {
        TypeTag tag = readTypeTag(buffer, pos);
        if (tag != TypeTag::Map) return false;
        
        // Read size
        if (pos + sizeof(uint32_t) > buffer.size()) return false;
        uint32_t size;
        std::memcpy(&size, buffer.data() + pos, sizeof(size));
        pos += sizeof(size);
        
        // Read key-value pairs
        map.clear();
        for (uint32_t i = 0; i < size; ++i) {
            K key;
            V value;
            if (!deserialize(buffer, pos, key) || !deserialize(buffer, pos, value)) {
                return false;
            }
            map[std::move(key)] = std::move(value);
        }
        
        return true;
    }
    
    // File operations
    static bool saveToFile(const std::vector<uint8_t>& data, const std::string& filename);
    static std::optional<std::vector<uint8_t>> loadFromFile(const std::string& filename);
};

/**
 * @brief XML serialization support
 */
class XmlSerializer {
private:
    struct XmlNode {
        std::string name;
        std::unordered_map<std::string, std::string> attributes;
        std::string text;
        std::vector<std::unique_ptr<XmlNode>> children;
        
        std::string toString(int indent = 0) const;
    };
    
    static std::unique_ptr<XmlNode> parseNode(const std::string& xml, size_t& pos);
    static void skipWhitespace(const std::string& xml, size_t& pos);
    static std::string parseTagName(const std::string& xml, size_t& pos);
    static std::unordered_map<std::string, std::string> parseAttributes(const std::string& xml, size_t& pos);
    static std::string parseText(const std::string& xml, size_t& pos);
    static std::string escapeXml(const std::string& str);
    static std::string unescapeXml(const std::string& str);
    
public:
    // Convert JSON to XML
    static std::string jsonToXml(const JsonValue& json, const std::string& root_name = "root");
    
    // Convert XML to JSON
    static JsonValue xmlToJson(const std::string& xml);
    
    // Parse XML to internal representation
    static std::unique_ptr<XmlNode> parseXml(const std::string& xml);
    
    // File operations
    static bool saveToFile(const std::string& xml, const std::string& filename);
    static std::optional<std::string> loadFromFile(const std::string& filename);
};

/**
 * @brief Configuration file serialization
 */
class ConfigSerializer {
public:
    // INI format
    static std::unordered_map<std::string, std::unordered_map<std::string, std::string>> 
           parseIni(const std::string& content);
    static std::string serializeIni(const std::unordered_map<std::string, std::unordered_map<std::string, std::string>>& config);
    
    // YAML-like format (simplified)
    static JsonValue parseYaml(const std::string& content);
    static std::string serializeYaml(const JsonValue& value, int indent = 0);
    
    // TOML-like format (simplified)
    static JsonValue parseToml(const std::string& content);
    static std::string serializeToml(const JsonValue& value);
    
    // File operations
    static bool saveIniToFile(const std::unordered_map<std::string, std::unordered_map<std::string, std::string>>& config, 
                             const std::string& filename);
    static std::optional<std::unordered_map<std::string, std::unordered_map<std::string, std::string>>> 
           loadIniFromFile(const std::string& filename);
    
    static bool saveYamlToFile(const JsonValue& value, const std::string& filename);
    static std::optional<JsonValue> loadYamlFromFile(const std::string& filename);
    
    static bool saveTomlToFile(const JsonValue& value, const std::string& filename);
    static std::optional<JsonValue> loadTomlFromFile(const std::string& filename);
};

/**
 * @brief Serializable interface for custom types
 */
class ISerializable {
public:
    virtual ~ISerializable() = default;
    virtual JsonValue toJson() const = 0;
    virtual bool fromJson(const JsonValue& json) = 0;
    virtual std::vector<uint8_t> toBinary() const = 0;
    virtual bool fromBinary(const std::vector<uint8_t>& data) = 0;
};

/**
 * @brief Serialization utilities
 */
class SerializationUtils {
public:
    // Compression support
    static std::vector<uint8_t> compress(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> decompress(const std::vector<uint8_t>& compressed_data);
    
    // Base64 encoding for text-safe binary data
    static std::string base64Encode(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> base64Decode(const std::string& encoded);
    
    // Hex encoding
    static std::string hexEncode(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> hexDecode(const std::string& hex);
    
    // Hash calculation for data integrity
    static std::string calculateHash(const std::vector<uint8_t>& data);
    static bool verifyHash(const std::vector<uint8_t>& data, const std::string& expected_hash);
    
    // File format detection
    enum class FileFormat {
        Unknown,
        Json,
        Xml,
        Binary,
        Ini,
        Yaml,
        Toml
    };
    
    static FileFormat detectFormat(const std::string& filename);
    static FileFormat detectFormatFromContent(const std::string& content);
    
    // Generic serialization based on file extension
    static bool saveToFile(const JsonValue& value, const std::string& filename);
    static std::optional<JsonValue> loadFromFile(const std::string& filename);
    
    // Batch operations
    static bool saveMultipleToDirectory(const std::unordered_map<std::string, JsonValue>& data, 
                                       const std::string& directory, FileFormat format = FileFormat::Json);
    static std::unordered_map<std::string, JsonValue> loadMultipleFromDirectory(const std::string& directory);
    
    // Schema validation (basic)
    struct JsonSchema {
        std::unordered_map<std::string, std::string> required_fields; // field_name -> expected_type
        std::unordered_map<std::string, std::string> optional_fields;
    };
    
    static bool validateJson(const JsonValue& value, const JsonSchema& schema);
    static std::vector<std::string> getValidationErrors(const JsonValue& value, const JsonSchema& schema);
};

} // namespace utilities

#endif // UTILITIES_SERIALIZATION_H
