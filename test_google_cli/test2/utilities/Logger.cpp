#include "Logger.h"
#include <ctime>
#include <algorithm>

namespace utilities {

// Static member definitions
std::map<std::string, std::shared_ptr<Logger>> Logger::instances_;
std::mutex Logger::instances_mutex_;

// TextFormatter implementation
std::string TextFormatter::format(const LogEntry& entry) const {
    std::ostringstream oss;
    
    // Timestamp
    oss << formatTimestamp(entry.timestamp) << " | ";
    
    // Level with color
    std::string level_str = levelToString(entry.level);
    if (use_colors_) {
        oss << levelToColor(entry.level) << std::setw(8) << std::left << level_str << Colors::RESET;
    } else {
        oss << std::setw(8) << std::left << level_str;
    }
    
    oss << " | " << entry.logger_name << " | ";
    
    if (!entry.module.empty()) {
        oss << entry.module << " | ";
    }
    
    if (!entry.function.empty()) {
        oss << entry.function;
        if (entry.line > 0) {
            oss << ":" << entry.line;
        }
        oss << " | ";
    }
    
    oss << entry.message;
    
    // Add extra fields
    for (const auto& [key, value] : entry.extra_fields) {
        oss << " | " << key << "=" << value;
    }
    
    return oss.str();
}

std::string TextFormatter::levelToString(LogLevel level) const {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARNING";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

std::string TextFormatter::levelToColor(LogLevel level) const {
    switch (level) {
        case LogLevel::DEBUG: return Colors::DEBUG;
        case LogLevel::INFO: return Colors::INFO;
        case LogLevel::WARNING: return Colors::WARNING;
        case LogLevel::ERROR: return Colors::ERROR;
        case LogLevel::CRITICAL: return Colors::CRITICAL;
        default: return Colors::RESET;
    }
}

std::string TextFormatter::formatTimestamp(const std::chrono::system_clock::time_point& tp) const {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        tp.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();
    return oss.str();
}

// JsonFormatter implementation
std::string JsonFormatter::format(const LogEntry& entry) const {
    std::ostringstream oss;
    oss << "{";
    oss << "\"timestamp\":\"" << formatTimestamp(entry.timestamp) << "\",";
    oss << "\"level\":\"" << static_cast<int>(entry.level) << "\",";
    oss << "\"logger\":\"" << escapeJson(entry.logger_name) << "\",";
    oss << "\"module\":\"" << escapeJson(entry.module) << "\",";
    oss << "\"function\":\"" << escapeJson(entry.function) << "\",";
    oss << "\"line\":" << entry.line << ",";
    oss << "\"message\":\"" << escapeJson(entry.message) << "\",";
    oss << "\"thread_id\":\"" << entry.thread_id << "\"";
    
    // Add extra fields
    for (const auto& [key, value] : entry.extra_fields) {
        oss << ",\"" << escapeJson(key) << "\":\"" << escapeJson(value) << "\"";
    }
    
    oss << "}";
    return oss.str();
}

std::string JsonFormatter::escapeJson(const std::string& str) const {
    std::string escaped;
    for (char c : str) {
        switch (c) {
            case '"': escaped += "\\\""; break;
            case '\\': escaped += "\\\\"; break;
            case '\b': escaped += "\\b"; break;
            case '\f': escaped += "\\f"; break;
            case '\n': escaped += "\\n"; break;
            case '\r': escaped += "\\r"; break;
            case '\t': escaped += "\\t"; break;
            default: escaped += c; break;
        }
    }
    return escaped;
}

std::string JsonFormatter::formatTimestamp(const std::chrono::system_clock::time_point& tp) const {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::ostringstream oss;
    oss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%S");
    oss << "Z";
    return oss.str();
}

// ConsoleHandler implementation
ConsoleHandler::ConsoleHandler(std::ostream& stream) 
    : output_stream_(stream) {
    formatter_ = std::make_unique<TextFormatter>(true);
}

void ConsoleHandler::handle(const LogEntry& entry) {
    if (!shouldHandle(entry.level)) return;
    
    std::lock_guard<std::mutex> lock(mutex_);
    output_stream_ << formatter_->format(entry) << std::endl;
    output_stream_.flush();
}

// FileHandler implementation
FileHandler::FileHandler(const std::string& filename, size_t max_file_size, int backup_count)
    : filename_(filename), max_file_size_(max_file_size), backup_count_(backup_count), current_size_(0) {
    
    ensureDirectoryExists(filename);
    
    file_stream_.open(filename, std::ios::app);
    if (!file_stream_.is_open()) {
        throw std::runtime_error("Failed to open log file: " + filename);
    }
    
    // Get current file size
    file_stream_.seekp(0, std::ios::end);
    current_size_ = file_stream_.tellp();
    
    formatter_ = std::make_unique<TextFormatter>(false);
}

FileHandler::~FileHandler() {
    if (file_stream_.is_open()) {
        file_stream_.close();
    }
}

void FileHandler::handle(const LogEntry& entry) {
    if (!shouldHandle(entry.level)) return;
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::string formatted = formatter_->format(entry);
    file_stream_ << formatted << std::endl;
    file_stream_.flush();
    
    current_size_ += formatted.length() + 1; // +1 for newline
    
    if (current_size_ >= max_file_size_) {
        rotateFile();
    }
}

void FileHandler::rotateFile() {
    file_stream_.close();
    
    // Rotate backup files
    for (int i = backup_count_ - 1; i > 0; --i) {
        std::string old_name = filename_ + "." + std::to_string(i);
        std::string new_name = filename_ + "." + std::to_string(i + 1);
        
        if (std::filesystem::exists(old_name)) {
            if (i == backup_count_ - 1) {
                std::filesystem::remove(new_name); // Remove oldest backup
            }
            std::filesystem::rename(old_name, new_name);
        }
    }
    
    // Move current file to .1
    if (std::filesystem::exists(filename_)) {
        std::filesystem::rename(filename_, filename_ + ".1");
    }
    
    // Open new file
    file_stream_.open(filename_, std::ios::out | std::ios::trunc);
    current_size_ = 0;
}

void FileHandler::ensureDirectoryExists(const std::string& filepath) {
    std::filesystem::path path(filepath);
    std::filesystem::path dir = path.parent_path();
    
    if (!dir.empty() && !std::filesystem::exists(dir)) {
        std::filesystem::create_directories(dir);
    }
}

// Logger implementation
Logger::Logger(const std::string& name, LogLevel min_level) 
    : name_(name), min_level_(min_level) {
}

std::shared_ptr<Logger> Logger::getLogger(const std::string& name) {
    std::lock_guard<std::mutex> lock(instances_mutex_);
    
    auto it = instances_.find(name);
    if (it != instances_.end()) {
        return it->second;
    }
    
    auto logger = std::shared_ptr<Logger>(new Logger(name));
    instances_[name] = logger;
    return logger;
}

void Logger::addHandler(std::unique_ptr<LogHandler> handler) {
    std::lock_guard<std::mutex> lock(mutex_);
    handlers_.push_back(std::move(handler));
}

void Logger::removeAllHandlers() {
    std::lock_guard<std::mutex> lock(mutex_);
    handlers_.clear();
}

void Logger::addFilter(std::unique_ptr<LogFilter> filter) {
    std::lock_guard<std::mutex> lock(mutex_);
    filters_.push_back(std::move(filter));
}

void Logger::removeAllFilters() {
    std::lock_guard<std::mutex> lock(mutex_);
    filters_.clear();
}

void Logger::log(LogLevel level, const std::string& message,
                 const std::string& module, const std::string& function,
                 int line, const std::map<std::string, std::string>& extra) {

    if (!shouldLog(level)) return;

    LogEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.level = level;
    entry.logger_name = name_;
    entry.module = module;
    entry.function = function;
    entry.line = line;
    entry.message = message;
    entry.thread_id = std::this_thread::get_id();
    entry.extra_fields = extra;

    if (!passesFilters(entry)) return;

    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& handler : handlers_) {
        handler->handle(entry);
    }
}

void Logger::debug(const std::string& message, const std::string& module,
                   const std::string& function, int line) {
    log(LogLevel::DEBUG, message, module, function, line);
}

void Logger::info(const std::string& message, const std::string& module,
                  const std::string& function, int line) {
    log(LogLevel::INFO, message, module, function, line);
}

void Logger::warning(const std::string& message, const std::string& module,
                     const std::string& function, int line) {
    log(LogLevel::WARNING, message, module, function, line);
}

void Logger::error(const std::string& message, const std::string& module,
                   const std::string& function, int line) {
    log(LogLevel::ERROR, message, module, function, line);
}

void Logger::critical(const std::string& message, const std::string& module,
                      const std::string& function, int line) {
    log(LogLevel::CRITICAL, message, module, function, line);
}

bool Logger::shouldLog(LogLevel level) const {
    return static_cast<int>(level) >= static_cast<int>(min_level_);
}

bool Logger::passesFilters(const LogEntry& entry) const {
    for (const auto& filter : filters_) {
        if (!filter->shouldLog(entry)) {
            return false;
        }
    }
    return true;
}

// Convenience function
std::shared_ptr<Logger> getLogger(const std::string& name) {
    return Logger::getLogger(name);
}

} // namespace utilities
