#ifndef UTILITIES_CONFIG_H
#define UTILITIES_CONFIG_H

#include <string>
#include <map>
#include <vector>
#include <memory>
#include <fstream>
#include <sstream>
#include <mutex>
#include <functional>
#include <variant>
#include <optional>
#include "ErrorHandler.h"

namespace utilities {

/**
 * @brief Configuration value type that can hold different data types
 */
using ConfigValue = std::variant<std::string, int, double, bool>;

/**
 * @brief Configuration change callback type
 */
using ConfigChangeCallback = std::function<void(const std::string& key, const ConfigValue& old_value, const ConfigValue& new_value)>;

/**
 * @brief Configuration validation function type
 */
using ConfigValidator = std::function<bool(const std::string& key, const ConfigValue& value)>;

/**
 * @brief Configuration source interface
 */
class ConfigSource {
public:
    virtual ~ConfigSource() = default;
    virtual std::map<std::string, ConfigValue> load() = 0;
    virtual void save(const std::map<std::string, ConfigValue>& config) = 0;
    virtual bool isWritable() const = 0;
    virtual std::string getSourceName() const = 0;
};

/**
 * @brief File-based configuration source
 */
class FileConfigSource : public ConfigSource {
private:
    std::string filename_;
    bool writable_;
    
public:
    explicit FileConfigSource(const std::string& filename, bool writable = true)
        : filename_(filename), writable_(writable) {}
    
    std::map<std::string, ConfigValue> load() override;
    void save(const std::map<std::string, ConfigValue>& config) override;
    bool isWritable() const override { return writable_; }
    std::string getSourceName() const override { return "File: " + filename_; }
    
private:
    ConfigValue parseValue(const std::string& value);
    std::string valueToString(const ConfigValue& value);
    std::string trim(const std::string& str);
};

/**
 * @brief Environment variable configuration source
 */
class EnvironmentConfigSource : public ConfigSource {
private:
    std::string prefix_;
    
public:
    explicit EnvironmentConfigSource(const std::string& prefix = "")
        : prefix_(prefix) {}
    
    std::map<std::string, ConfigValue> load() override;
    void save(const std::map<std::string, ConfigValue>& config) override;
    bool isWritable() const override { return true; }
    std::string getSourceName() const override { return "Environment" + (prefix_.empty() ? "" : " (prefix: " + prefix_ + ")"); }
    
private:
    std::string keyToEnvVar(const std::string& key);
    std::string envVarToKey(const std::string& env_var);
    ConfigValue parseValue(const std::string& value);
};

/**
 * @brief Command line arguments configuration source
 */
class CommandLineConfigSource : public ConfigSource {
private:
    std::vector<std::string> args_;
    std::string prefix_;
    
public:
    CommandLineConfigSource(int argc, char* argv[], const std::string& prefix = "--")
        : prefix_(prefix) {
        for (int i = 1; i < argc; i++) {
            args_.emplace_back(argv[i]);
        }
    }
    
    explicit CommandLineConfigSource(const std::vector<std::string>& args, const std::string& prefix = "--")
        : args_(args), prefix_(prefix) {}
    
    std::map<std::string, ConfigValue> load() override;
    void save(const std::map<std::string, ConfigValue>& config) override;
    bool isWritable() const override { return false; }
    std::string getSourceName() const override { return "Command Line"; }
    
private:
    ConfigValue parseValue(const std::string& value);
};

/**
 * @brief Main Configuration Manager class
 */
class ConfigManager {
private:
    std::map<std::string, ConfigValue> config_;
    std::vector<std::unique_ptr<ConfigSource>> sources_;
    std::map<std::string, ConfigValidator> validators_;
    std::vector<ConfigChangeCallback> change_callbacks_;
    std::map<std::string, ConfigValue> defaults_;
    mutable std::mutex mutex_;
    bool auto_save_;
    std::shared_ptr<Logger> logger_;
    
public:
    explicit ConfigManager(bool auto_save = false, std::shared_ptr<Logger> logger = nullptr);
    
    // Source management
    void addSource(std::unique_ptr<ConfigSource> source);
    void loadFromSources();
    void saveToWritableSources();
    
    // Configuration access
    template<typename T>
    T get(const std::string& key) const;
    
    template<typename T>
    T get(const std::string& key, const T& default_value) const;
    
    template<typename T>
    void set(const std::string& key, const T& value);
    
    bool has(const std::string& key) const;
    void remove(const std::string& key);
    void clear();
    
    // Default values
    template<typename T>
    void setDefault(const std::string& key, const T& value);
    
    void removeDefault(const std::string& key);
    
    // Validation
    void addValidator(const std::string& key, ConfigValidator validator);
    void removeValidator(const std::string& key);
    bool validate() const;
    bool validate(const std::string& key, const ConfigValue& value) const;
    
    // Change callbacks
    void addChangeCallback(ConfigChangeCallback callback);
    void removeAllChangeCallbacks();
    
    // Utility methods
    std::vector<std::string> getKeys() const;
    std::map<std::string, ConfigValue> getAll() const;
    void merge(const std::map<std::string, ConfigValue>& other);

    // Logger management
    void setLogger(std::shared_ptr<Logger> logger) { logger_ = logger; }
    std::shared_ptr<Logger> getLogger() const { return logger_; }
    
    // Configuration sections (dot notation support)
    std::map<std::string, ConfigValue> getSection(const std::string& section_prefix) const;
    void setSection(const std::string& section_prefix, const std::map<std::string, ConfigValue>& section);
    
    // Type conversion utilities
    static std::string toString(const ConfigValue& value);
    static int toInt(const ConfigValue& value);
    static double toDouble(const ConfigValue& value);
    static bool toBool(const ConfigValue& value);
    
    // Configuration file format detection
    enum class FileFormat {
        AUTO,
        KEY_VALUE,
        JSON,
        INI
    };
    
    void loadFromFile(const std::string& filename, FileFormat format = FileFormat::AUTO);
    void saveToFile(const std::string& filename, FileFormat format = FileFormat::KEY_VALUE);
    
private:
    void notifyChange(const std::string& key, const ConfigValue& old_value, const ConfigValue& new_value);
    FileFormat detectFileFormat(const std::string& filename);
    std::map<std::string, ConfigValue> parseKeyValueFile(const std::string& content);
    std::map<std::string, ConfigValue> parseJsonFile(const std::string& content);
    std::map<std::string, ConfigValue> parseIniFile(const std::string& content);
    std::string generateKeyValueFile(const std::map<std::string, ConfigValue>& config);
    std::string generateJsonFile(const std::map<std::string, ConfigValue>& config);
    std::string generateIniFile(const std::map<std::string, ConfigValue>& config);
};

/**
 * @brief Global configuration manager instance
 */
ConfigManager& getGlobalConfig();

/**
 * @brief Configuration builder for easy setup
 */
class ConfigBuilder {
private:
    std::unique_ptr<ConfigManager> config_manager_;
    
public:
    ConfigBuilder();
    
    ConfigBuilder& withFileSource(const std::string& filename, bool writable = true);
    ConfigBuilder& withEnvironmentSource(const std::string& prefix = "");
    ConfigBuilder& withCommandLineSource(int argc, char* argv[], const std::string& prefix = "--");
    ConfigBuilder& withCommandLineSource(const std::vector<std::string>& args, const std::string& prefix = "--");
    ConfigBuilder& withAutoSave(bool auto_save = true);
    ConfigBuilder& withLogger(std::shared_ptr<Logger> logger);
    
    template<typename T>
    ConfigBuilder& withDefault(const std::string& key, const T& value);
    
    ConfigBuilder& withValidator(const std::string& key, ConfigValidator validator);
    ConfigBuilder& withChangeCallback(ConfigChangeCallback callback);
    
    std::unique_ptr<ConfigManager> build();
};

// Template implementations
template<typename T>
T ConfigManager::get(const std::string& key) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = config_.find(key);
    if (it != config_.end()) {
        try {
            return std::get<T>(it->second);
        } catch (const std::bad_variant_access& e) {
            THROW_CONFIG_ERROR("Type mismatch for config key '" + key + "': " + e.what());
        }
    }
    
    // Check defaults
    auto default_it = defaults_.find(key);
    if (default_it != defaults_.end()) {
        try {
            return std::get<T>(default_it->second);
        } catch (const std::bad_variant_access& e) {
            THROW_CONFIG_ERROR("Type mismatch for default config key '" + key + "': " + e.what());
        }
    }
    
    THROW_CONFIG_ERROR("Configuration key not found: " + key);
}

template<typename T>
T ConfigManager::get(const std::string& key, const T& default_value) const {
    try {
        return get<T>(key);
    } catch (const ConfigurationError&) {
        return default_value;
    }
}

template<typename T>
void ConfigManager::set(const std::string& key, const T& value) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    ConfigValue new_value = value;
    
    // Validate the new value
    if (!validate(key, new_value)) {
        THROW_CONFIG_ERROR("Validation failed for config key '" + key + "'");
    }
    
    ConfigValue old_value;
    bool had_old_value = false;
    
    auto it = config_.find(key);
    if (it != config_.end()) {
        old_value = it->second;
        had_old_value = true;
    }
    
    config_[key] = new_value;
    
    // Notify change
    if (had_old_value) {
        notifyChange(key, old_value, new_value);
    } else {
        notifyChange(key, ConfigValue{}, new_value);
    }
    
    // Auto-save if enabled
    if (auto_save_) {
        try {
            saveToWritableSources();
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->warning("Failed to auto-save configuration: " + std::string(e.what()));
            }
        }
    }
}

template<typename T>
void ConfigManager::setDefault(const std::string& key, const T& value) {
    std::lock_guard<std::mutex> lock(mutex_);
    defaults_[key] = value;
}

template<typename T>
ConfigBuilder& ConfigBuilder::withDefault(const std::string& key, const T& value) {
    config_manager_->setDefault(key, value);
    return *this;
}

} // namespace utilities

#endif // UTILITIES_CONFIG_H
