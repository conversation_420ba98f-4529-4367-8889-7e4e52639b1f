#!/bin/bash

# Build script for the C++ Utilities Library
# This script provides various build options and configurations

set -e  # Exit on any error

# Default values
BUILD_TYPE="Release"
BUILD_TESTS=true
BUILD_EXAMPLES=true
BUILD_DOCS=false
INSTALL=false
CLEAN=false
USE_CMAKE=true
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -t, --type TYPE        Build type: Debug, Release, RelWithDebInfo (default: Release)"
    echo "  -c, --cmake            Use CMake for building (default)"
    echo "  -m, --make             Use Makefile for building"
    echo "  --no-tests             Don't build tests"
    echo "  --no-examples          Don't build examples"
    echo "  --docs                 Build documentation"
    echo "  --install              Install after building"
    echo "  --clean                Clean before building"
    echo "  -v, --verbose          Verbose output"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                     # Build release version with CMake"
    echo "  $0 -t Debug --docs     # Build debug version with documentation"
    echo "  $0 -m --clean          # Clean build with Makefile"
    echo "  $0 --install           # Build and install"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -c|--cmake)
            USE_CMAKE=true
            shift
            ;;
        -m|--make)
            USE_CMAKE=false
            shift
            ;;
        --no-tests)
            BUILD_TESTS=false
            shift
            ;;
        --no-examples)
            BUILD_EXAMPLES=false
            shift
            ;;
        --docs)
            BUILD_DOCS=true
            shift
            ;;
        --install)
            INSTALL=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate build type
case $BUILD_TYPE in
    Debug|Release|RelWithDebInfo)
        ;;
    *)
        print_error "Invalid build type: $BUILD_TYPE"
        print_error "Valid types: Debug, Release, RelWithDebInfo"
        exit 1
        ;;
esac

print_status "Starting build process..."
print_status "Build type: $BUILD_TYPE"
print_status "Build system: $([ "$USE_CMAKE" = true ] && echo "CMake" || echo "Makefile")"
print_status "Build tests: $BUILD_TESTS"
print_status "Build examples: $BUILD_EXAMPLES"
print_status "Build docs: $BUILD_DOCS"

# Create logs directory
mkdir -p logs

# Clean if requested
if [ "$CLEAN" = true ]; then
    print_status "Cleaning previous build..."
    if [ "$USE_CMAKE" = true ]; then
        rm -rf build/
    else
        make clean 2>/dev/null || true
    fi
    print_success "Clean completed"
fi

# Build with CMake
if [ "$USE_CMAKE" = true ]; then
    print_status "Building with CMake..."
    
    # Create build directory
    mkdir -p build
    cd build
    
    # Configure
    CMAKE_ARGS="-DCMAKE_BUILD_TYPE=$BUILD_TYPE"
    
    if [ "$BUILD_TESTS" = true ]; then
        CMAKE_ARGS="$CMAKE_ARGS -DBUILD_TESTS=ON"
    fi
    
    if [ "$BUILD_DOCS" = true ]; then
        CMAKE_ARGS="$CMAKE_ARGS -DBUILD_DOCS=ON"
    fi
    
    if [ "$VERBOSE" = true ]; then
        CMAKE_ARGS="$CMAKE_ARGS -DCMAKE_VERBOSE_MAKEFILE=ON"
    fi
    
    print_status "Configuring with: cmake $CMAKE_ARGS .."
    cmake $CMAKE_ARGS ..
    
    # Build
    MAKE_ARGS=""
    if [ "$VERBOSE" = true ]; then
        MAKE_ARGS="VERBOSE=1"
    fi
    
    print_status "Building..."
    make $MAKE_ARGS -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
    
    cd ..
    
# Build with Makefile
else
    print_status "Building with Makefile..."
    
    MAKE_TARGET="all"
    if [ "$BUILD_TYPE" = "Debug" ]; then
        MAKE_TARGET="debug"
    elif [ "$BUILD_TYPE" = "Release" ]; then
        MAKE_TARGET="release"
    fi
    
    make $MAKE_TARGET
fi

print_success "Build completed successfully!"

# Run tests if built
if [ "$BUILD_TESTS" = true ]; then
    print_status "Running tests..."
    if [ "$USE_CMAKE" = true ]; then
        cd build
        ./test_utilities || print_warning "Some tests failed"
        cd ..
    else
        make test || print_warning "Some tests failed"
    fi
fi

# Run example if built
if [ "$BUILD_EXAMPLES" = true ]; then
    print_status "Running example application..."
    if [ "$USE_CMAKE" = true ]; then
        cd build
        ./example_app || print_warning "Example failed to run"
        cd ..
    else
        make run-example || print_warning "Example failed to run"
    fi
fi

# Install if requested
if [ "$INSTALL" = true ]; then
    print_status "Installing..."
    if [ "$USE_CMAKE" = true ]; then
        cd build
        sudo make install
        cd ..
    else
        sudo make install
    fi
    print_success "Installation completed"
fi

print_success "All operations completed successfully!"
print_status "Build artifacts are in: $([ "$USE_CMAKE" = true ] && echo "build/" || echo "current directory")"

if [ "$BUILD_DOCS" = true ]; then
    print_status "Documentation is in: docs/html/"
fi
