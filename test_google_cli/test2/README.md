# Enhanced C++ Web Server

A comprehensive, high-performance web server built in C++ with modern features including middleware support, database integration, security enhancements, and JavaScript engine integration.

## 🚀 Features

### Core Architecture
- **Dependency Injection**: Modular service container with automatic lifecycle management
- **Event-Driven Architecture**: Loose coupling through event bus system
- **Thread Pool**: High-performance task scheduling with priority queues
- **Application Modules**: Priority-based loading and configuration system

### HTTP Server
- **Middleware Pipeline**: Extensible middleware chain with CORS, rate limiting, authentication, compression
- **Advanced Routing**: Pattern-based routing with parameter extraction
- **WebSocket Support**: Real-time bidirectional communication
- **SSL/TLS**: Secure connections with certificate management
- **Static File Serving**: Efficient static content delivery with caching
- **Request/Response Interceptors**: Comprehensive logging and monitoring

### Database Integration
- **SQLite Integration**: Built-in database support with connection pooling
- **ORM-like Features**: Query builder, model mapping, and repository pattern
- **Migration System**: Database schema versioning and management
- **Transaction Support**: ACID compliance with automatic rollback

### Security
- **Authentication & Authorization**: JWT tokens, session management, role-based access
- **Input Validation**: SQL injection, XSS, and path traversal prevention
- **CSRF Protection**: Cross-site request forgery mitigation
- **Rate Limiting**: API endpoint protection with configurable limits
- **Security Headers**: Comprehensive security header management

### JavaScript Engine
- **Enhanced JS Runtime**: Improved error handling and debugging
- **Module System**: CommonJS-style module loading and dependency resolution
- **Async Execution**: Non-blocking JavaScript execution with worker threads
- **Built-in APIs**: Console, timers, file system, HTTP, and crypto APIs

### Testing & Quality
- **Comprehensive Testing Framework**: Unit tests, integration tests, and benchmarks
- **Mock Framework**: Easy mocking for isolated testing
- **Code Coverage**: Detailed coverage reporting
- **Performance Benchmarks**: Automated performance regression detection

### DevOps & Deployment
- **Docker Support**: Multi-stage builds with production and development images
- **CI/CD Pipeline**: GitHub Actions with automated testing and deployment
- **Package Management**: Cross-platform packaging (DEB, RPM, MSI, DMG)
- **Documentation**: Auto-generated API docs with Doxygen

## 📋 Requirements

### System Requirements
- **C++17** compatible compiler (GCC 8+, Clang 7+, MSVC 2019+)
- **CMake 3.16+**
- **Git** for version control

### Optional Dependencies
- **SQLite3** (bundled if not found)
- **zlib** for compression support
- **OpenSSL** for SSL/TLS and advanced cryptography

## 🛠️ Quick Start

### Building from Source

```bash
# Clone the repository
git clone https://github.com/your-org/enhanced-webserver.git
cd enhanced-webserver

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake -DCMAKE_BUILD_TYPE=Release ..

# Build the project
make -j$(nproc)

# Run tests
make test

# Install (optional)
sudo make install
```

### Using Docker

```bash
# Build and run with Docker
docker build -t enhanced-webserver .
docker run -p 8080:8080 enhanced-webserver

# Or use pre-built image
docker run -p 8080:8080 ghcr.io/your-org/enhanced-webserver:latest
```

### Basic Usage

```cpp
#include "core/Application.h"
#include "server/EnhancedHttpServer.h"

int main() {
    // Initialize application
    auto app = core::Application::getInstance();
    app->initialize();
    
    // Configure server
    server::ServerConfig config;
    config.host = "0.0.0.0";
    config.port = 8080;
    
    // Create and configure server
    auto server = std::make_unique<server::EnhancedHttpServer>(config);
    
    // Add middleware
    server->enableCors();
    server->enableLogging();
    server->enableSecurityHeaders();
    server->enableStaticFiles("./static");
    
    // Add routes
    server->getRouter().get("/", [](const auto& req, auto& res) {
        res.body = R"({"message": "Hello, World!"})";
        res.headers["Content-Type"] = "application/json";
    });
    
    server->getRouter().get("/api/users/{id}", [](const auto& req, auto& res) {
        auto user_id = req.path_params.at("id");
        res.body = R"({"id": ")" + user_id + R"(", "name": "John Doe"})";
        res.headers["Content-Type"] = "application/json";
    });
    
    // Start server
    server->start();
    
    // Keep running
    app->run();
    
    return 0;
}
```

## 📚 Documentation

### API Documentation
- [Core Architecture](docs/core.md) - Application framework and dependency injection
- [HTTP Server](docs/server.md) - Server configuration and middleware
- [Database](docs/database.md) - Database integration and ORM
- [Security](docs/security.md) - Authentication, authorization, and security features
- [JavaScript Engine](docs/javascript.md) - JS runtime and module system
- [Testing](docs/testing.md) - Testing framework and best practices

### Tutorials
- [Getting Started](docs/tutorials/getting-started.md)
- [Building a REST API](docs/tutorials/rest-api.md)
- [Adding Authentication](docs/tutorials/authentication.md)
- [Database Integration](docs/tutorials/database.md)
- [Deployment Guide](docs/tutorials/deployment.md)

### Examples
- [Basic Web Server](examples/basic-server/)
- [REST API with Database](examples/rest-api/)
- [Real-time Chat Application](examples/chat-app/)
- [File Upload Service](examples/file-upload/)
- [Microservice Template](examples/microservice/)

## 🧪 Testing

```bash
# Run all tests
./run_tests

# Run with verbose output
./run_tests --verbose

# Run specific test suite
./run_tests --filter="ServerTests"

# Run benchmarks
./run_benchmarks

# Generate coverage report (Debug build)
make coverage
```

## 🔧 Configuration

### Server Configuration

```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 8080,
    "thread_pool_size": 8,
    "max_connections": 1000,
    "request_timeout": 30,
    "enable_compression": true,
    "static_files_root": "./static"
  },
  "database": {
    "connection_string": "database.db",
    "max_connections": 10,
    "connection_timeout": 30
  },
  "security": {
    "jwt_secret": "your-secret-key",
    "token_expiration": 3600,
    "enable_rate_limiting": true,
    "rate_limit_requests": 100,
    "rate_limit_window": 60
  },
  "logging": {
    "level": "info",
    "file": "server.log",
    "max_file_size": "10MB",
    "max_files": 5
  }
}
```

### Environment Variables

```bash
# Server configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_THREADS=8

# Database
DATABASE_URL=sqlite:///app/data/database.db

# Security
JWT_SECRET=your-jwt-secret-key
ENABLE_SSL=false
SSL_CERT_FILE=/path/to/cert.pem
SSL_KEY_FILE=/path/to/key.pem

# Logging
LOG_LEVEL=info
LOG_FILE=/app/logs/server.log
```

## 🚀 Deployment

### Docker Deployment

```yaml
# docker-compose.yml
version: '3.8'
services:
  webserver:
    image: ghcr.io/your-org/enhanced-webserver:latest
    ports:
      - "8080:8080"
    environment:
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - LOG_LEVEL=info
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./static:/app/static
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### Kubernetes Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: enhanced-webserver
spec:
  replicas: 3
  selector:
    matchLabels:
      app: enhanced-webserver
  template:
    metadata:
      labels:
        app: enhanced-webserver
    spec:
      containers:
      - name: webserver
        image: ghcr.io/your-org/enhanced-webserver:latest
        ports:
        - containerPort: 8080
        env:
        - name: SERVER_HOST
          value: "0.0.0.0"
        - name: SERVER_PORT
          value: "8080"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone and setup development environment
git clone https://github.com/your-org/enhanced-webserver.git
cd enhanced-webserver

# Install development dependencies
sudo apt-get install -y build-essential cmake pkg-config \
  libsqlite3-dev zlib1g-dev libssl-dev \
  clang-format cppcheck doxygen graphviz lcov

# Setup development build
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Debug ..
make dev-setup

# Run tests and checks
make test
make lint
make format
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with modern C++17 features
- Inspired by popular web frameworks like Express.js and Flask
- Uses industry-standard security practices
- Follows RAII and modern C++ best practices

## 📞 Support

- 📖 [Documentation](https://your-org.github.io/enhanced-webserver/)
- 🐛 [Issue Tracker](https://github.com/your-org/enhanced-webserver/issues)
- 💬 [Discussions](https://github.com/your-org/enhanced-webserver/discussions)
- 📧 [Email Support](mailto:<EMAIL>)
