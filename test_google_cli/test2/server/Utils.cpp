#include "Utils.h"
#include "Response.h" // For HttpResponse
#include <sstream>
#include <chrono>
#include <ctime>
#include <fstream>
#include <filesystem>

namespace Utils {

std::string get_current_gmt_time() {
    auto now = std::chrono::system_clock::now();
    std::time_t current_time = std::chrono::system_clock::to_time_t(now);
    std::tm* gmt = std::gmtime(&current_time);

    char buffer[100];
    std::strftime(buffer, sizeof(buffer), "%a, %d %b %Y %H:%M:%S GMT", gmt);
    return std::string(buffer);
}

std::map<std::string, std::string> parse_url_encoded(const std::string& data) {
    std::map<std::string, std::string> params;
    std::istringstream iss(data);
    std::string segment;

    while (std::getline(iss, segment, '&')) {
        size_t eq_pos = segment.find('=');
        if (eq_pos != std::string::npos) {
            std::string key = segment.substr(0, eq_pos);
            std::string value = segment.substr(eq_pos + 1);

            std::string decoded_value;
            for (size_t i = 0; i < value.length(); ++i) {
                if (value[i] == '+') {
                    decoded_value += ' ';
                } else if (value[i] == '%' && i + 2 < value.length()) {
                    std::string hex_code = value.substr(i + 1, 2);
                    try {
                        char decoded_char = static_cast<char>(std::stoi(hex_code, nullptr, 16));
                        decoded_value += decoded_char;
                        i += 2; // Skip the two hex characters
                    } catch (const std::exception& e) {
                        decoded_value += value[i];
                    }
                } else {
                    decoded_value += value[i];
                }
            }
            params[key] = decoded_value;
        }
    }
    return params;
}

std::string get_mime_type(const std::string& file_path) {
    size_t dot_pos = file_path.rfind('.');
    if (dot_pos == std::string::npos) {
        return "application/octet-stream";
    }
    std::string ext = file_path.substr(dot_pos);
    if (ext == ".html" || ext == ".htm") return "text/html";
    if (ext == ".css") return "text/css";
    if (ext == ".js") return "application/javascript";
    if (ext == ".json") return "application/json";
    if (ext == ".jpg" || ext == ".jpeg") return "image/jpeg";
    if (ext == ".png") return "image/png";
    if (ext == ".gif") return "image/gif";
    if (ext == ".svg") return "image/svg+xml";
    if (ext == ".ico") return "image/x-icon";
    if (ext == ".pdf") return "application/pdf";
    return "application/octet-stream";
}

std::string render_template(std::string html_template, const std::map<std::string, std::string>& context) {
    for (const auto& pair : context) {
        std::string placeholder = "{{" + pair.first + "}}";
        size_t pos = html_template.find(placeholder);
        while (pos != std::string::npos) {
            html_template.replace(pos, placeholder.length(), pair.second);
            pos = html_template.find(placeholder, pos + pair.second.length());
        }
    }
    return html_template;
}

HttpResponse serve_static_file(const std::string& file_path) {
    HttpResponse response;
    std::string full_path = "public" + file_path;

    if (!std::filesystem::exists(full_path) || !std::filesystem::is_regular_file(full_path)) {
        response.status_code = 404;
        response.status_message = "Not Found";
        response.headers["Content-Type"] = "text/html";
        response.body = "<html><body><h1>404 Not Found</h1><p>The requested static file " + file_path + " was not found.</p></body></html>";
        return response;
    }

    std::ifstream file(full_path, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
        response.status_code = 500;
        response.status_message = "Internal Server Error";
        response.headers["Content-Type"] = "text/html";
        response.body = "<html><body><h1>500 Internal Server Error</h1><p>Could not open static file " + file_path + ".</p></body></html>";
        return response;
    }

    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);

    std::vector<char> buffer(size);
    if (file.read(buffer.data(), size)) {
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = get_mime_type(file_path);
        response.body.assign(buffer.begin(), buffer.end());
    } else {
        response.status_code = 500;
        response.status_message = "Internal Server Error";
        response.headers["Content-Type"] = "text/html";
        response.body = "<html><body><h1>500 Internal Server Error</h1><p>Could not read static file " + file_path + ".</p></body></html>";
    }
    file.close();
    return response;
}

} // namespace Utils
