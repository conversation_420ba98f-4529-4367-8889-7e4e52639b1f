#ifndef ROUTER_H
#define ROUTER_H

#include "Request.h"
#include "Response.h"
#include <functional>
#include <map>
#include <string>

// Define a request handler type
using RequestHandler = std::function<HttpResponse(const HttpRequest&)>;

class Router {
public:
    void get(const std::string& path, RequestHandler handler);
    void post(const std::string& path, RequestHandler handler);
    HttpResponse handleRequest(const HttpRequest& request) const;

private:
    std::map<std::string, RequestHandler> get_routes_;
    std::map<std::string, RequestHandler> post_routes_;
};

#endif // ROUTER_H
