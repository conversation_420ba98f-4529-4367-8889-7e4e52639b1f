#ifndef SERVER_MIDDLEWARE_H
#define SERVER_MIDDLEWARE_H

#include "Request.h"
#include "Response.h"
#include <functional>
#include <memory>
#include <vector>
#include <string>
#include <chrono>
#include <unordered_map>
#include <unordered_set>
#include <regex>
#include "../utilities/Logger.h"

namespace server {

// Forward declarations
class MiddlewareChain;

/**
 * @brief Next function type for middleware chain
 */
using NextFunction = std::function<void()>;

/**
 * @brief Middleware function type
 */
using MiddlewareFunction = std::function<void(HttpRequest&, HttpResponse&, NextFunction)>;

/**
 * @brief Base middleware interface
 */
class IMiddleware {
public:
    virtual ~IMiddleware() = default;
    virtual void handle(HttpRequest& request, HttpResponse& response, NextFunction next) = 0;
    virtual const std::string& getName() const = 0;
    virtual int getPriority() const { return 0; } // Higher priority executes first
};

/**
 * @brief Base middleware implementation
 */
class BaseMiddleware : public IMiddleware {
protected:
    std::string name_;
    int priority_;
    std::shared_ptr<utilities::Logger> logger_;
    
public:
    explicit BaseMiddleware(const std::string& name, int priority = 0);
    virtual ~BaseMiddleware() = default;
    
    const std::string& getName() const override { return name_; }
    int getPriority() const override { return priority_; }
};

/**
 * @brief CORS (Cross-Origin Resource Sharing) middleware
 */
class CorsMiddleware : public BaseMiddleware {
private:
    std::unordered_set<std::string> allowed_origins_;
    std::unordered_set<std::string> allowed_methods_;
    std::unordered_set<std::string> allowed_headers_;
    std::unordered_set<std::string> exposed_headers_;
    bool allow_credentials_;
    int max_age_;
    
public:
    explicit CorsMiddleware(int priority = 100);
    
    void handle(HttpRequest& request, HttpResponse& response, NextFunction next) override;
    
    // Configuration methods
    CorsMiddleware& allowOrigin(const std::string& origin);
    CorsMiddleware& allowOrigins(const std::vector<std::string>& origins);
    CorsMiddleware& allowMethod(const std::string& method);
    CorsMiddleware& allowMethods(const std::vector<std::string>& methods);
    CorsMiddleware& allowHeader(const std::string& header);
    CorsMiddleware& allowHeaders(const std::vector<std::string>& headers);
    CorsMiddleware& exposeHeader(const std::string& header);
    CorsMiddleware& exposeHeaders(const std::vector<std::string>& headers);
    CorsMiddleware& allowCredentials(bool allow = true);
    CorsMiddleware& setMaxAge(int seconds);
};

/**
 * @brief Logging middleware for request/response logging
 */
class LoggingMiddleware : public BaseMiddleware {
private:
    bool log_request_body_;
    bool log_response_body_;
    size_t max_body_log_size_;
    
public:
    explicit LoggingMiddleware(int priority = 90);
    
    void handle(HttpRequest& request, HttpResponse& response, NextFunction next) override;
    
    LoggingMiddleware& logRequestBody(bool log = true, size_t max_size = 1024);
    LoggingMiddleware& logResponseBody(bool log = true, size_t max_size = 1024);
};

/**
 * @brief Rate limiting middleware
 */
class RateLimitMiddleware : public BaseMiddleware {
private:
    struct ClientInfo {
        size_t request_count;
        std::chrono::steady_clock::time_point window_start;
        std::chrono::steady_clock::time_point last_request;
    };
    
    std::unordered_map<std::string, ClientInfo> clients_;
    mutable std::mutex clients_mutex_;
    
    size_t max_requests_;
    std::chrono::seconds window_duration_;
    std::chrono::seconds cleanup_interval_;
    std::chrono::steady_clock::time_point last_cleanup_;
    
    std::string getClientKey(const HttpRequest& request) const;
    void cleanupExpiredClients();
    
public:
    explicit RateLimitMiddleware(size_t max_requests = 100, 
                                std::chrono::seconds window = std::chrono::seconds(60),
                                int priority = 95);
    
    void handle(HttpRequest& request, HttpResponse& response, NextFunction next) override;
};

/**
 * @brief Authentication middleware
 */
class AuthMiddleware : public BaseMiddleware {
private:
    std::function<bool(const std::string&)> token_validator_;
    std::vector<std::regex> excluded_paths_;
    std::string auth_header_;
    std::string auth_scheme_;
    
public:
    explicit AuthMiddleware(std::function<bool(const std::string&)> validator,
                           const std::string& auth_header = "Authorization",
                           const std::string& auth_scheme = "Bearer",
                           int priority = 80);
    
    void handle(HttpRequest& request, HttpResponse& response, NextFunction next) override;
    
    AuthMiddleware& excludePath(const std::string& path_pattern);
    AuthMiddleware& excludePaths(const std::vector<std::string>& path_patterns);
};

/**
 * @brief Compression middleware (gzip)
 */
class CompressionMiddleware : public BaseMiddleware {
private:
    size_t min_size_;
    std::unordered_set<std::string> compressible_types_;
    
    bool shouldCompress(const HttpRequest& request, const HttpResponse& response) const;
    std::string compressGzip(const std::string& data) const;
    
public:
    explicit CompressionMiddleware(size_t min_size = 1024, int priority = 10);
    
    void handle(HttpRequest& request, HttpResponse& response, NextFunction next) override;
    
    CompressionMiddleware& addCompressibleType(const std::string& content_type);
    CompressionMiddleware& setMinSize(size_t size);
};

/**
 * @brief Security headers middleware
 */
class SecurityHeadersMiddleware : public BaseMiddleware {
private:
    std::unordered_map<std::string, std::string> security_headers_;
    
public:
    explicit SecurityHeadersMiddleware(int priority = 85);
    
    void handle(HttpRequest& request, HttpResponse& response, NextFunction next) override;
    
    SecurityHeadersMiddleware& setHeader(const std::string& name, const std::string& value);
    SecurityHeadersMiddleware& enableHSTS(int max_age = 31536000, bool include_subdomains = true);
    SecurityHeadersMiddleware& enableCSP(const std::string& policy);
    SecurityHeadersMiddleware& enableXFrameOptions(const std::string& value = "DENY");
    SecurityHeadersMiddleware& enableXContentTypeOptions();
    SecurityHeadersMiddleware& enableReferrerPolicy(const std::string& policy = "strict-origin-when-cross-origin");
};

/**
 * @brief Static file serving middleware
 */
class StaticFileMiddleware : public BaseMiddleware {
private:
    std::string root_path_;
    std::string url_prefix_;
    std::unordered_map<std::string, std::string> mime_types_;
    bool enable_directory_listing_;
    std::vector<std::string> index_files_;
    
    std::string getMimeType(const std::string& file_path) const;
    std::string generateDirectoryListing(const std::string& dir_path, const std::string& url_path) const;
    bool isPathSafe(const std::string& path) const;
    
public:
    explicit StaticFileMiddleware(const std::string& root_path, 
                                 const std::string& url_prefix = "/static",
                                 int priority = 50);
    
    void handle(HttpRequest& request, HttpResponse& response, NextFunction next) override;
    
    StaticFileMiddleware& enableDirectoryListing(bool enable = true);
    StaticFileMiddleware& addIndexFile(const std::string& filename);
    StaticFileMiddleware& addMimeType(const std::string& extension, const std::string& mime_type);
};

/**
 * @brief Middleware chain for managing and executing middleware
 */
class MiddlewareChain {
private:
    std::vector<std::shared_ptr<IMiddleware>> middlewares_;
    std::shared_ptr<utilities::Logger> logger_;
    
public:
    MiddlewareChain();
    
    void add(std::shared_ptr<IMiddleware> middleware);
    void remove(const std::string& name);
    void clear();
    
    void execute(HttpRequest& request, HttpResponse& response);
    
    std::vector<std::string> getMiddlewareNames() const;
    size_t size() const { return middlewares_.size(); }
    
private:
    void sortMiddlewares();
    void executeNext(size_t index, HttpRequest& request, HttpResponse& response);
};

/**
 * @brief Middleware factory for easy creation
 */
class MiddlewareFactory {
public:
    static std::shared_ptr<CorsMiddleware> createCors();
    static std::shared_ptr<LoggingMiddleware> createLogging();
    static std::shared_ptr<RateLimitMiddleware> createRateLimit(size_t max_requests = 100, 
                                                               std::chrono::seconds window = std::chrono::seconds(60));
    static std::shared_ptr<AuthMiddleware> createAuth(std::function<bool(const std::string&)> validator);
    static std::shared_ptr<CompressionMiddleware> createCompression(size_t min_size = 1024);
    static std::shared_ptr<SecurityHeadersMiddleware> createSecurityHeaders();
    static std::shared_ptr<StaticFileMiddleware> createStaticFiles(const std::string& root_path, 
                                                                  const std::string& url_prefix = "/static");
    
    // Convenience method to create a common middleware chain
    static std::unique_ptr<MiddlewareChain> createDefaultChain(const std::string& static_root = "");
};

} // namespace server

#endif // SERVER_MIDDLEWARE_H
