#include "Middleware.h"
#include <algorithm>
#include <fstream>
#include <sstream>
#include <filesystem>

namespace server {

// BaseMiddleware implementation
BaseMiddleware::BaseMiddleware(const std::string& name, int priority)
    : name_(name), priority_(priority) {
    logger_ = utilities::Logger::getLogger("Middleware." + name_);
}

// CorsMiddleware implementation
CorsMiddleware::CorsMiddleware(int priority)
    : BaseMiddleware("CORS", priority), allow_credentials_(false), max_age_(86400) {
    
    // Default allowed methods
    allowed_methods_.insert("GET");
    allowed_methods_.insert("POST");
    allowed_methods_.insert("PUT");
    allowed_methods_.insert("DELETE");
    allowed_methods_.insert("OPTIONS");
    
    // Default allowed headers
    allowed_headers_.insert("Content-Type");
    allowed_headers_.insert("Authorization");
    allowed_headers_.insert("X-Requested-With");
}

void CorsMiddleware::handle(HttpRequest& request, HttpResponse& response, NextFunction next) {
    const std::string& origin = request.headers.count("Origin") ? request.headers.at("Origin") : "";
    
    // Check if origin is allowed
    bool origin_allowed = allowed_origins_.empty() || 
                         allowed_origins_.count("*") || 
                         allowed_origins_.count(origin);
    
    if (origin_allowed && !origin.empty()) {
        response.headers["Access-Control-Allow-Origin"] = origin;
    }
    
    if (allow_credentials_) {
        response.headers["Access-Control-Allow-Credentials"] = "true";
    }
    
    // Handle preflight request
    if (request.method == "OPTIONS") {
        if (!allowed_methods_.empty()) {
            std::string methods;
            for (const auto& method : allowed_methods_) {
                if (!methods.empty()) methods += ", ";
                methods += method;
            }
            response.headers["Access-Control-Allow-Methods"] = methods;
        }
        
        if (!allowed_headers_.empty()) {
            std::string headers;
            for (const auto& header : allowed_headers_) {
                if (!headers.empty()) headers += ", ";
                headers += header;
            }
            response.headers["Access-Control-Allow-Headers"] = headers;
        }
        
        response.headers["Access-Control-Max-Age"] = std::to_string(max_age_);
        response.status_code = 204;
        return; // Don't call next for preflight
    }
    
    // Add exposed headers for actual requests
    if (!exposed_headers_.empty()) {
        std::string headers;
        for (const auto& header : exposed_headers_) {
            if (!headers.empty()) headers += ", ";
            headers += header;
        }
        response.headers["Access-Control-Expose-Headers"] = headers;
    }
    
    next();
}

CorsMiddleware& CorsMiddleware::allowOrigin(const std::string& origin) {
    allowed_origins_.insert(origin);
    return *this;
}

CorsMiddleware& CorsMiddleware::allowOrigins(const std::vector<std::string>& origins) {
    for (const auto& origin : origins) {
        allowed_origins_.insert(origin);
    }
    return *this;
}

CorsMiddleware& CorsMiddleware::allowMethod(const std::string& method) {
    allowed_methods_.insert(method);
    return *this;
}

CorsMiddleware& CorsMiddleware::allowMethods(const std::vector<std::string>& methods) {
    for (const auto& method : methods) {
        allowed_methods_.insert(method);
    }
    return *this;
}

CorsMiddleware& CorsMiddleware::allowHeader(const std::string& header) {
    allowed_headers_.insert(header);
    return *this;
}

CorsMiddleware& CorsMiddleware::allowHeaders(const std::vector<std::string>& headers) {
    for (const auto& header : headers) {
        allowed_headers_.insert(header);
    }
    return *this;
}

CorsMiddleware& CorsMiddleware::exposeHeader(const std::string& header) {
    exposed_headers_.insert(header);
    return *this;
}

CorsMiddleware& CorsMiddleware::exposeHeaders(const std::vector<std::string>& headers) {
    for (const auto& header : headers) {
        exposed_headers_.insert(header);
    }
    return *this;
}

CorsMiddleware& CorsMiddleware::allowCredentials(bool allow) {
    allow_credentials_ = allow;
    return *this;
}

CorsMiddleware& CorsMiddleware::setMaxAge(int seconds) {
    max_age_ = seconds;
    return *this;
}

// LoggingMiddleware implementation
LoggingMiddleware::LoggingMiddleware(int priority)
    : BaseMiddleware("Logging", priority), log_request_body_(false), 
      log_response_body_(false), max_body_log_size_(1024) {
}

void LoggingMiddleware::handle(HttpRequest& request, HttpResponse& response, NextFunction next) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Log request
    std::ostringstream request_log;
    request_log << request.method << " " << request.path;
    if (!request.query_string.empty()) {
        request_log << "?" << request.query_string;
    }
    request_log << " HTTP/1.1";
    
    logger_->info("Request: " + request_log.str());
    
    if (log_request_body_ && !request.body.empty()) {
        std::string body = request.body;
        if (body.length() > max_body_log_size_) {
            body = body.substr(0, max_body_log_size_) + "... (truncated)";
        }
        logger_->debug("Request body: " + body);
    }
    
    // Execute next middleware/handler
    next();
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Log response
    std::ostringstream response_log;
    response_log << "Response: " << response.status_code << " " << response.status_message;
    response_log << " (" << duration.count() << "ms)";
    
    if (response.status_code >= 400) {
        logger_->warning(response_log.str());
    } else {
        logger_->info(response_log.str());
    }
    
    if (log_response_body_ && !response.body.empty()) {
        std::string body = response.body;
        if (body.length() > max_body_log_size_) {
            body = body.substr(0, max_body_log_size_) + "... (truncated)";
        }
        logger_->debug("Response body: " + body);
    }
}

LoggingMiddleware& LoggingMiddleware::logRequestBody(bool log, size_t max_size) {
    log_request_body_ = log;
    max_body_log_size_ = max_size;
    return *this;
}

LoggingMiddleware& LoggingMiddleware::logResponseBody(bool log, size_t max_size) {
    log_response_body_ = log;
    max_body_log_size_ = max_size;
    return *this;
}

// RateLimitMiddleware implementation
RateLimitMiddleware::RateLimitMiddleware(size_t max_requests, std::chrono::seconds window, int priority)
    : BaseMiddleware("RateLimit", priority), max_requests_(max_requests), 
      window_duration_(window), cleanup_interval_(std::chrono::seconds(300)),
      last_cleanup_(std::chrono::steady_clock::now()) {
}

std::string RateLimitMiddleware::getClientKey(const HttpRequest& request) const {
    // Use X-Forwarded-For if available, otherwise use connection info
    auto it = request.headers.find("X-Forwarded-For");
    if (it != request.headers.end()) {
        return it->second;
    }
    
    it = request.headers.find("X-Real-IP");
    if (it != request.headers.end()) {
        return it->second;
    }
    
    // Fallback to a default key (in a real implementation, you'd get the client IP)
    return "default_client";
}

void RateLimitMiddleware::cleanupExpiredClients() {
    auto now = std::chrono::steady_clock::now();
    
    if (now - last_cleanup_ < cleanup_interval_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    auto it = clients_.begin();
    while (it != clients_.end()) {
        if (now - it->second.last_request > window_duration_ * 2) {
            it = clients_.erase(it);
        } else {
            ++it;
        }
    }
    
    last_cleanup_ = now;
}

void RateLimitMiddleware::handle(HttpRequest& request, HttpResponse& response, NextFunction next) {
    cleanupExpiredClients();
    
    std::string client_key = getClientKey(request);
    auto now = std::chrono::steady_clock::now();
    
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    auto& client_info = clients_[client_key];
    
    // Reset window if expired
    if (now - client_info.window_start > window_duration_) {
        client_info.request_count = 0;
        client_info.window_start = now;
    }
    
    client_info.last_request = now;
    client_info.request_count++;
    
    // Check rate limit
    if (client_info.request_count > max_requests_) {
        response.status_code = 429;
        response.status_message = "Too Many Requests";
        response.headers["Content-Type"] = "application/json";
        response.headers["Retry-After"] = std::to_string(window_duration_.count());
        response.body = R"({"error": "Rate limit exceeded", "retry_after": )" + 
                       std::to_string(window_duration_.count()) + "}";
        
        logger_->warning("Rate limit exceeded for client: " + client_key);
        return; // Don't call next
    }
    
    // Add rate limit headers
    response.headers["X-RateLimit-Limit"] = std::to_string(max_requests_);
    response.headers["X-RateLimit-Remaining"] = std::to_string(max_requests_ - client_info.request_count);
    response.headers["X-RateLimit-Reset"] = std::to_string(
        std::chrono::duration_cast<std::chrono::seconds>(
            client_info.window_start + window_duration_ - now).count());
    
    next();
}

// MiddlewareChain implementation
MiddlewareChain::MiddlewareChain() {
    logger_ = utilities::Logger::getLogger("MiddlewareChain");
}

void MiddlewareChain::add(std::shared_ptr<IMiddleware> middleware) {
    middlewares_.push_back(middleware);
    sortMiddlewares();
    logger_->info("Added middleware: " + middleware->getName());
}

void MiddlewareChain::remove(const std::string& name) {
    auto it = std::remove_if(middlewares_.begin(), middlewares_.end(),
                            [&name](const std::shared_ptr<IMiddleware>& middleware) {
                                return middleware->getName() == name;
                            });

    if (it != middlewares_.end()) {
        middlewares_.erase(it, middlewares_.end());
        logger_->info("Removed middleware: " + name);
    }
}

void MiddlewareChain::clear() {
    middlewares_.clear();
    logger_->info("Cleared all middleware");
}

void MiddlewareChain::execute(HttpRequest& request, HttpResponse& response) {
    if (middlewares_.empty()) {
        return;
    }

    executeNext(0, request, response);
}

void MiddlewareChain::executeNext(size_t index, HttpRequest& request, HttpResponse& response) {
    if (index >= middlewares_.size()) {
        return;
    }

    auto next = [this, index, &request, &response]() {
        executeNext(index + 1, request, response);
    };

    try {
        middlewares_[index]->handle(request, response, next);
    } catch (const std::exception& e) {
        logger_->error("Error in middleware " + middlewares_[index]->getName() + ": " + e.what());
        response.status_code = 500;
        response.status_message = "Internal Server Error";
        response.headers["Content-Type"] = "application/json";
        response.body = R"({"error": "Internal server error"})";
    }
}

void MiddlewareChain::sortMiddlewares() {
    std::sort(middlewares_.begin(), middlewares_.end(),
              [](const std::shared_ptr<IMiddleware>& a, const std::shared_ptr<IMiddleware>& b) {
                  return a->getPriority() > b->getPriority();
              });
}

std::vector<std::string> MiddlewareChain::getMiddlewareNames() const {
    std::vector<std::string> names;
    names.reserve(middlewares_.size());

    for (const auto& middleware : middlewares_) {
        names.push_back(middleware->getName());
    }

    return names;
}

// MiddlewareFactory implementation
std::shared_ptr<CorsMiddleware> MiddlewareFactory::createCors() {
    return std::make_shared<CorsMiddleware>();
}

std::shared_ptr<LoggingMiddleware> MiddlewareFactory::createLogging() {
    return std::make_shared<LoggingMiddleware>();
}

std::shared_ptr<RateLimitMiddleware> MiddlewareFactory::createRateLimit(size_t max_requests, std::chrono::seconds window) {
    return std::make_shared<RateLimitMiddleware>(max_requests, window);
}

std::shared_ptr<AuthMiddleware> MiddlewareFactory::createAuth(std::function<bool(const std::string&)> validator) {
    return std::make_shared<AuthMiddleware>(validator);
}

std::shared_ptr<SecurityHeadersMiddleware> MiddlewareFactory::createSecurityHeaders() {
    return std::make_shared<SecurityHeadersMiddleware>();
}

std::shared_ptr<StaticFileMiddleware> MiddlewareFactory::createStaticFiles(const std::string& root_path, const std::string& url_prefix) {
    return std::make_shared<StaticFileMiddleware>(root_path, url_prefix);
}

std::unique_ptr<MiddlewareChain> MiddlewareFactory::createDefaultChain(const std::string& static_root) {
    auto chain = std::make_unique<MiddlewareChain>();

    // Add default middleware in order of priority
    chain->add(createSecurityHeaders());
    chain->add(createCors());
    chain->add(createLogging());

    if (!static_root.empty()) {
        chain->add(createStaticFiles(static_root));
    }

    return chain;
}

// SecurityHeadersMiddleware implementation
SecurityHeadersMiddleware::SecurityHeadersMiddleware(int priority)
    : BaseMiddleware("SecurityHeaders", priority) {
}

void SecurityHeadersMiddleware::handle(HttpRequest& request, HttpResponse& response, NextFunction next) {
    // Add security headers
    response.headers["X-Content-Type-Options"] = "nosniff";
    response.headers["X-Frame-Options"] = "DENY";
    response.headers["X-XSS-Protection"] = "1; mode=block";
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains";
    response.headers["Content-Security-Policy"] = "default-src 'self'";

    next();
}

// StaticFileMiddleware implementation
StaticFileMiddleware::StaticFileMiddleware(const std::string& root_path, const std::string& url_prefix, int priority)
    : BaseMiddleware("StaticFiles", priority), root_path_(root_path), url_prefix_(url_prefix) {
}

void StaticFileMiddleware::handle(HttpRequest& request, HttpResponse& response, NextFunction next) {
    try {
        // Check if this request is for a static file
        std::string file_path = request.path;

        // Remove leading slash if present
        if (file_path.front() == '/') {
            file_path = file_path.substr(1);
        }

        // If no file extension, try adding .html
        if (file_path.find('.') == std::string::npos) {
            file_path += ".html";
        }

        // Construct full file path
        std::string full_path = root_path_ + "/" + file_path;

        // Security check: prevent directory traversal
        if (file_path.find("..") != std::string::npos) {
            response.status_code = 403;
            response.status_message = "Forbidden";
            response.body = "Access denied";
            return;
        }

        // Try to open and read the file
        std::ifstream file(full_path, std::ios::binary);
        if (!file.is_open()) {
            // File not found, continue to next middleware/route
            next();
            return;
        }

        // Read file content
        std::ostringstream content_stream;
        content_stream << file.rdbuf();
        std::string content = content_stream.str();
        file.close();

        // Set appropriate content type based on file extension
        std::string content_type = getMimeType(file_path);

        // Set response
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = content_type;
        response.headers["Content-Length"] = std::to_string(content.length());
        response.body = content;

        // Log successful file serving
        std::cout << "Served static file: " << full_path << " (" << content.length() << " bytes)" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error serving static file: " << e.what() << std::endl;
        response.status_code = 500;
        response.status_message = "Internal Server Error";
        response.body = "Error serving file";
    }
}

std::string StaticFileMiddleware::getMimeType(const std::string& file_path) const {
    // Extract file extension
    size_t dot_pos = file_path.find_last_of('.');
    if (dot_pos == std::string::npos) {
        return "application/octet-stream";
    }

    std::string extension = file_path.substr(dot_pos + 1);
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    // Map common file extensions to MIME types
    if (extension == "html" || extension == "htm") {
        return "text/html";
    } else if (extension == "css") {
        return "text/css";
    } else if (extension == "js") {
        return "application/javascript";
    } else if (extension == "json") {
        return "application/json";
    } else if (extension == "png") {
        return "image/png";
    } else if (extension == "jpg" || extension == "jpeg") {
        return "image/jpeg";
    } else if (extension == "gif") {
        return "image/gif";
    } else if (extension == "svg") {
        return "image/svg+xml";
    } else if (extension == "ico") {
        return "image/x-icon";
    } else if (extension == "txt") {
        return "text/plain";
    } else if (extension == "xml") {
        return "application/xml";
    } else if (extension == "pdf") {
        return "application/pdf";
    } else {
        return "application/octet-stream";
    }
}

// CompressionMiddleware implementation
CompressionMiddleware::CompressionMiddleware(size_t min_size, int priority)
    : BaseMiddleware("Compression", priority), min_size_(min_size) {
}

void CompressionMiddleware::handle(HttpRequest& request, HttpResponse& response, NextFunction next) {
    // Simple compression middleware (stub implementation)
    next();

    // Check if compression is supported and response is large enough
    auto accept_encoding = request.headers.find("Accept-Encoding");
    if (accept_encoding != request.headers.end() &&
        accept_encoding->second.find("gzip") != std::string::npos &&
        response.body.size() >= min_size_) {
        // Add compression header (actual compression not implemented)
        response.headers["Content-Encoding"] = "gzip";
    }
}

// AuthMiddleware implementation
AuthMiddleware::AuthMiddleware(std::function<bool(const std::string&)> validator,
                               const std::string& header_name,
                               const std::string& scheme,
                               int priority)
    : BaseMiddleware("Auth", priority), token_validator_(validator),
      auth_header_(header_name), auth_scheme_(scheme) {
}

void AuthMiddleware::handle(HttpRequest& request, HttpResponse& response, NextFunction next) {
    auto auth_header = request.headers.find(auth_header_);
    if (auth_header == request.headers.end()) {
        response.status_code = 401;
        response.body = "Authorization required";
        response.headers["WWW-Authenticate"] = auth_scheme_;
        return;
    }

    std::string token = auth_header->second;
    if (!auth_scheme_.empty() && token.find(auth_scheme_ + " ") == 0) {
        token = token.substr(auth_scheme_.length() + 1);
    }

    if (!token_validator_(token)) {
        response.status_code = 401;
        response.body = "Invalid authorization";
        return;
    }

    next();
}

} // namespace server
