#include "Router.h"
#include "Utils.h" // For Utils::serve_static_file (if static serving is integrated here, or in HttpServer)

void Router::get(const std::string& path, RequestHandler handler) {
    get_routes_[path] = handler;
}

void Router::post(const std::string& path, RequestHandler handler) {
    post_routes_[path] = handler;
}

HttpResponse Router::handleRequest(const HttpRequest& request) const {
    if (request.method == "GET") {
        auto it = get_routes_.find(request.path);
        if (it != get_routes_.end()) {
            return it->second(request);
        } 
    } else if (request.method == "POST") {
        auto it = post_routes_.find(request.path);
        if (it != post_routes_.end()) {
            return it->second(request);
        }
    }
    
    // If no specific route found for the method/path combination
    HttpResponse response;
    response.status_code = 404;
    response.status_message = "Not Found";
    response.headers["Content-Type"] = "text/html";
    response.body = "<html><body><h1>404 Not Found</h1><p>The requested URL " + request.path + " was not found on this server.</p></body></html>";
    return response;
}
