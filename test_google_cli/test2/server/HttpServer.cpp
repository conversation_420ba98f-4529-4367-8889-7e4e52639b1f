#include "HttpServer.h"
#include "Request.h"
#include "Response.h"
#include "Utils.h"

#include <iostream>
#include <thread>
#include <csignal>
#include <sstream>
#include <algorithm>

#include <sys/socket.h>
#include <netinet/in.h>
#include <unistd.h>
#include <cstring>
#include <arpa/inet.h>
#include <fcntl.h>
#include <sys/time.h>

// Global pointer to HttpServer instance for signal handler
HttpServer* global_http_server_instance = nullptr;

// Signal handler for graceful shutdown
void server_signal_handler(int signal) {
    if (signal == SIGINT && global_http_server_instance) {
        std::cout << "\nSIGINT received. Shutting down server..." << std::endl;
        global_http_server_instance->stop();
    }
}

HttpServer::HttpServer(int port) : HttpServer(ServerConfig{.port = port}) {
}

HttpServer::HttpServer(const ServerConfig& config)
    : config_(config), server_fd_(-1), running_(false) {

    // Initialize logger
    logger_ = utilities::getLogger("HttpServer");

    // Initialize thread pool
    thread_pool_ = std::make_unique<core::ThreadPool>(config_.thread_pool_size);

    // Setup default error handler
    setup_default_error_handler();

    // Setup signal handlers
    setup_signal_handlers();

    logger_->info("HttpServer initialized on " + config_.host + ":" + std::to_string(config_.port));
}

HttpServer::~HttpServer() {
    stop();
    if (thread_pool_) {
        thread_pool_->shutdown();
    }
}

void HttpServer::start() {
    if (running_.load()) {
        logger_->warning("Server is already running");
        return;
    }

    struct sockaddr_in address;
    int opt = 1;
    int addrlen = sizeof(address);

    try {
        // Creating socket file descriptor
        if ((server_fd_ = socket(AF_INET, SOCK_STREAM, 0)) == -1) {
            throw std::runtime_error("Failed to create socket: " + std::string(strerror(errno)));
        }

        // Set socket options
        if (!set_socket_options(server_fd_)) {
            close(server_fd_);
            throw std::runtime_error("Failed to set socket options");
        }

        // Configure address
        address.sin_family = AF_INET;
        if (config_.host == "0.0.0.0" || config_.host == "localhost") {
            address.sin_addr.s_addr = INADDR_ANY;
        } else {
            // For specific IP addresses, would need inet_pton
            address.sin_addr.s_addr = INADDR_ANY;
        }
        address.sin_port = htons(config_.port);

        // Bind socket to address
        if (bind(server_fd_, (struct sockaddr *)&address, sizeof(address)) == -1) {
            close(server_fd_);
            throw std::runtime_error("Failed to bind to " + config_.host + ":" +
                                   std::to_string(config_.port) + " - " + std::string(strerror(errno)));
        }

        // Start listening
        if (listen(server_fd_, config_.max_connections) == -1) {
            close(server_fd_);
            throw std::runtime_error("Failed to listen on socket: " + std::string(strerror(errno)));
        }

        running_ = true;
        stats_.start_time = std::chrono::steady_clock::now();

        logger_->info("Server started successfully");
        logger_->info("Listening on " + config_.host + ":" + std::to_string(config_.port));
        logger_->info("Thread pool size: " + std::to_string(config_.thread_pool_size));
        logger_->info("Max connections: " + std::to_string(config_.max_connections));

        std::cout << "Server listening on " << config_.host << ":" << config_.port << std::endl;
        std::cout << "Access it at http://localhost:" << config_.port << "/" << std::endl;
        std::cout << "Press Ctrl+C to shut down the server." << std::endl;

        // Main accept loop
        while (running_.load()) {
            int new_socket = accept(server_fd_, (struct sockaddr *)&address, (socklen_t*)&addrlen);
            if (new_socket == -1) {
                if (running_.load()) {
                    logger_->error("Accept failed: " + std::string(strerror(errno)));
                }
                continue;
            }

            // Update statistics
            stats_.total_connections++;
            stats_.active_connections++;

            // Set socket timeout
            set_socket_timeout(new_socket, config_.request_timeout);

            // Handle connection in thread pool
            thread_pool_->enqueue([this, new_socket]() {
                handle_client(new_socket);
                stats_.active_connections--;
            });
        }

    } catch (const std::exception& e) {
        logger_->critical("Failed to start server: " + std::string(e.what()));
        throw;
    }
}

void HttpServer::stop() {
    if (!running_.load()) {
        return;
    }

    logger_->info("Shutting down server...");
    running_ = false;

    if (server_fd_ != -1) {
        close(server_fd_);
        server_fd_ = -1;
    }

    // Wait for active connections to finish (with timeout)
    auto start_time = std::chrono::steady_clock::now();
    auto timeout = std::chrono::seconds(10);

    while (stats_.active_connections.load() > 0 &&
           std::chrono::steady_clock::now() - start_time < timeout) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    if (stats_.active_connections.load() > 0) {
        logger_->warning("Forced shutdown with " +
                        std::to_string(stats_.active_connections.load()) + " active connections");
    }

    logger_->info("Server shut down gracefully");
    std::cout << "Server shut down gracefully." << std::endl;
}

Router& HttpServer::get_router() {
    return router_;
}

// Middleware management
void HttpServer::use(std::shared_ptr<server::IMiddleware> middleware) {
    middleware_chain_.push_back(middleware);
    logger_->debug("Added middleware: " + middleware->getName());
}

void HttpServer::enableCors(const std::string& origin) {
    auto cors_middleware = std::make_shared<server::CorsMiddleware>();
    if (origin != "*") {
        cors_middleware->allowOrigin(origin);
    } else {
        cors_middleware->allowOrigin("*");
    }
    use(cors_middleware);
}

void HttpServer::enableLogging() {
    auto logging_middleware = std::make_shared<server::LoggingMiddleware>();
    use(logging_middleware);
}

void HttpServer::enableRateLimit(size_t max_requests, std::chrono::seconds window) {
    auto rate_limit_middleware = std::make_shared<server::RateLimitMiddleware>(max_requests, window);
    use(rate_limit_middleware);
}

void HttpServer::enableSecurityHeaders() {
    auto security_middleware = std::make_shared<server::SecurityHeadersMiddleware>();
    use(security_middleware);
}

void HttpServer::enableStaticFiles(const std::string& root_path) {
    auto static_middleware = std::make_shared<server::StaticFileMiddleware>(root_path);
    use(static_middleware);
}

void HttpServer::enableCompression() {
    auto compression_middleware = std::make_shared<server::CompressionMiddleware>();
    use(compression_middleware);
}

// Configuration
void HttpServer::setConfig(const ServerConfig& config) {
    if (running_.load()) {
        throw std::runtime_error("Cannot change configuration while server is running");
    }
    config_ = config;
}

// Statistics
std::string HttpServer::getStatsJson() const {
    auto now = std::chrono::steady_clock::now();
    auto uptime = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.start_time).count();

    std::ostringstream oss;
    oss << "{"
        << "\"uptime_seconds\":" << uptime << ","
        << "\"total_connections\":" << stats_.total_connections.load() << ","
        << "\"active_connections\":" << stats_.active_connections.load() << ","
        << "\"total_requests\":" << stats_.total_requests.load() << ","
        << "\"failed_requests\":" << stats_.failed_requests.load() << ","
        << "\"bytes_sent\":" << stats_.bytes_sent.load() << ","
        << "\"bytes_received\":" << stats_.bytes_received.load() << ","
        << "\"requests_per_second\":" << (uptime > 0 ? stats_.total_requests.load() / uptime : 0)
        << "}";
    return oss.str();
}

// Error handling
void HttpServer::setErrorHandler(std::function<HttpResponse(const std::exception&, const HttpRequest&)> handler) {
    error_handler_ = handler;
}

void HttpServer::handle_client(int client_socket) {
    try {
        logger_->debug("Handling new client connection", "HttpServer", "handle_client", client_socket);

        // Validate socket
        if (client_socket < 0) {
            logger_->error("Invalid client socket descriptor: " + std::to_string(client_socket));
            return;
        }

        // Track connection in active connections
        {
            std::lock_guard<std::mutex> lock(connections_mutex_);
            active_connections_[client_socket] = std::chrono::steady_clock::now();
            logger_->debug("Added client to active connections", "HttpServer", "handle_client", client_socket);
        }

        // Allocate buffer with safety checks
        std::vector<char> buffer(config_.max_request_size);
        ssize_t bytes_read = read(client_socket, buffer.data(), buffer.size() - 1);

        if (bytes_read < 0) {
            logger_->error("Failed to read from client socket " + std::to_string(client_socket) +
                          ": " + std::string(strerror(errno)));
            close(client_socket);
            return;
        }
        if (bytes_read == 0) {
            logger_->debug("Client closed connection", "HttpServer", "handle_client", client_socket);
            close(client_socket);
            return;
        }

        // Ensure null termination
        buffer[bytes_read] = '\0';
        std::string raw_request(buffer.data(), bytes_read);

        // Validate request size
        if (raw_request.size() > config_.max_request_size) {
            logger_->warning("Request size exceeds maximum: " + std::to_string(raw_request.size()));
            // Send 413 Payload Too Large response
            std::string error_response = "HTTP/1.1 413 Payload Too Large\r\nContent-Length: 0\r\n\r\n";
            write(client_socket, error_response.c_str(), error_response.length());
            close(client_socket);
            return;
        }

        stats_.bytes_received += bytes_read;
        stats_.total_requests++;

        logger_->debug("Received request", "HttpServer", "handle_client",
                      static_cast<int>(raw_request.size()));

        // Process the request with enhanced error handling
        process_request(client_socket, raw_request);

    } catch (const std::exception& e) {
        logger_->error("Error handling client: " + std::string(e.what()));
        stats_.failed_requests++;

        // Send error response
        HttpResponse error_response;
        error_response.status_code = 500;
        error_response.status_message = "Internal Server Error";
        error_response.headers["Content-Type"] = "text/plain";
        error_response.body = "Internal Server Error";

        std::string response_str = error_response.to_string();
        write(client_socket, response_str.c_str(), response_str.length());
    }

    // Clean up connection
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        active_connections_.erase(client_socket);
    }
    close(client_socket);
}

void HttpServer::process_request(int client_socket, const std::string& raw_request) {
    try {
        logger_->debug("Processing request", "HttpServer", "process_request", client_socket);

        // Validate input
        if (raw_request.empty()) {
            logger_->warning("Empty request received from client " + std::to_string(client_socket));
            send_error_response(client_socket, 400, "Bad Request", "Empty request");
            return;
        }

        // Parse HTTP request with error handling
        HttpRequest request;
        try {
            request = parse_http_request(raw_request);
        } catch (const std::exception& e) {
            logger_->error("Failed to parse HTTP request: " + std::string(e.what()));
            send_error_response(client_socket, 400, "Bad Request", "Malformed request");
            return;
        }

        // Log request details
        logger_->info("Processing " + request.method + " " + request.path,
                     "HttpServer", "process_request", client_socket);

        // Special handling for JavaScript execution requests
        if (request.path == "/run_js" || request.path.find("js_tester") != std::string::npos) {
            logger_->info("JavaScript execution request detected", "HttpServer", "process_request");

            // Add extra validation for JS requests
            if (request.method != "GET" && request.method != "POST") {
                logger_->warning("Invalid method for JS request: " + request.method);
                send_error_response(client_socket, 405, "Method Not Allowed", "Only GET and POST allowed for JS execution");
                return;
            }
        }

        HttpResponse response;

        // Execute middleware chain and router with enhanced error handling
        try {
            response = execute_middleware_chain(request);
        } catch (const std::exception& e) {
            logger_->error("Error in middleware/router execution: " + std::string(e.what()));
            response = handle_error(e, request);
        }

        // Send response with validation
        send_response(client_socket, response);

        logger_->debug("Request processed successfully", "HttpServer", "process_request", client_socket);

    } catch (const std::exception& e) {
        logger_->error("Critical error processing request: " + std::string(e.what()));
        send_error_response(client_socket, 500, "Internal Server Error", "Server error occurred");
        stats_.failed_requests++;
    } catch (...) {
        logger_->error("Unknown error processing request");
        send_error_response(client_socket, 500, "Internal Server Error", "Unknown server error");
        stats_.failed_requests++;
    }
}

HttpResponse HttpServer::execute_middleware_chain(const HttpRequest& request) {
    HttpRequest mutable_request = request;
    HttpResponse response;

    // If no middleware, go directly to router
    if (middleware_chain_.empty()) {
        return router_.handleRequest(mutable_request);
    }

    // Execute middleware chain
    size_t current_index = 0;
    bool chain_completed = false;

    std::function<void()> next = [&]() {
        if (current_index < middleware_chain_.size()) {
            auto middleware = middleware_chain_[current_index++];
            middleware->handle(mutable_request, response, next);
        } else if (!chain_completed) {
            // All middleware executed, now handle with router
            chain_completed = true;
            response = router_.handleRequest(mutable_request);
        }
    };

    // Start the chain
    next();

    return response;
}

// Utility methods
void HttpServer::setup_default_error_handler() {
    error_handler_ = [this](const std::exception& e, const HttpRequest& request) -> HttpResponse {
        HttpResponse response;
        response.status_code = 500;
        response.status_message = "Internal Server Error";
        response.headers["Content-Type"] = "application/json";

        std::ostringstream oss;
        oss << "{"
            << "\"error\": \"Internal Server Error\","
            << "\"message\": \"" << e.what() << "\","
            << "\"path\": \"" << request.path << "\","
            << "\"method\": \"" << request.method << "\""
            << "}";
        response.body = oss.str();

        return response;
    };
}

void HttpServer::setup_signal_handlers() {
    global_http_server_instance = this;
    std::signal(SIGINT, server_signal_handler);
    std::signal(SIGTERM, server_signal_handler);
}

HttpResponse HttpServer::handle_error(const std::exception& e, const HttpRequest& request) {
    if (error_handler_) {
        return error_handler_(e, request);
    }

    // Default error response
    HttpResponse response;
    response.status_code = 500;
    response.status_message = "Internal Server Error";
    response.headers["Content-Type"] = "text/plain";
    response.body = "Internal Server Error";
    return response;
}

bool HttpServer::set_socket_options(int socket_fd) {
    int opt = 1;

    // Allow socket reuse
    if (setsockopt(socket_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) == -1) {
        logger_->error("Failed to set SO_REUSEADDR: " + std::string(strerror(errno)));
        return false;
    }

    if (setsockopt(socket_fd, SOL_SOCKET, SO_REUSEPORT, &opt, sizeof(opt)) == -1) {
        logger_->error("Failed to set SO_REUSEPORT: " + std::string(strerror(errno)));
        return false;
    }

    return true;
}

void HttpServer::set_socket_timeout(int socket_fd, std::chrono::seconds timeout) {
    struct timeval tv;
    tv.tv_sec = timeout.count();
    tv.tv_usec = 0;

    if (setsockopt(socket_fd, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv)) == -1) {
        logger_->warning("Failed to set receive timeout: " + std::string(strerror(errno)));
    }

    if (setsockopt(socket_fd, SOL_SOCKET, SO_SNDTIMEO, &tv, sizeof(tv)) == -1) {
        logger_->warning("Failed to set send timeout: " + std::string(strerror(errno)));
    }
}

// Enhanced error handling helper methods
void HttpServer::send_error_response(int client_socket, int status_code,
                                   const std::string& status_message,
                                   const std::string& error_message) {
    try {
        HttpResponse response;
        response.status_code = status_code;
        response.status_message = status_message;
        response.headers["Content-Type"] = "application/json";
        response.headers["Connection"] = "close";

        std::ostringstream oss;
        oss << "{"
            << "\"error\": \"" << status_message << "\","
            << "\"message\": \"" << error_message << "\","
            << "\"status\": " << status_code
            << "}";
        response.body = oss.str();

        send_response(client_socket, response);

        logger_->debug("Sent error response", "HttpServer", "send_error_response", status_code);

    } catch (const std::exception& e) {
        logger_->error("Failed to send error response: " + std::string(e.what()));
        // Fallback: send minimal response
        std::string fallback = "HTTP/1.1 500 Internal Server Error\r\nContent-Length: 0\r\n\r\n";
        write(client_socket, fallback.c_str(), fallback.length());
    }
}

void HttpServer::send_response(int client_socket, const HttpResponse& response) {
    try {
        // Validate response
        if (response.status_code < 100 || response.status_code > 599) {
            logger_->warning("Invalid status code: " + std::to_string(response.status_code));
        }

        std::string response_str = response.to_string();

        // Validate response size
        if (response_str.size() > 10 * 1024 * 1024) { // 10MB limit
            logger_->warning("Response size exceeds limit: " + std::to_string(response_str.size()));
            send_error_response(client_socket, 500, "Internal Server Error", "Response too large");
            return;
        }

        ssize_t bytes_sent = write(client_socket, response_str.c_str(), response_str.length());

        if (bytes_sent < 0) {
            logger_->error("Failed to send response to client " + std::to_string(client_socket) +
                          ": " + std::string(strerror(errno)));
        } else if (static_cast<size_t>(bytes_sent) != response_str.length()) {
            logger_->warning("Partial response sent: " + std::to_string(bytes_sent) +
                           "/" + std::to_string(response_str.length()) + " bytes");
        } else {
            stats_.bytes_sent += bytes_sent;
            logger_->debug("Response sent successfully", "HttpServer", "send_response",
                          static_cast<int>(bytes_sent));
        }

    } catch (const std::exception& e) {
        logger_->error("Exception while sending response: " + std::string(e.what()));
        send_error_response(client_socket, 500, "Internal Server Error", "Failed to send response");
    }
}