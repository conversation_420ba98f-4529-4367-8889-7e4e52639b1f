#ifndef HTTP_REQUEST_H
#define HTTP_REQUEST_H

#include <string>
#include <map>

struct HttpRequest {
    std::string method;
    std::string path;
    std::string full_uri;
    std::string http_version;
    std::map<std::string, std::string> headers;
    std::map<std::string, std::string> query_params;
    std::string query_string;
    std::map<std::string, std::string> form_data;
    std::string body;
};

HttpRequest parse_http_request(const std::string& raw_request);

#endif // HTTP_REQUEST_H
