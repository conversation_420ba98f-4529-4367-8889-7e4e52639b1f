#ifndef UTILS_H
#define UTILS_H

#include <string>
#include <map>
#include <vector>

// Forward declarations for structs used in utility functions
struct HttpResponse;

namespace Utils {
    std::string get_current_gmt_time();
    std::map<std::string, std::string> parse_url_encoded(const std::string& data);
    std::string get_mime_type(const std::string& file_path);
    std::string render_template(std::string html_template, const std::map<std::string, std::string>& context);
    HttpResponse serve_static_file(const std::string& file_path);
}

#endif // UTILS_H
