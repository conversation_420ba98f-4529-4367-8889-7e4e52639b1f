#ifndef SERVER_ENHANCED_HTTP_SERVER_H
#define SERVER_ENHANCED_HTTP_SERVER_H

#include "HttpServer.h"
#include "Middleware.h"
#include "Router.h"
#include "../core/Application.h"
#include "../core/ThreadPool.h"
#include "../utilities/Logger.h"
#include "../utilities/Config.h"
#include <memory>
#include <atomic>
#include <thread>
#include <vector>
#include <chrono>

namespace server {

/**
 * @brief Server configuration structure
 */
struct ServerConfig {
    std::string host = "localhost";
    int port = 8080;
    size_t thread_pool_size = std::thread::hardware_concurrency();
    size_t max_connections = 1000;
    std::chrono::seconds keep_alive_timeout{30};
    std::chrono::seconds request_timeout{30};
    size_t max_request_size = 1024 * 1024; // 1MB
    size_t max_header_size = 8192; // 8KB
    bool enable_compression = true;
    bool enable_keep_alive = true;
    std::string static_files_root;
    std::string static_files_prefix = "/static";
    
    // SSL/TLS configuration
    bool enable_ssl = false;
    std::string ssl_cert_file;
    std::string ssl_key_file;
    std::string ssl_ca_file;
    
    // Security configuration
    bool enable_rate_limiting = true;
    size_t rate_limit_requests = 100;
    std::chrono::seconds rate_limit_window{60};
    
    // CORS configuration
    bool enable_cors = true;
    std::vector<std::string> cors_origins = {"*"};
    std::vector<std::string> cors_methods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
    std::vector<std::string> cors_headers = {"Content-Type", "Authorization", "X-Requested-With"};
};

/**
 * @brief Connection statistics
 */
struct ConnectionStats {
    std::atomic<size_t> total_connections{0};
    std::atomic<size_t> active_connections{0};
    std::atomic<size_t> total_requests{0};
    std::atomic<size_t> failed_requests{0};
    std::atomic<size_t> bytes_sent{0};
    std::atomic<size_t> bytes_received{0};
    std::chrono::system_clock::time_point start_time;
    
    ConnectionStats() : start_time(std::chrono::system_clock::now()) {}
    
    double getRequestsPerSecond() const {
        auto now = std::chrono::system_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
        return duration.count() > 0 ? static_cast<double>(total_requests.load()) / duration.count() : 0.0;
    }
    
    double getErrorRate() const {
        size_t total = total_requests.load();
        return total > 0 ? static_cast<double>(failed_requests.load()) / total : 0.0;
    }
};

/**
 * @brief Enhanced HTTP server with middleware support, thread pool, and advanced features
 */
class EnhancedHttpServer : public core::BaseService {
private:
    ServerConfig config_;
    std::unique_ptr<MiddlewareChain> middleware_chain_;
    std::unique_ptr<Router> router_;
    std::shared_ptr<core::ThreadPool> thread_pool_;
    
    int server_fd_;
    std::atomic<bool> running_;
    std::vector<std::thread> acceptor_threads_;
    
    ConnectionStats stats_;
    std::shared_ptr<utilities::Logger> logger_;
    std::shared_ptr<utilities::ConfigManager> config_manager_;
    
    // Connection management
    struct Connection {
        int socket_fd;
        std::chrono::steady_clock::time_point created_at;
        std::chrono::steady_clock::time_point last_activity;
        bool keep_alive;
        size_t requests_count;
    };
    
    std::unordered_map<int, std::unique_ptr<Connection>> active_connections_;
    std::mutex connections_mutex_;
    std::thread cleanup_thread_;
    
    void setupSocket();
    void acceptorLoop();
    void handleConnection(std::unique_ptr<Connection> connection);
    void processRequest(Connection& connection);
    void cleanupConnections();
    void loadConfigFromManager();
    
    HttpRequest parseRequest(const std::string& raw_request);
    std::string formatResponse(const HttpResponse& response);
    
protected:
    void doInitialize() override;
    void doShutdown() override;
    
public:
    explicit EnhancedHttpServer(const ServerConfig& config = ServerConfig{});
    virtual ~EnhancedHttpServer();
    
    // Configuration
    void setConfig(const ServerConfig& config);
    const ServerConfig& getConfig() const { return config_; }
    void loadConfigFromFile(const std::string& config_file);
    
    // Middleware management
    MiddlewareChain& getMiddlewareChain() { return *middleware_chain_; }
    const MiddlewareChain& getMiddlewareChain() const { return *middleware_chain_; }
    
    void addMiddleware(std::shared_ptr<IMiddleware> middleware);
    void removeMiddleware(const std::string& name);
    void clearMiddleware();
    
    // Router access
    Router& getRouter() { return *router_; }
    const Router& getRouter() const { return *router_; }
    
    // Server control
    void start();
    void stop();
    void restart();
    
    bool isRunning() const { return running_.load(); }
    
    // Statistics
    const ConnectionStats& getStats() const { return stats_; }
    std::string getStatsJson() const;
    
    // Convenience methods for common middleware
    void enableCors(const std::vector<std::string>& origins = {"*"});
    void enableRateLimit(size_t max_requests = 100, std::chrono::seconds window = std::chrono::seconds(60));
    void enableAuth(std::function<bool(const std::string&)> token_validator);
    void enableStaticFiles(const std::string& root_path, const std::string& url_prefix = "/static");
    void enableCompression(size_t min_size = 1024);
    void enableSecurityHeaders();
    void enableLogging(bool log_bodies = false);
    
    // SSL/TLS support (placeholder for future implementation)
    void enableSSL(const std::string& cert_file, const std::string& key_file, const std::string& ca_file = "");
    
    // Health check endpoint
    void enableHealthCheck(const std::string& path = "/health");
    
    // Metrics endpoint
    void enableMetrics(const std::string& path = "/metrics");
};

/**
 * @brief HTTP server module for application integration
 */
class HttpServerModule : public core::BaseApplicationModule {
private:
    std::shared_ptr<EnhancedHttpServer> server_;
    ServerConfig config_;
    
protected:
    void doConfigure(core::ServiceContainer& container) override;
    void doInitialize() override;
    void doShutdown() override;
    
public:
    explicit HttpServerModule(const ServerConfig& config = ServerConfig{}, int priority = 50);
    
    std::shared_ptr<EnhancedHttpServer> getServer() const { return server_; }
    void setConfig(const ServerConfig& config) { config_ = config; }
};

/**
 * @brief Server builder for fluent configuration
 */
class ServerBuilder {
private:
    ServerConfig config_;
    std::vector<std::shared_ptr<IMiddleware>> middleware_;
    std::vector<std::function<void(Router&)>> route_configurators_;
    
public:
    ServerBuilder();
    
    // Basic configuration
    ServerBuilder& host(const std::string& host);
    ServerBuilder& port(int port);
    ServerBuilder& threads(size_t count);
    ServerBuilder& maxConnections(size_t count);
    ServerBuilder& requestTimeout(std::chrono::seconds timeout);
    ServerBuilder& keepAliveTimeout(std::chrono::seconds timeout);
    
    // Features
    ServerBuilder& enableCors(const std::vector<std::string>& origins = {"*"});
    ServerBuilder& enableRateLimit(size_t max_requests = 100, std::chrono::seconds window = std::chrono::seconds(60));
    ServerBuilder& enableAuth(std::function<bool(const std::string&)> validator);
    ServerBuilder& enableStaticFiles(const std::string& root, const std::string& prefix = "/static");
    ServerBuilder& enableCompression(size_t min_size = 1024);
    ServerBuilder& enableSecurityHeaders();
    ServerBuilder& enableLogging(bool log_bodies = false);
    ServerBuilder& enableSSL(const std::string& cert, const std::string& key, const std::string& ca = "");
    
    // Middleware
    ServerBuilder& addMiddleware(std::shared_ptr<IMiddleware> middleware);
    
    // Routes
    ServerBuilder& configureRoutes(std::function<void(Router&)> configurator);
    
    // Build
    std::unique_ptr<EnhancedHttpServer> build();
    std::unique_ptr<HttpServerModule> buildModule(int priority = 50);
};

} // namespace server

#endif // SERVER_ENHANCED_HTTP_SERVER_H
