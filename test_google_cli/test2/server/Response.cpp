#include "Response.h"
#include "Utils.h" // For Utils::get_current_gmt_time
#include <sstream>

std::string HttpResponse::to_string() const {
    std::ostringstream oss;
    oss << "HTTP/1.1 " << status_code << " " << status_message << "\r\n";
    oss << "Date: " << Utils::get_current_gmt_time() << "\r\n";
    oss << "Server: CppWebServer/1.0 (Unix)\r\n";
    oss << "Content-Length: " << body.length() << "\r\n";
    for (const auto& header : headers) {
        oss << header.first << ": " << header.second << "\r\n";
    }
    oss << "\r\n"; // End of headers
    oss << body;
    return oss.str();
}

