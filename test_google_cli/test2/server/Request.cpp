#include "Request.h"
#include "Utils.h" // For Utils::parse_url_encoded
#include <sstream>

HttpRequest parse_http_request(const std::string& raw_request) {
    HttpRequest request;
    std::istringstream iss(raw_request);
    std::string line;

    // Parse request line (e.g., GET / HTTP/1.1)
    std::getline(iss, line);
    std::istringstream request_line_stream(line);
    request_line_stream >> request.method >> request.full_uri >> request.http_version;

    // Extract path and query parameters
    size_t query_pos = request.full_uri.find('?');
    if (query_pos != std::string::npos) {
        request.path = request.full_uri.substr(0, query_pos);
        request.query_params = Utils::parse_url_encoded(request.full_uri.substr(query_pos + 1));
    } else {
        request.path = request.full_uri;
    }

    // Parse headers
    bool in_body = false;
    std::string current_line;
    while (std::getline(iss, current_line)) {
        if (current_line == "\r") { // End of headers
            in_body = true;
            continue;
        }
        if (!in_body) {
            size_t colon_pos = current_line.find(':');
            if (colon_pos != std::string::npos) {
                std::string name = current_line.substr(0, colon_pos);
                std::string value = current_line.substr(colon_pos + 2); // +2 to skip ": "
                if (!value.empty() && value.back() == '\r') {
                    value.pop_back();
                }
                request.headers[name] = value;
            }
        } else {
            request.body += current_line;
        }
    }

    // Parse form data if Content-Type is application/x-www-form-urlencoded
    auto content_type_it = request.headers.find("Content-Type");
    if (content_type_it != request.headers.end() && 
        content_type_it->second.find("application/x-www-form-urlencoded") != std::string::npos) {
        request.form_data = Utils::parse_url_encoded(request.body);
    }

    return request;
}