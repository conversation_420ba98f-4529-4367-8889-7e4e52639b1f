#ifndef PERFORMANCE_CACHE_H
#define PERFORMANCE_CACHE_H

#include <string>
#include <memory>
#include <unordered_map>
#include <list>
#include <mutex>
#include <chrono>
#include <functional>
#include <atomic>
#include <thread>
#include <condition_variable>
#include "../utilities/Logger.h"
#include "../core/Application.h"

namespace performance {

/**
 * @brief Cache entry with expiration and metadata
 */
template<typename T>
struct CacheEntry {
    T value;
    std::chrono::steady_clock::time_point created_at;
    std::chrono::steady_clock::time_point expires_at;
    std::chrono::steady_clock::time_point last_accessed;
    size_t access_count = 0;
    size_t size_bytes = 0;
    
    bool isExpired() const {
        return std::chrono::steady_clock::now() >= expires_at;
    }
    
    void updateAccess() {
        last_accessed = std::chrono::steady_clock::now();
        access_count++;
    }
};

/**
 * @brief LRU (Least Recently Used) Cache implementation
 */
template<typename Key, typename Value>
class LRUCache {
private:
    using CacheEntryType = CacheEntry<Value>;
    using ListIterator = typename std::list<std::pair<Key, CacheEntryType>>::iterator;
    
    std::list<std::pair<Key, CacheEntryType>> items_;
    std::unordered_map<Key, ListIterator> index_;
    
    size_t max_size_;
    size_t max_memory_;
    std::chrono::seconds default_ttl_;
    
    mutable std::mutex mutex_;
    
    // Statistics
    std::atomic<size_t> hits_{0};
    std::atomic<size_t> misses_{0};
    std::atomic<size_t> evictions_{0};
    std::atomic<size_t> current_memory_{0};
    
    void evictExpired() {
        auto now = std::chrono::steady_clock::now();
        auto it = items_.begin();
        
        while (it != items_.end()) {
            if (it->second.expires_at <= now) {
                current_memory_ -= it->second.size_bytes;
                index_.erase(it->first);
                it = items_.erase(it);
                evictions_++;
            } else {
                ++it;
            }
        }
    }
    
    void evictLRU() {
        while ((items_.size() > max_size_ || current_memory_ > max_memory_) && !items_.empty()) {
            auto& back = items_.back();
            current_memory_ -= back.second.size_bytes;
            index_.erase(back.first);
            items_.pop_back();
            evictions_++;
        }
    }
    
    size_t calculateSize(const Value& value) {
        // Default size calculation - can be specialized
        return sizeof(Value);
    }
    
public:
    explicit LRUCache(size_t max_size = 1000, 
                     size_t max_memory = 100 * 1024 * 1024, // 100MB
                     std::chrono::seconds default_ttl = std::chrono::seconds(3600))
        : max_size_(max_size), max_memory_(max_memory), default_ttl_(default_ttl) {}
    
    bool get(const Key& key, Value& value) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = index_.find(key);
        if (it == index_.end()) {
            misses_++;
            return false;
        }
        
        auto& entry = it->second->second;
        if (entry.isExpired()) {
            current_memory_ -= entry.size_bytes;
            items_.erase(it->second);
            index_.erase(it);
            misses_++;
            return false;
        }
        
        // Move to front (most recently used)
        items_.splice(items_.begin(), items_, it->second);
        entry.updateAccess();
        value = entry.value;
        hits_++;
        return true;
    }
    
    void put(const Key& key, const Value& value, 
             std::chrono::seconds ttl = std::chrono::seconds(0)) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (ttl == std::chrono::seconds(0)) {
            ttl = default_ttl_;
        }
        
        auto now = std::chrono::steady_clock::now();
        size_t value_size = calculateSize(value);
        
        // Check if key already exists
        auto it = index_.find(key);
        if (it != index_.end()) {
            // Update existing entry
            auto& entry = it->second->second;
            current_memory_ = current_memory_ - entry.size_bytes + value_size;
            entry.value = value;
            entry.created_at = now;
            entry.expires_at = now + ttl;
            entry.last_accessed = now;
            entry.size_bytes = value_size;
            
            // Move to front
            items_.splice(items_.begin(), items_, it->second);
            return;
        }
        
        // Create new entry
        CacheEntryType entry;
        entry.value = value;
        entry.created_at = now;
        entry.expires_at = now + ttl;
        entry.last_accessed = now;
        entry.size_bytes = value_size;
        
        // Add to front
        items_.emplace_front(key, std::move(entry));
        index_[key] = items_.begin();
        current_memory_ += value_size;
        
        // Cleanup if necessary
        evictExpired();
        evictLRU();
    }
    
    bool remove(const Key& key) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = index_.find(key);
        if (it == index_.end()) {
            return false;
        }
        
        current_memory_ -= it->second->second.size_bytes;
        items_.erase(it->second);
        index_.erase(it);
        return true;
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        items_.clear();
        index_.clear();
        current_memory_ = 0;
    }
    
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return items_.size();
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return items_.empty();
    }
    
    // Statistics
    struct CacheStats {
        size_t hits;
        size_t misses;
        size_t evictions;
        size_t current_size;
        size_t current_memory;
        double hit_ratio;
        size_t max_size;
        size_t max_memory;
    };
    
    CacheStats getStats() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        CacheStats stats;
        stats.hits = hits_.load();
        stats.misses = misses_.load();
        stats.evictions = evictions_.load();
        stats.current_size = items_.size();
        stats.current_memory = current_memory_.load();
        stats.hit_ratio = (stats.hits + stats.misses > 0) ? 
                         static_cast<double>(stats.hits) / (stats.hits + stats.misses) : 0.0;
        stats.max_size = max_size_;
        stats.max_memory = max_memory_;
        
        return stats;
    }
    
    void resetStats() {
        hits_ = 0;
        misses_ = 0;
        evictions_ = 0;
    }
    
    // Configuration
    void setMaxSize(size_t max_size) {
        std::lock_guard<std::mutex> lock(mutex_);
        max_size_ = max_size;
        evictLRU();
    }
    
    void setMaxMemory(size_t max_memory) {
        std::lock_guard<std::mutex> lock(mutex_);
        max_memory_ = max_memory;
        evictLRU();
    }
    
    void setDefaultTTL(std::chrono::seconds ttl) {
        default_ttl_ = ttl;
    }
    
    // Iteration support
    std::vector<Key> getKeys() const {
        std::lock_guard<std::mutex> lock(mutex_);
        std::vector<Key> keys;
        keys.reserve(items_.size());
        
        for (const auto& item : items_) {
            if (!item.second.isExpired()) {
                keys.push_back(item.first);
            }
        }
        
        return keys;
    }
};

/**
 * @brief Multi-level cache with L1 (memory) and L2 (disk) storage
 */
template<typename Key, typename Value>
class MultiLevelCache {
private:
    std::unique_ptr<LRUCache<Key, Value>> l1_cache_;
    std::string l2_cache_dir_;
    
    std::shared_ptr<utilities::Logger> logger_;
    
    std::string getL2Path(const Key& key) const {
        std::hash<Key> hasher;
        auto hash = hasher(key);
        return l2_cache_dir_ + "/" + std::to_string(hash) + ".cache";
    }
    
    bool loadFromL2(const Key& key, Value& value) {
        try {
            std::string path = getL2Path(key);
            std::ifstream file(path, std::ios::binary);
            if (!file.is_open()) {
                return false;
            }
            
            // Simple binary serialization - would need proper implementation
            file.read(reinterpret_cast<char*>(&value), sizeof(Value));
            return file.good();
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->error("Failed to load from L2 cache: " + std::string(e.what()));
            }
            return false;
        }
    }
    
    void saveToL2(const Key& key, const Value& value) {
        try {
            std::string path = getL2Path(key);
            std::ofstream file(path, std::ios::binary);
            if (file.is_open()) {
                file.write(reinterpret_cast<const char*>(&value), sizeof(Value));
            }
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->error("Failed to save to L2 cache: " + std::string(e.what()));
            }
        }
    }
    
public:
    explicit MultiLevelCache(const std::string& l2_dir = "/tmp/cache",
                           size_t l1_max_size = 1000,
                           size_t l1_max_memory = 100 * 1024 * 1024)
        : l1_cache_(std::make_unique<LRUCache<Key, Value>>(l1_max_size, l1_max_memory)),
          l2_cache_dir_(l2_dir) {
        
        logger_ = utilities::Logger::getInstance();
        
        // Create L2 cache directory
        std::filesystem::create_directories(l2_cache_dir_);
    }
    
    bool get(const Key& key, Value& value) {
        // Try L1 cache first
        if (l1_cache_->get(key, value)) {
            return true;
        }
        
        // Try L2 cache
        if (loadFromL2(key, value)) {
            // Promote to L1
            l1_cache_->put(key, value);
            return true;
        }
        
        return false;
    }
    
    void put(const Key& key, const Value& value, std::chrono::seconds ttl = std::chrono::seconds(0)) {
        // Store in L1
        l1_cache_->put(key, value, ttl);
        
        // Store in L2
        saveToL2(key, value);
    }
    
    bool remove(const Key& key) {
        bool l1_removed = l1_cache_->remove(key);
        
        // Remove from L2
        std::string path = getL2Path(key);
        bool l2_removed = std::filesystem::remove(path);
        
        return l1_removed || l2_removed;
    }
    
    void clear() {
        l1_cache_->clear();
        
        // Clear L2 cache directory
        try {
            std::filesystem::remove_all(l2_cache_dir_);
            std::filesystem::create_directories(l2_cache_dir_);
        } catch (const std::exception& e) {
            if (logger_) {
                logger_->error("Failed to clear L2 cache: " + std::string(e.what()));
            }
        }
    }
    
    auto getL1Stats() const { return l1_cache_->getStats(); }
};

/**
 * @brief Cache service for application integration
 */
class CacheService : public core::BaseService {
private:
    std::unique_ptr<LRUCache<std::string, std::string>> string_cache_;
    std::unique_ptr<LRUCache<std::string, std::vector<uint8_t>>> binary_cache_;
    
    std::thread cleanup_thread_;
    std::atomic<bool> shutdown_{false};
    std::condition_variable cleanup_cv_;
    std::mutex cleanup_mutex_;
    
    void cleanupLoop();
    
protected:
    void doInitialize() override;
    void doShutdown() override;
    
public:
    CacheService();
    virtual ~CacheService();
    
    // String cache operations
    bool getString(const std::string& key, std::string& value);
    void putString(const std::string& key, const std::string& value, 
                  std::chrono::seconds ttl = std::chrono::seconds(0));
    bool removeString(const std::string& key);
    
    // Binary cache operations
    bool getBinary(const std::string& key, std::vector<uint8_t>& value);
    void putBinary(const std::string& key, const std::vector<uint8_t>& value,
                  std::chrono::seconds ttl = std::chrono::seconds(0));
    bool removeBinary(const std::string& key);
    
    // Cache management
    void clearAll();
    void clearExpired();
    
    // Statistics
    struct ServiceStats {
        typename LRUCache<std::string, std::string>::CacheStats string_stats;
        typename LRUCache<std::string, std::vector<uint8_t>>::CacheStats binary_stats;
    };
    
    ServiceStats getStats() const;
    std::string getStatsJson() const;
    
    // Configuration
    void configureStringCache(size_t max_size, size_t max_memory, std::chrono::seconds default_ttl);
    void configureBinaryCache(size_t max_size, size_t max_memory, std::chrono::seconds default_ttl);
};

} // namespace performance

#endif // PERFORMANCE_CACHE_H
