#include <iostream>
#include <string>
#include <map>
#include <vector>
#include <sstream> // Added for std::ostringstream

#include "server/HttpServer.h"
#include "server/Request.h"
#include "server/Response.h"
#include "server/Utils.h"
#include "server/Middleware.h"
#include "js_engine/Interpreter.h" // Include the interpreter
#include "security/SecureInterpreter.h" // Include the secure interpreter

int main() {
    HttpServer server(8080);
    Router& router = server.get_router();

    // Enable static file serving from the "public" directory
    server.enableStaticFiles("../public");

    // Define GET routes
    router.get("/", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/html";
        response.body = "<html><body><h1>🔒 C++ Web Server with Enterprise Security</h1><p>This is a C++ application with dual-mode JavaScript interpreter supporting both standard and enterprise execution.</p><p>Path: " + req.path + "</p><h2>🌐 Standard Features:</h2><ul><li>Visit <a href=\"/index.html\">/index.html</a> for a static page.</li><li>Try the <a href=\"/form.html\">form example</a>.</li><li>Run <a href=\"/run_js?code=let%20x%20%3D%2010%20%2B%205%3B%20print(x)%3B\">standard JS code</a>.</li><li>Test the <a href=\"/js_tester.html\">standard JS Interpreter</a>.</li></ul><h2>🔐 Enterprise Security Features:</h2><ul><li><strong><a href=\"/secure_js_tester.html\">🚀 Secure JS Interpreter Tester</a></strong> - Full-featured interface with dual-mode support</li><li>Try <a href=\"/run_js_secure?code=print('Hello%20Enterprise!');&secret_key=test_secret&enterprise_token=test_token\">secure JS execution</a> (requires credentials).</li><li>Test enterprise content with <a href=\"/run_js_secure?code=let%20data%20=%20getSecureData('user_info');%20print(data);&secret_key=test_secret&enterprise_token=test_token\">enterprise functions</a>.</li><li>Session-based execution via <code>/run_js_session</code> endpoint.</li></ul><h2>🔧 API Endpoints:</h2><ul><li><code>GET /run_js</code> - Standard JavaScript execution</li><li><code>GET /run_js_secure</code> - Enterprise JavaScript execution (requires secret_key & enterprise_token)</li><li><code>GET /run_js_session</code> - Session-based execution (requires session_id)</li></ul><p><strong>💡 Tip:</strong> Use the <a href=\"/secure_js_tester.html\">Secure JS Tester</a> for the best experience with both standard and enterprise features!</p></body></html>";
        return response;
    });

    router.get("/about", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/html";
        response.body = "<html><body><h1>About Us</h1><p>This is a simple C++ web server.</p></body></html>";
        return response;
    });

    router.get("/greet", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/html";
        
        std::string name = "Guest";
        auto it = req.query_params.find("name");
        if (it != req.query_params.end()) {
            name = it->second;
        }

        std::map<std::string, std::string> context;
        context["name"] = name;
        response.body = Utils::render_template("<html><body><h1>Hello, {{name}}!</h1><p>Welcome to our C++ web server.</p><p>Try: <a href=\"/greet?name=YourName\">/greet?name=YourName</a></p></body></html>", context);
        return response;
    });

    // New route for running JS code
    router.get("/run_js", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";

        // Check if real DOM is requested
        bool use_real_dom = false;
        auto real_dom_param = req.query_params.find("real_dom");
        if (real_dom_param != req.query_params.end() && real_dom_param->second == "true") {
            use_real_dom = true;
            response.headers["Content-Type"] = "application/json"; // Return JSON for real DOM
        } else {
            response.headers["Content-Type"] = "text/plain"; // Return plain text for mock DOM
        }

        std::cout << "[DEBUG] /run_js route called" << std::endl;
        std::cout << "[DEBUG] Query params count: " << req.query_params.size() << std::endl;
        for (const auto& param : req.query_params) {
            std::cout << "[DEBUG] Query param: '" << param.first << "' = '" << param.second << "'" << std::endl;
        }

        auto it = req.query_params.find("code");
        if (it != req.query_params.end()) {
            std::string js_code = it->second;
            Interpreter interpreter;
            std::string interpreter_error_output;
            std::string output_text;

            // Enable real DOM if requested
            if (use_real_dom) {
                interpreter.enable_real_dom(true);
                std::cout << "[DEBUG] Real DOM mode enabled" << std::endl;
            }

            // Define the print callback
            auto print_callback = [&](const std::string& text) {
                std::cout << "[DEBUG] Print callback called with: '" << text << "'" << std::endl;
                output_text += text;
            };

            try {
                std::cout << "[DEBUG] About to interpret code: '" << js_code << "'" << std::endl;
                std::cout << "[DEBUG] Code length: " << js_code.length() << std::endl;
                interpreter.interpret(js_code, print_callback);
                std::cout << "[DEBUG] Interpretation completed. Response body: '" << response.body << "'" << std::endl;
            } catch (const std::runtime_error& e) {
                std::cout << "[DEBUG] Runtime error: " << e.what() << std::endl;
                interpreter_error_output = "Error: " + std::string(e.what()) + "\n";
            } catch (const std::exception& e) {
                std::cout << "[DEBUG] Exception: " << e.what() << std::endl;
                interpreter_error_output = "Error: " + std::string(e.what()) + "\n";
            } catch (...) {
                std::cout << "[DEBUG] Unknown exception occurred" << std::endl;
                interpreter_error_output = "Error: Unknown exception\n";
            }

            if (!interpreter_error_output.empty()) {
                if (use_real_dom) {
                    // Return error as JSON
                    response.body = "{\"output\":\"" + interpreter_error_output + "\",\"dom_operations\":[],\"error\":true}";
                } else {
                    response.body = interpreter_error_output;
                }
            } else if (use_real_dom) {
                // Return output and DOM operations as JSON
                std::ostringstream json_response;
                json_response << "{\"output\":\"";

                // Escape quotes and newlines in output
                for (char c : output_text) {
                    if (c == '"') json_response << "\\\"";
                    else if (c == '\n') json_response << "\\n";
                    else if (c == '\r') json_response << "\\r";
                    else if (c == '\t') json_response << "\\t";
                    else if (c == '\\') json_response << "\\\\";
                    else json_response << c;
                }

                json_response << "\",\"dom_operations\":[";

                // Add DOM operations
                auto dom_ops = interpreter.get_dom_operations();
                for (size_t i = 0; i < dom_ops.size(); ++i) {
                    if (i > 0) json_response << ",";
                    json_response << "\"" << dom_ops[i] << "\"";
                }

                json_response << "],\"error\":false}";
                response.body = json_response.str();
            } else {
                response.body = output_text;
            }

        } else {
            if (use_real_dom) {
                response.body = "{\"output\":\"Error: No 'code' query parameter provided.\",\"dom_operations\":[],\"error\":true}";
            } else {
                response.body = "Error: No 'code' query parameter provided. Example: /run_js?code=let%20x%20%3D%2010%20%2B%205%3B%20print(x)%3B";
            }
        }
        return response;
    });

    // New secure route for enterprise JavaScript execution
    router.get("/run_js_secure", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "application/json";

        std::cout << "[DEBUG] /run_js_secure route called" << std::endl;

        // Extract required parameters
        auto code_param = req.query_params.find("code");
        auto secret_key_param = req.query_params.find("secret_key");
        auto enterprise_token_param = req.query_params.find("enterprise_token");

        if (code_param == req.query_params.end()) {
            response.body = "{\"output\":\"Error: No 'code' query parameter provided.\",\"dom_operations\":[],\"error\":true,\"security_level\":0}";
            return response;
        }

        if (secret_key_param == req.query_params.end() || enterprise_token_param == req.query_params.end()) {
            response.body = "{\"output\":\"Error: Missing security credentials. Both 'secret_key' and 'enterprise_token' are required.\",\"dom_operations\":[],\"error\":true,\"security_level\":0}";
            return response;
        }

        std::string js_code = code_param->second;
        std::string secret_key = secret_key_param->second;
        std::string enterprise_token = enterprise_token_param->second;

        // Optional parameters
        std::string client_id = "web_client";
        auto client_id_param = req.query_params.find("client_id");
        if (client_id_param != req.query_params.end()) {
            client_id = client_id_param->second;
        }

        bool use_real_dom = false;
        auto real_dom_param = req.query_params.find("real_dom");
        if (real_dom_param != req.query_params.end() && real_dom_param->second == "true") {
            use_real_dom = true;
        }

        try {
            // Initialize secure interpreter with default configuration
            security::SecurityConfig config;
            config.session_timeout = std::chrono::minutes(30);
            config.max_failed_attempts = 3;
            config.lockout_duration = std::chrono::minutes(15);

            security::SecureInterpreter secure_interpreter(config);

            // Initialize security with default organization
            if (!secure_interpreter.initializeSecurity(secret_key, enterprise_token, "DEFAULT_ORG")) {
                response.body = "{\"output\":\"Error: Failed to initialize security system.\",\"dom_operations\":[],\"error\":true,\"security_level\":0}";
                return response;
            }

            // Enable real DOM if requested
            if (use_real_dom) {
                secure_interpreter.getBaseInterpreter().enable_real_dom(true);
                std::cout << "[DEBUG] Real DOM mode enabled for secure execution" << std::endl;
            }

            std::string output_text;
            auto print_callback = [&](const std::string& text) {
                std::cout << "[DEBUG] Secure print callback: '" << text << "'" << std::endl;
                output_text += text;
            };

            // Execute secure interpretation
            security::SecureInterpretationResult result = secure_interpreter.interpretSecure(
                js_code, secret_key, enterprise_token, print_callback, client_id);

            // Build JSON response
            std::ostringstream json_response;
            json_response << "{\"output\":\"";

            // Escape quotes and newlines in output
            std::string output_to_escape = result.success ? result.output : result.error_message;
            for (char c : output_to_escape) {
                if (c == '"') json_response << "\\\"";
                else if (c == '\n') json_response << "\\n";
                else if (c == '\r') json_response << "\\r";
                else if (c == '\t') json_response << "\\t";
                else if (c == '\\') json_response << "\\\\";
                else json_response << c;
            }

            json_response << "\",\"dom_operations\":[";

            // Add DOM operations
            for (size_t i = 0; i < result.dom_operations.size(); ++i) {
                if (i > 0) json_response << ",";
                json_response << "\"" << result.dom_operations[i] << "\"";
            }

            json_response << "],\"error\":" << (result.success ? "false" : "true");
            json_response << ",\"security_level\":" << static_cast<int>(result.security_level);
            json_response << ",\"execution_mode\":" << static_cast<int>(result.execution_mode);
            json_response << ",\"session_id\":\"" << result.session_id << "\"";
            json_response << "}";

            response.body = json_response.str();

            std::cout << "[DEBUG] Secure execution completed. Success: " << result.success
                      << ", Security Level: " << static_cast<int>(result.security_level) << std::endl;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] Secure interpretation exception: " << e.what() << std::endl;
            response.body = "{\"output\":\"Error: " + std::string(e.what()) + "\",\"dom_operations\":[],\"error\":true,\"security_level\":0}";
        }

        return response;
    });

    // Enhanced route for session-based secure execution
    router.get("/run_js_session", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "application/json";

        std::cout << "[DEBUG] /run_js_session route called" << std::endl;

        // Extract required parameters
        auto code_param = req.query_params.find("code");
        auto session_id_param = req.query_params.find("session_id");

        if (code_param == req.query_params.end()) {
            response.body = "{\"output\":\"Error: No 'code' query parameter provided.\",\"dom_operations\":[],\"error\":true,\"security_level\":0}";
            return response;
        }

        if (session_id_param == req.query_params.end()) {
            response.body = "{\"output\":\"Error: No 'session_id' query parameter provided.\",\"dom_operations\":[],\"error\":true,\"security_level\":0}";
            return response;
        }

        std::string js_code = code_param->second;
        std::string session_id = session_id_param->second;

        bool use_real_dom = false;
        auto real_dom_param = req.query_params.find("real_dom");
        if (real_dom_param != req.query_params.end() && real_dom_param->second == "true") {
            use_real_dom = true;
        }

        try {
            // Note: In a real application, you would maintain a global secure interpreter instance
            // or a session manager. For this demo, we create a new instance each time.
            security::SecurityConfig config;
            security::SecureInterpreter secure_interpreter(config);

            // For session-based execution, we assume security was already initialized
            // In a real application, you would retrieve the interpreter instance from a session store

            if (use_real_dom) {
                secure_interpreter.getBaseInterpreter().enable_real_dom(true);
            }

            std::string output_text;
            auto print_callback = [&](const std::string& text) {
                output_text += text;
            };

            // Execute with session
            security::SecureInterpretationResult result = secure_interpreter.interpretWithSession(
                js_code, session_id, print_callback);

            // Build JSON response (similar to secure endpoint)
            std::ostringstream json_response;
            json_response << "{\"output\":\"";

            std::string output_to_escape = result.success ? result.output : result.error_message;
            for (char c : output_to_escape) {
                if (c == '"') json_response << "\\\"";
                else if (c == '\n') json_response << "\\n";
                else if (c == '\r') json_response << "\\r";
                else if (c == '\t') json_response << "\\t";
                else if (c == '\\') json_response << "\\\\";
                else json_response << c;
            }

            json_response << "\",\"dom_operations\":[";
            for (size_t i = 0; i < result.dom_operations.size(); ++i) {
                if (i > 0) json_response << ",";
                json_response << "\"" << result.dom_operations[i] << "\"";
            }

            json_response << "],\"error\":" << (result.success ? "false" : "true");
            json_response << ",\"security_level\":" << static_cast<int>(result.security_level);
            json_response << ",\"execution_mode\":" << static_cast<int>(result.execution_mode);
            json_response << ",\"session_id\":\"" << result.session_id << "\"";
            json_response << "}";

            response.body = json_response.str();

        } catch (const std::exception& e) {
            response.body = "{\"output\":\"Error: " + std::string(e.what()) + "\",\"dom_operations\":[],\"error\":true,\"security_level\":0}";
        }

        return response;
    });

    // Define POST routes
    router.post("/submit_form", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/html";
        
        std::ostringstream oss;
        oss << "<html><body><h1>Form Submission Received!</h1>";
        oss << "<p>You submitted:</p><ul>";
        for (const auto& pair : req.form_data) {
            oss << "<li><strong>" << pair.first << ":</strong> " << pair.second << "</li>";
        }
        oss << "</ul><p>Go back to <a href=\"/form.html\">form</a> or <a href=\"/\">home</a>.</p></body></html>";
        response.body = oss.str();
        return response;
    });

    server.start();

    return 0;
}
