#ifndef JAVASCRIPT_ENHANCED_JS_ENGINE_H
#define JAVASCRIPT_ENHANCED_JS_ENGINE_H

#include <string>
#include <memory>
#include <unordered_map>
#include <vector>
#include <functional>
#include <chrono>
#include <future>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <variant>
#include "../utilities/Logger.h"
#include "../core/Application.h"

namespace javascript {

/**
 * @brief JavaScript value types
 */
using JSValue = std::variant<
    std::nullptr_t,
    bool,
    int64_t,
    double,
    std::string,
    std::vector<JSValue>,
    std::unordered_map<std::string, JSValue>
>;

/**
 * @brief JavaScript execution context
 */
struct JSContext {
    std::unordered_map<std::string, JSValue> global_variables;
    std::unordered_map<std::string, std::function<JSValue(const std::vector<JSValue>&)>> native_functions;
    std::vector<std::string> module_paths;
    std::unordered_map<std::string, JSValue> module_cache;
    std::chrono::milliseconds execution_timeout{5000};
    size_t max_memory_usage{100 * 1024 * 1024}; // 100MB
    size_t max_call_stack_depth{1000};
    bool strict_mode{false};
};

/**
 * @brief JavaScript execution result
 */
struct JSResult {
    bool success{false};
    JSValue result;
    std::string error_message;
    std::string stack_trace;
    std::chrono::milliseconds execution_time{0};
    size_t memory_used{0};
    
    std::string toJson() const;
    static JSResult fromError(const std::string& error, const std::string& stack = "");
    static JSResult fromValue(const JSValue& value, std::chrono::milliseconds exec_time = std::chrono::milliseconds{0});
};

/**
 * @brief JavaScript module system
 */
class JSModule {
private:
    std::string name_;
    std::string source_code_;
    std::unordered_map<std::string, JSValue> exports_;
    std::vector<std::string> dependencies_;
    bool loaded_{false};
    
public:
    explicit JSModule(const std::string& name, const std::string& source = "");
    
    const std::string& getName() const { return name_; }
    const std::string& getSourceCode() const { return source_code_; }
    const std::unordered_map<std::string, JSValue>& getExports() const { return exports_; }
    const std::vector<std::string>& getDependencies() const { return dependencies_; }
    
    void setSourceCode(const std::string& source);
    void addExport(const std::string& name, const JSValue& value);
    void addDependency(const std::string& module_name);
    
    bool isLoaded() const { return loaded_; }
    void markLoaded() { loaded_ = true; }
    
    std::string toJson() const;
};

/**
 * @brief JavaScript module loader
 */
class JSModuleLoader {
private:
    std::unordered_map<std::string, std::unique_ptr<JSModule>> modules_;
    std::vector<std::string> search_paths_;
    std::shared_ptr<utilities::Logger> logger_;
    
    std::string resolveModulePath(const std::string& module_name);
    std::string loadFileContent(const std::string& file_path);
    
public:
    JSModuleLoader();
    
    void addSearchPath(const std::string& path);
    void removeSearchPath(const std::string& path);
    const std::vector<std::string>& getSearchPaths() const { return search_paths_; }
    
    bool loadModule(const std::string& module_name);
    bool loadModuleFromFile(const std::string& module_name, const std::string& file_path);
    bool loadModuleFromString(const std::string& module_name, const std::string& source_code);
    
    JSModule* getModule(const std::string& module_name);
    const JSModule* getModule(const std::string& module_name) const;
    
    std::vector<std::string> getLoadedModules() const;
    void unloadModule(const std::string& module_name);
    void unloadAllModules();
    
    // Dependency resolution
    std::vector<std::string> resolveDependencies(const std::string& module_name);
    bool hasCircularDependency(const std::string& module_name);
};

/**
 * @brief Asynchronous JavaScript execution
 */
class JSAsyncExecutor {
private:
    struct AsyncTask {
        std::string code;
        JSContext context;
        std::promise<JSResult> promise;
        std::chrono::steady_clock::time_point created_at;
        std::string task_id;
    };
    
    std::queue<std::unique_ptr<AsyncTask>> task_queue_;
    std::unordered_map<std::string, std::unique_ptr<AsyncTask>> running_tasks_;
    std::vector<std::thread> worker_threads_;
    
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::atomic<bool> shutdown_{false};
    
    std::shared_ptr<utilities::Logger> logger_;
    
    void workerLoop();
    std::string generateTaskId();
    
public:
    explicit JSAsyncExecutor(size_t worker_count = std::thread::hardware_concurrency());
    ~JSAsyncExecutor();
    
    std::future<JSResult> executeAsync(const std::string& code, const JSContext& context = JSContext{});
    bool cancelTask(const std::string& task_id);
    
    size_t getQueueSize() const;
    size_t getRunningTaskCount() const;
    std::vector<std::string> getRunningTaskIds() const;
    
    void shutdown();
};

/**
 * @brief Enhanced JavaScript engine with advanced features
 */
class EnhancedJSEngine : public core::BaseService {
private:
    JSContext default_context_;
    std::unique_ptr<JSModuleLoader> module_loader_;
    std::unique_ptr<JSAsyncExecutor> async_executor_;
    
    std::shared_ptr<utilities::Logger> logger_;
    
    // Built-in functions
    void registerBuiltinFunctions();
    void registerConsoleAPI();
    void registerTimerAPI();
    void registerFileSystemAPI();
    void registerHttpAPI();
    void registerCryptoAPI();
    void registerUtilityAPI();
    
    // Execution helpers
    JSResult executeWithTimeout(const std::string& code, const JSContext& context);
    JSValue parseValue(const std::string& value_str);
    std::string valueToString(const JSValue& value);
    
protected:
    void doInitialize() override;
    void doShutdown() override;
    
public:
    EnhancedJSEngine();
    virtual ~EnhancedJSEngine();
    
    // Synchronous execution
    JSResult execute(const std::string& code);
    JSResult execute(const std::string& code, const JSContext& context);
    
    // Asynchronous execution
    std::future<JSResult> executeAsync(const std::string& code);
    std::future<JSResult> executeAsync(const std::string& code, const JSContext& context);
    
    // Context management
    JSContext& getDefaultContext() { return default_context_; }
    const JSContext& getDefaultContext() const { return default_context_; }
    void setDefaultContext(const JSContext& context);
    
    // Variable management
    void setGlobalVariable(const std::string& name, const JSValue& value);
    JSValue getGlobalVariable(const std::string& name);
    bool hasGlobalVariable(const std::string& name);
    void removeGlobalVariable(const std::string& name);
    void clearGlobalVariables();
    
    // Function registration
    void registerFunction(const std::string& name, std::function<JSValue(const std::vector<JSValue>&)> func);
    void unregisterFunction(const std::string& name);
    bool hasFunction(const std::string& name);
    
    // Module system
    JSModuleLoader& getModuleLoader() { return *module_loader_; }
    const JSModuleLoader& getModuleLoader() const { return *module_loader_; }
    
    bool loadModule(const std::string& module_name);
    bool loadModuleFromFile(const std::string& module_name, const std::string& file_path);
    bool loadModuleFromString(const std::string& module_name, const std::string& source_code);
    
    // Configuration
    void setTimeout(std::chrono::milliseconds timeout);
    void setMaxMemoryUsage(size_t max_memory);
    void setMaxCallStackDepth(size_t max_depth);
    void setStrictMode(bool strict);
    
    // Utility methods
    std::string formatError(const std::string& error, const std::string& code, size_t line = 0, size_t column = 0);
    std::vector<std::string> getAvailableFunctions() const;
    std::vector<std::string> getGlobalVariableNames() const;
    
    // Statistics
    struct EngineStats {
        size_t total_executions{0};
        size_t successful_executions{0};
        size_t failed_executions{0};
        std::chrono::milliseconds total_execution_time{0};
        std::chrono::milliseconds average_execution_time{0};
        size_t peak_memory_usage{0};
        size_t loaded_modules{0};
    };
    
    EngineStats getStats() const;
    void resetStats();
    std::string getStatsJson() const;
};

/**
 * @brief JavaScript engine service for application integration
 */
class JSEngineService : public core::BaseService {
private:
    std::unique_ptr<EnhancedJSEngine> engine_;
    
protected:
    void doInitialize() override;
    void doShutdown() override;
    
public:
    JSEngineService();
    
    EnhancedJSEngine& getEngine() { return *engine_; }
    const EnhancedJSEngine& getEngine() const { return *engine_; }
    
    // Convenience methods
    JSResult executeScript(const std::string& code);
    std::future<JSResult> executeScriptAsync(const std::string& code);
    
    void loadBuiltinModules();
    void configureForWebServer();
};

/**
 * @brief JavaScript value utilities
 */
class JSValueUtils {
public:
    // Type checking
    static bool isNull(const JSValue& value);
    static bool isBool(const JSValue& value);
    static bool isNumber(const JSValue& value);
    static bool isString(const JSValue& value);
    static bool isArray(const JSValue& value);
    static bool isObject(const JSValue& value);
    
    // Type conversion
    static bool toBool(const JSValue& value);
    static int64_t toInt(const JSValue& value);
    static double toDouble(const JSValue& value);
    static std::string toString(const JSValue& value);
    static std::vector<JSValue> toArray(const JSValue& value);
    static std::unordered_map<std::string, JSValue> toObject(const JSValue& value);
    
    // JSON serialization
    static std::string toJson(const JSValue& value, bool pretty = false);
    static JSValue fromJson(const std::string& json);
    
    // Comparison
    static bool equals(const JSValue& a, const JSValue& b);
    static bool strictEquals(const JSValue& a, const JSValue& b);
    
    // Utility
    static size_t getSize(const JSValue& value);
    static std::string getTypeName(const JSValue& value);
    static JSValue deepCopy(const JSValue& value);
    static void merge(JSValue& target, const JSValue& source);
};

/**
 * @brief JavaScript error types
 */
class JSError : public std::exception {
private:
    std::string message_;
    std::string stack_trace_;
    size_t line_;
    size_t column_;
    
public:
    JSError(const std::string& message, size_t line = 0, size_t column = 0, const std::string& stack = "");
    
    const char* what() const noexcept override { return message_.c_str(); }
    const std::string& getMessage() const { return message_; }
    const std::string& getStackTrace() const { return stack_trace_; }
    size_t getLine() const { return line_; }
    size_t getColumn() const { return column_; }
    
    std::string toString() const;
};

class JSSyntaxError : public JSError {
public:
    JSSyntaxError(const std::string& message, size_t line = 0, size_t column = 0);
};

class JSRuntimeError : public JSError {
public:
    JSRuntimeError(const std::string& message, const std::string& stack = "");
};

class JSTimeoutError : public JSError {
public:
    JSTimeoutError(std::chrono::milliseconds timeout);
};

class JSMemoryError : public JSError {
public:
    JSMemoryError(size_t used, size_t limit);
};

} // namespace javascript

#endif // JAVASCRIPT_ENHANCED_JS_ENGINE_H
