# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles /home/<USER>/test_google_cli/test2/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named utilities

# Build rule for target.
utilities: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 utilities
.PHONY : utilities

# fast build rule for target.
utilities/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/build
.PHONY : utilities/fast

#=============================================================================
# Target rules for targets named core

# Build rule for target.
core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core
.PHONY : core

# fast build rule for target.
core/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/build
.PHONY : core/fast

#=============================================================================
# Target rules for targets named server

# Build rule for target.
server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 server
.PHONY : server

# fast build rule for target.
server/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/build
.PHONY : server/fast

#=============================================================================
# Target rules for targets named js_engine

# Build rule for target.
js_engine: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 js_engine
.PHONY : js_engine

# fast build rule for target.
js_engine/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/build
.PHONY : js_engine/fast

#=============================================================================
# Target rules for targets named security

# Build rule for target.
security: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 security
.PHONY : security

# fast build rule for target.
security/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/build
.PHONY : security/fast

#=============================================================================
# Target rules for targets named webserver

# Build rule for target.
webserver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 webserver
.PHONY : webserver

# fast build rule for target.
webserver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/webserver.dir/build.make CMakeFiles/webserver.dir/build
.PHONY : webserver/fast

#=============================================================================
# Target rules for targets named test_server

# Build rule for target.
test_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_server
.PHONY : test_server

# fast build rule for target.
test_server/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_server.dir/build.make CMakeFiles/test_server.dir/build
.PHONY : test_server/fast

core/Application.o: core/Application.cpp.o
.PHONY : core/Application.o

# target to build an object file
core/Application.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/core/Application.cpp.o
.PHONY : core/Application.cpp.o

core/Application.i: core/Application.cpp.i
.PHONY : core/Application.i

# target to preprocess a source file
core/Application.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/core/Application.cpp.i
.PHONY : core/Application.cpp.i

core/Application.s: core/Application.cpp.s
.PHONY : core/Application.s

# target to generate assembly for a file
core/Application.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/core/Application.cpp.s
.PHONY : core/Application.cpp.s

core/ThreadPool.o: core/ThreadPool.cpp.o
.PHONY : core/ThreadPool.o

# target to build an object file
core/ThreadPool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/core/ThreadPool.cpp.o
.PHONY : core/ThreadPool.cpp.o

core/ThreadPool.i: core/ThreadPool.cpp.i
.PHONY : core/ThreadPool.i

# target to preprocess a source file
core/ThreadPool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/core/ThreadPool.cpp.i
.PHONY : core/ThreadPool.cpp.i

core/ThreadPool.s: core/ThreadPool.cpp.s
.PHONY : core/ThreadPool.s

# target to generate assembly for a file
core/ThreadPool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/core/ThreadPool.cpp.s
.PHONY : core/ThreadPool.cpp.s

js_engine/Interpreter.o: js_engine/Interpreter.cpp.o
.PHONY : js_engine/Interpreter.o

# target to build an object file
js_engine/Interpreter.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/js_engine/Interpreter.cpp.o
.PHONY : js_engine/Interpreter.cpp.o

js_engine/Interpreter.i: js_engine/Interpreter.cpp.i
.PHONY : js_engine/Interpreter.i

# target to preprocess a source file
js_engine/Interpreter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/js_engine/Interpreter.cpp.i
.PHONY : js_engine/Interpreter.cpp.i

js_engine/Interpreter.s: js_engine/Interpreter.cpp.s
.PHONY : js_engine/Interpreter.s

# target to generate assembly for a file
js_engine/Interpreter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/js_engine/Interpreter.cpp.s
.PHONY : js_engine/Interpreter.cpp.s

js_engine/Tokenizer.o: js_engine/Tokenizer.cpp.o
.PHONY : js_engine/Tokenizer.o

# target to build an object file
js_engine/Tokenizer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/js_engine/Tokenizer.cpp.o
.PHONY : js_engine/Tokenizer.cpp.o

js_engine/Tokenizer.i: js_engine/Tokenizer.cpp.i
.PHONY : js_engine/Tokenizer.i

# target to preprocess a source file
js_engine/Tokenizer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/js_engine/Tokenizer.cpp.i
.PHONY : js_engine/Tokenizer.cpp.i

js_engine/Tokenizer.s: js_engine/Tokenizer.cpp.s
.PHONY : js_engine/Tokenizer.s

# target to generate assembly for a file
js_engine/Tokenizer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/js_engine/Tokenizer.cpp.s
.PHONY : js_engine/Tokenizer.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/webserver.dir/build.make CMakeFiles/webserver.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/webserver.dir/build.make CMakeFiles/webserver.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/webserver.dir/build.make CMakeFiles/webserver.dir/main.cpp.s
.PHONY : main.cpp.s

security/EnterpriseContentHandler.o: security/EnterpriseContentHandler.cpp.o
.PHONY : security/EnterpriseContentHandler.o

# target to build an object file
security/EnterpriseContentHandler.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o
.PHONY : security/EnterpriseContentHandler.cpp.o

security/EnterpriseContentHandler.i: security/EnterpriseContentHandler.cpp.i
.PHONY : security/EnterpriseContentHandler.i

# target to preprocess a source file
security/EnterpriseContentHandler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.i
.PHONY : security/EnterpriseContentHandler.cpp.i

security/EnterpriseContentHandler.s: security/EnterpriseContentHandler.cpp.s
.PHONY : security/EnterpriseContentHandler.s

# target to generate assembly for a file
security/EnterpriseContentHandler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.s
.PHONY : security/EnterpriseContentHandler.cpp.s

security/SecureInterpreter.o: security/SecureInterpreter.cpp.o
.PHONY : security/SecureInterpreter.o

# target to build an object file
security/SecureInterpreter.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/SecureInterpreter.cpp.o
.PHONY : security/SecureInterpreter.cpp.o

security/SecureInterpreter.i: security/SecureInterpreter.cpp.i
.PHONY : security/SecureInterpreter.i

# target to preprocess a source file
security/SecureInterpreter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/SecureInterpreter.cpp.i
.PHONY : security/SecureInterpreter.cpp.i

security/SecureInterpreter.s: security/SecureInterpreter.cpp.s
.PHONY : security/SecureInterpreter.s

# target to generate assembly for a file
security/SecureInterpreter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/SecureInterpreter.cpp.s
.PHONY : security/SecureInterpreter.cpp.s

security/SecurityManager.o: security/SecurityManager.cpp.o
.PHONY : security/SecurityManager.o

# target to build an object file
security/SecurityManager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/SecurityManager.cpp.o
.PHONY : security/SecurityManager.cpp.o

security/SecurityManager.i: security/SecurityManager.cpp.i
.PHONY : security/SecurityManager.i

# target to preprocess a source file
security/SecurityManager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/SecurityManager.cpp.i
.PHONY : security/SecurityManager.cpp.i

security/SecurityManager.s: security/SecurityManager.cpp.s
.PHONY : security/SecurityManager.s

# target to generate assembly for a file
security/SecurityManager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/security/SecurityManager.cpp.s
.PHONY : security/SecurityManager.cpp.s

server/HttpServer.o: server/HttpServer.cpp.o
.PHONY : server/HttpServer.o

# target to build an object file
server/HttpServer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/HttpServer.cpp.o
.PHONY : server/HttpServer.cpp.o

server/HttpServer.i: server/HttpServer.cpp.i
.PHONY : server/HttpServer.i

# target to preprocess a source file
server/HttpServer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/HttpServer.cpp.i
.PHONY : server/HttpServer.cpp.i

server/HttpServer.s: server/HttpServer.cpp.s
.PHONY : server/HttpServer.s

# target to generate assembly for a file
server/HttpServer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/HttpServer.cpp.s
.PHONY : server/HttpServer.cpp.s

server/Middleware.o: server/Middleware.cpp.o
.PHONY : server/Middleware.o

# target to build an object file
server/Middleware.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Middleware.cpp.o
.PHONY : server/Middleware.cpp.o

server/Middleware.i: server/Middleware.cpp.i
.PHONY : server/Middleware.i

# target to preprocess a source file
server/Middleware.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Middleware.cpp.i
.PHONY : server/Middleware.cpp.i

server/Middleware.s: server/Middleware.cpp.s
.PHONY : server/Middleware.s

# target to generate assembly for a file
server/Middleware.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Middleware.cpp.s
.PHONY : server/Middleware.cpp.s

server/Request.o: server/Request.cpp.o
.PHONY : server/Request.o

# target to build an object file
server/Request.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Request.cpp.o
.PHONY : server/Request.cpp.o

server/Request.i: server/Request.cpp.i
.PHONY : server/Request.i

# target to preprocess a source file
server/Request.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Request.cpp.i
.PHONY : server/Request.cpp.i

server/Request.s: server/Request.cpp.s
.PHONY : server/Request.s

# target to generate assembly for a file
server/Request.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Request.cpp.s
.PHONY : server/Request.cpp.s

server/Response.o: server/Response.cpp.o
.PHONY : server/Response.o

# target to build an object file
server/Response.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Response.cpp.o
.PHONY : server/Response.cpp.o

server/Response.i: server/Response.cpp.i
.PHONY : server/Response.i

# target to preprocess a source file
server/Response.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Response.cpp.i
.PHONY : server/Response.cpp.i

server/Response.s: server/Response.cpp.s
.PHONY : server/Response.s

# target to generate assembly for a file
server/Response.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Response.cpp.s
.PHONY : server/Response.cpp.s

server/Router.o: server/Router.cpp.o
.PHONY : server/Router.o

# target to build an object file
server/Router.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Router.cpp.o
.PHONY : server/Router.cpp.o

server/Router.i: server/Router.cpp.i
.PHONY : server/Router.i

# target to preprocess a source file
server/Router.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Router.cpp.i
.PHONY : server/Router.cpp.i

server/Router.s: server/Router.cpp.s
.PHONY : server/Router.s

# target to generate assembly for a file
server/Router.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Router.cpp.s
.PHONY : server/Router.cpp.s

server/Utils.o: server/Utils.cpp.o
.PHONY : server/Utils.o

# target to build an object file
server/Utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Utils.cpp.o
.PHONY : server/Utils.cpp.o

server/Utils.i: server/Utils.cpp.i
.PHONY : server/Utils.i

# target to preprocess a source file
server/Utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Utils.cpp.i
.PHONY : server/Utils.cpp.i

server/Utils.s: server/Utils.cpp.s
.PHONY : server/Utils.s

# target to generate assembly for a file
server/Utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/server/Utils.cpp.s
.PHONY : server/Utils.cpp.s

test_server.o: test_server.cpp.o
.PHONY : test_server.o

# target to build an object file
test_server.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_server.dir/build.make CMakeFiles/test_server.dir/test_server.cpp.o
.PHONY : test_server.cpp.o

test_server.i: test_server.cpp.i
.PHONY : test_server.i

# target to preprocess a source file
test_server.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_server.dir/build.make CMakeFiles/test_server.dir/test_server.cpp.i
.PHONY : test_server.cpp.i

test_server.s: test_server.cpp.s
.PHONY : test_server.s

# target to generate assembly for a file
test_server.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_server.dir/build.make CMakeFiles/test_server.dir/test_server.cpp.s
.PHONY : test_server.cpp.s

utilities/Config.o: utilities/Config.cpp.o
.PHONY : utilities/Config.o

# target to build an object file
utilities/Config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/Config.cpp.o
.PHONY : utilities/Config.cpp.o

utilities/Config.i: utilities/Config.cpp.i
.PHONY : utilities/Config.i

# target to preprocess a source file
utilities/Config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/Config.cpp.i
.PHONY : utilities/Config.cpp.i

utilities/Config.s: utilities/Config.cpp.s
.PHONY : utilities/Config.s

# target to generate assembly for a file
utilities/Config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/Config.cpp.s
.PHONY : utilities/Config.cpp.s

utilities/ErrorHandler.o: utilities/ErrorHandler.cpp.o
.PHONY : utilities/ErrorHandler.o

# target to build an object file
utilities/ErrorHandler.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/ErrorHandler.cpp.o
.PHONY : utilities/ErrorHandler.cpp.o

utilities/ErrorHandler.i: utilities/ErrorHandler.cpp.i
.PHONY : utilities/ErrorHandler.i

# target to preprocess a source file
utilities/ErrorHandler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/ErrorHandler.cpp.i
.PHONY : utilities/ErrorHandler.cpp.i

utilities/ErrorHandler.s: utilities/ErrorHandler.cpp.s
.PHONY : utilities/ErrorHandler.s

# target to generate assembly for a file
utilities/ErrorHandler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/ErrorHandler.cpp.s
.PHONY : utilities/ErrorHandler.cpp.s

utilities/Logger.o: utilities/Logger.cpp.o
.PHONY : utilities/Logger.o

# target to build an object file
utilities/Logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/Logger.cpp.o
.PHONY : utilities/Logger.cpp.o

utilities/Logger.i: utilities/Logger.cpp.i
.PHONY : utilities/Logger.i

# target to preprocess a source file
utilities/Logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/Logger.cpp.i
.PHONY : utilities/Logger.cpp.i

utilities/Logger.s: utilities/Logger.cpp.s
.PHONY : utilities/Logger.s

# target to generate assembly for a file
utilities/Logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/utilities/Logger.cpp.s
.PHONY : utilities/Logger.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... core"
	@echo "... js_engine"
	@echo "... security"
	@echo "... server"
	@echo "... test_server"
	@echo "... utilities"
	@echo "... webserver"
	@echo "... core/Application.o"
	@echo "... core/Application.i"
	@echo "... core/Application.s"
	@echo "... core/ThreadPool.o"
	@echo "... core/ThreadPool.i"
	@echo "... core/ThreadPool.s"
	@echo "... js_engine/Interpreter.o"
	@echo "... js_engine/Interpreter.i"
	@echo "... js_engine/Interpreter.s"
	@echo "... js_engine/Tokenizer.o"
	@echo "... js_engine/Tokenizer.i"
	@echo "... js_engine/Tokenizer.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... security/EnterpriseContentHandler.o"
	@echo "... security/EnterpriseContentHandler.i"
	@echo "... security/EnterpriseContentHandler.s"
	@echo "... security/SecureInterpreter.o"
	@echo "... security/SecureInterpreter.i"
	@echo "... security/SecureInterpreter.s"
	@echo "... security/SecurityManager.o"
	@echo "... security/SecurityManager.i"
	@echo "... security/SecurityManager.s"
	@echo "... server/HttpServer.o"
	@echo "... server/HttpServer.i"
	@echo "... server/HttpServer.s"
	@echo "... server/Middleware.o"
	@echo "... server/Middleware.i"
	@echo "... server/Middleware.s"
	@echo "... server/Request.o"
	@echo "... server/Request.i"
	@echo "... server/Request.s"
	@echo "... server/Response.o"
	@echo "... server/Response.i"
	@echo "... server/Response.s"
	@echo "... server/Router.o"
	@echo "... server/Router.i"
	@echo "... server/Router.s"
	@echo "... server/Utils.o"
	@echo "... server/Utils.i"
	@echo "... server/Utils.s"
	@echo "... test_server.o"
	@echo "... test_server.i"
	@echo "... test_server.s"
	@echo "... utilities/Config.o"
	@echo "... utilities/Config.i"
	@echo "... utilities/Config.s"
	@echo "... utilities/ErrorHandler.o"
	@echo "... utilities/ErrorHandler.i"
	@echo "... utilities/ErrorHandler.s"
	@echo "... utilities/Logger.o"
	@echo "... utilities/Logger.i"
	@echo "... utilities/Logger.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

