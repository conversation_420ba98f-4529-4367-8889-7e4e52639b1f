
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/test_google_cli/test2/js_engine/Interpreter.cpp" "CMakeFiles/js_engine.dir/js_engine/Interpreter.cpp.o" "gcc" "CMakeFiles/js_engine.dir/js_engine/Interpreter.cpp.o.d"
  "/home/<USER>/test_google_cli/test2/js_engine/Tokenizer.cpp" "CMakeFiles/js_engine.dir/js_engine/Tokenizer.cpp.o" "gcc" "CMakeFiles/js_engine.dir/js_engine/Tokenizer.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/test_google_cli/test2/build/CMakeFiles/utilities.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
