# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/build

# Include any dependencies generated for this target.
include CMakeFiles/security.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/security.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/security.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/security.dir/flags.make

CMakeFiles/security.dir/security/SecurityManager.cpp.o: CMakeFiles/security.dir/flags.make
CMakeFiles/security.dir/security/SecurityManager.cpp.o: /home/<USER>/test_google_cli/test2/security/SecurityManager.cpp
CMakeFiles/security.dir/security/SecurityManager.cpp.o: CMakeFiles/security.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/security.dir/security/SecurityManager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/security.dir/security/SecurityManager.cpp.o -MF CMakeFiles/security.dir/security/SecurityManager.cpp.o.d -o CMakeFiles/security.dir/security/SecurityManager.cpp.o -c /home/<USER>/test_google_cli/test2/security/SecurityManager.cpp

CMakeFiles/security.dir/security/SecurityManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/security.dir/security/SecurityManager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/security/SecurityManager.cpp > CMakeFiles/security.dir/security/SecurityManager.cpp.i

CMakeFiles/security.dir/security/SecurityManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/security.dir/security/SecurityManager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/security/SecurityManager.cpp -o CMakeFiles/security.dir/security/SecurityManager.cpp.s

CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o: CMakeFiles/security.dir/flags.make
CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o: /home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp
CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o: CMakeFiles/security.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o -MF CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o.d -o CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o -c /home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp

CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp > CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.i

CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/security/EnterpriseContentHandler.cpp -o CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.s

CMakeFiles/security.dir/security/SecureInterpreter.cpp.o: CMakeFiles/security.dir/flags.make
CMakeFiles/security.dir/security/SecureInterpreter.cpp.o: /home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp
CMakeFiles/security.dir/security/SecureInterpreter.cpp.o: CMakeFiles/security.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/security.dir/security/SecureInterpreter.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/security.dir/security/SecureInterpreter.cpp.o -MF CMakeFiles/security.dir/security/SecureInterpreter.cpp.o.d -o CMakeFiles/security.dir/security/SecureInterpreter.cpp.o -c /home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp

CMakeFiles/security.dir/security/SecureInterpreter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/security.dir/security/SecureInterpreter.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp > CMakeFiles/security.dir/security/SecureInterpreter.cpp.i

CMakeFiles/security.dir/security/SecureInterpreter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/security.dir/security/SecureInterpreter.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/security/SecureInterpreter.cpp -o CMakeFiles/security.dir/security/SecureInterpreter.cpp.s

# Object files for target security
security_OBJECTS = \
"CMakeFiles/security.dir/security/SecurityManager.cpp.o" \
"CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o" \
"CMakeFiles/security.dir/security/SecureInterpreter.cpp.o"

# External object files for target security
security_EXTERNAL_OBJECTS =

libsecurity.a: CMakeFiles/security.dir/security/SecurityManager.cpp.o
libsecurity.a: CMakeFiles/security.dir/security/EnterpriseContentHandler.cpp.o
libsecurity.a: CMakeFiles/security.dir/security/SecureInterpreter.cpp.o
libsecurity.a: CMakeFiles/security.dir/build.make
libsecurity.a: CMakeFiles/security.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library libsecurity.a"
	$(CMAKE_COMMAND) -P CMakeFiles/security.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/security.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/security.dir/build: libsecurity.a
.PHONY : CMakeFiles/security.dir/build

CMakeFiles/security.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/security.dir/cmake_clean.cmake
.PHONY : CMakeFiles/security.dir/clean

CMakeFiles/security.dir/depend:
	cd /home/<USER>/test_google_cli/test2/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test2 /home/<USER>/test_google_cli/test2 /home/<USER>/test_google_cli/test2/build /home/<USER>/test_google_cli/test2/build /home/<USER>/test_google_cli/test2/build/CMakeFiles/security.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/security.dir/depend

