
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/test_google_cli/test2/server/HttpServer.cpp" "CMakeFiles/server.dir/server/HttpServer.cpp.o" "gcc" "CMakeFiles/server.dir/server/HttpServer.cpp.o.d"
  "/home/<USER>/test_google_cli/test2/server/Middleware.cpp" "CMakeFiles/server.dir/server/Middleware.cpp.o" "gcc" "CMakeFiles/server.dir/server/Middleware.cpp.o.d"
  "/home/<USER>/test_google_cli/test2/server/Request.cpp" "CMakeFiles/server.dir/server/Request.cpp.o" "gcc" "CMakeFiles/server.dir/server/Request.cpp.o.d"
  "/home/<USER>/test_google_cli/test2/server/Response.cpp" "CMakeFiles/server.dir/server/Response.cpp.o" "gcc" "CMakeFiles/server.dir/server/Response.cpp.o.d"
  "/home/<USER>/test_google_cli/test2/server/Router.cpp" "CMakeFiles/server.dir/server/Router.cpp.o" "gcc" "CMakeFiles/server.dir/server/Router.cpp.o.d"
  "/home/<USER>/test_google_cli/test2/server/Utils.cpp" "CMakeFiles/server.dir/server/Utils.cpp.o" "gcc" "CMakeFiles/server.dir/server/Utils.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/test_google_cli/test2/build/CMakeFiles/core.dir/DependInfo.cmake"
  "/home/<USER>/test_google_cli/test2/build/CMakeFiles/utilities.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
