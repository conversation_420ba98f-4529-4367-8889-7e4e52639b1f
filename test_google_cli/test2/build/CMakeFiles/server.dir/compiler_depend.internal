# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

CMakeFiles/server.dir/server/HttpServer.cpp.o
 /home/<USER>/test_google_cli/test2/server/HttpServer.cpp
 /usr/include/stdc-predef.h
 /home/<USER>/test_google_cli/test2/server/HttpServer.h
 /home/<USER>/test_google_cli/test2/server/Router.h
 /home/<USER>/test_google_cli/test2/server/Request.h
 /usr/include/c++/12/string
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 /usr/include/features.h
 /usr/include/features-time64.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 /usr/include/c++/12/pstl/pstl_config.h
 /usr/include/c++/12/bits/stringfwd.h
 /usr/include/c++/12/bits/memoryfwd.h
 /usr/include/c++/12/bits/char_traits.h
 /usr/include/c++/12/bits/postypes.h
 /usr/include/c++/12/cwchar
 /usr/include/wchar.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 /usr/include/x86_64-linux-gnu/bits/wchar.h
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/c++/12/type_traits
 /usr/include/c++/12/cstdint
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdint.h
 /usr/include/stdint.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 /usr/include/c++/12/bits/allocator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 /usr/include/c++/12/bits/new_allocator.h
 /usr/include/c++/12/new
 /usr/include/c++/12/bits/exception.h
 /usr/include/c++/12/bits/functexcept.h
 /usr/include/c++/12/bits/exception_defines.h
 /usr/include/c++/12/bits/move.h
 /usr/include/c++/12/bits/cpp_type_traits.h
 /usr/include/c++/12/bits/localefwd.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 /usr/include/c++/12/clocale
 /usr/include/locale.h
 /usr/include/x86_64-linux-gnu/bits/locale.h
 /usr/include/c++/12/iosfwd
 /usr/include/c++/12/cctype
 /usr/include/ctype.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/c++/12/bits/ostream_insert.h
 /usr/include/c++/12/bits/cxxabi_forced.h
 /usr/include/c++/12/bits/stl_iterator_base_types.h
 /usr/include/c++/12/bits/stl_iterator_base_funcs.h
 /usr/include/c++/12/bits/concept_check.h
 /usr/include/c++/12/debug/assertions.h
 /usr/include/c++/12/bits/stl_iterator.h
 /usr/include/c++/12/ext/type_traits.h
 /usr/include/c++/12/bits/ptr_traits.h
 /usr/include/c++/12/bits/stl_function.h
 /usr/include/c++/12/backward/binders.h
 /usr/include/c++/12/ext/numeric_traits.h
 /usr/include/c++/12/bits/stl_algobase.h
 /usr/include/c++/12/bits/stl_pair.h
 /usr/include/c++/12/bits/utility.h
 /usr/include/c++/12/debug/debug.h
 /usr/include/c++/12/bits/predefined_ops.h
 /usr/include/c++/12/bits/refwrap.h
 /usr/include/c++/12/bits/invoke.h
 /usr/include/c++/12/bits/range_access.h
 /usr/include/c++/12/initializer_list
 /usr/include/c++/12/bits/basic_string.h
 /usr/include/c++/12/ext/alloc_traits.h
 /usr/include/c++/12/bits/alloc_traits.h
 /usr/include/c++/12/bits/stl_construct.h
 /usr/include/c++/12/string_view
 /usr/include/c++/12/bits/functional_hash.h
 /usr/include/c++/12/bits/hash_bytes.h
 /usr/include/c++/12/bits/string_view.tcc
 /usr/include/c++/12/ext/string_conversions.h
 /usr/include/c++/12/cstdlib
 /usr/include/stdlib.h
 /usr/include/x86_64-linux-gnu/bits/waitflags.h
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h
 /usr/include/x86_64-linux-gnu/sys/types.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/endian.h
 /usr/include/x86_64-linux-gnu/bits/byteswap.h
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 /usr/include/x86_64-linux-gnu/sys/select.h
 /usr/include/x86_64-linux-gnu/bits/select.h
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 /usr/include/alloca.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 /usr/include/c++/12/bits/std_abs.h
 /usr/include/c++/12/cstdio
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/stdio.h
 /usr/include/c++/12/cerrno
 /usr/include/errno.h
 /usr/include/x86_64-linux-gnu/bits/errno.h
 /usr/include/linux/errno.h
 /usr/include/x86_64-linux-gnu/asm/errno.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h
 /usr/include/c++/12/bits/charconv.h
 /usr/include/c++/12/bits/basic_string.tcc
 /usr/include/c++/12/map
 /usr/include/c++/12/bits/stl_tree.h
 /usr/include/c++/12/ext/aligned_buffer.h
 /usr/include/c++/12/bits/node_handle.h
 /usr/include/c++/12/bits/stl_map.h
 /usr/include/c++/12/tuple
 /usr/include/c++/12/bits/uses_allocator.h
 /usr/include/c++/12/bits/stl_multimap.h
 /usr/include/c++/12/bits/erase_if.h
 /home/<USER>/test_google_cli/test2/server/Response.h
 /usr/include/c++/12/functional
 /usr/include/c++/12/bits/std_function.h
 /usr/include/c++/12/typeinfo
 /usr/include/c++/12/unordered_map
 /usr/include/c++/12/bits/hashtable.h
 /usr/include/c++/12/bits/hashtable_policy.h
 /usr/include/c++/12/bits/enable_special_members.h
 /usr/include/c++/12/bits/unordered_map.h
 /usr/include/c++/12/vector
 /usr/include/c++/12/bits/stl_uninitialized.h
 /usr/include/c++/12/bits/stl_vector.h
 /usr/include/c++/12/bits/stl_bvector.h
 /usr/include/c++/12/bits/vector.tcc
 /usr/include/c++/12/array
 /usr/include/c++/12/compare
 /usr/include/c++/12/bits/stl_algo.h
 /usr/include/c++/12/bits/algorithmfwd.h
 /usr/include/c++/12/bits/stl_heap.h
 /usr/include/c++/12/bits/stl_tempbuf.h
 /usr/include/c++/12/bits/uniform_int_dist.h
 /home/<USER>/test_google_cli/test2/server/Middleware.h
 /usr/include/c++/12/memory
 /usr/include/c++/12/bits/stl_raw_storage_iter.h
 /usr/include/c++/12/bits/align.h
 /usr/include/c++/12/bit
 /usr/include/c++/12/bits/unique_ptr.h
 /usr/include/c++/12/bits/shared_ptr.h
 /usr/include/c++/12/bits/shared_ptr_base.h
 /usr/include/c++/12/bits/allocated_ptr.h
 /usr/include/c++/12/ext/atomicity.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 /usr/include/pthread.h
 /usr/include/sched.h
 /usr/include/x86_64-linux-gnu/bits/sched.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h
 /usr/include/time.h
 /usr/include/x86_64-linux-gnu/bits/time.h
 /usr/include/x86_64-linux-gnu/bits/timex.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 /usr/include/x86_64-linux-gnu/bits/setjmp.h
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h
 /usr/include/c++/12/ext/concurrence.h
 /usr/include/c++/12/exception
 /usr/include/c++/12/bits/exception_ptr.h
 /usr/include/c++/12/bits/cxxabi_init_exception.h
 /usr/include/c++/12/bits/nested_exception.h
 /usr/include/c++/12/bits/shared_ptr_atomic.h
 /usr/include/c++/12/bits/atomic_base.h
 /usr/include/c++/12/bits/atomic_lockfree_defines.h
 /usr/include/c++/12/backward/auto_ptr.h
 /usr/include/c++/12/pstl/glue_memory_defs.h
 /usr/include/c++/12/pstl/execution_defs.h
 /usr/include/c++/12/chrono
 /usr/include/c++/12/bits/chrono.h
 /usr/include/c++/12/ratio
 /usr/include/c++/12/limits
 /usr/include/c++/12/ctime
 /usr/include/c++/12/bits/parse_numbers.h
 /usr/include/c++/12/unordered_set
 /usr/include/c++/12/bits/unordered_set.h
 /usr/include/c++/12/regex
 /usr/include/c++/12/bitset
 /usr/include/c++/12/locale
 /usr/include/c++/12/bits/locale_classes.h
 /usr/include/c++/12/bits/locale_classes.tcc
 /usr/include/c++/12/bits/locale_facets.h
 /usr/include/c++/12/cwctype
 /usr/include/wctype.h
 /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_base.h
 /usr/include/c++/12/bits/ios_base.h
 /usr/include/c++/12/system_error
 /usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 /usr/include/c++/12/stdexcept
 /usr/include/c++/12/streambuf
 /usr/include/c++/12/bits/streambuf.tcc
 /usr/include/c++/12/bits/streambuf_iterator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_inline.h
 /usr/include/c++/12/bits/locale_facets.tcc
 /usr/include/c++/12/bits/locale_facets_nonio.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/time_members.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/messages_members.h
 /usr/include/libintl.h
 /usr/include/c++/12/bits/codecvt.h
 /usr/include/c++/12/bits/locale_facets_nonio.tcc
 /usr/include/c++/12/bits/locale_conv.h
 /usr/include/c++/12/sstream
 /usr/include/c++/12/istream
 /usr/include/c++/12/ios
 /usr/include/c++/12/bits/basic_ios.h
 /usr/include/c++/12/bits/basic_ios.tcc
 /usr/include/c++/12/ostream
 /usr/include/c++/12/bits/ostream.tcc
 /usr/include/c++/12/bits/istream.tcc
 /usr/include/c++/12/bits/sstream.tcc
 /usr/include/c++/12/stack
 /usr/include/c++/12/deque
 /usr/include/c++/12/bits/stl_deque.h
 /usr/include/c++/12/bits/deque.tcc
 /usr/include/c++/12/bits/stl_stack.h
 /usr/include/c++/12/bits/regex_constants.h
 /usr/include/c++/12/bits/regex_error.h
 /usr/include/c++/12/bits/regex_automaton.h
 /usr/include/c++/12/bits/regex_automaton.tcc
 /usr/include/c++/12/bits/regex_scanner.h
 /usr/include/c++/12/bits/regex_scanner.tcc
 /usr/include/c++/12/bits/regex_compiler.h
 /usr/include/c++/12/bits/regex_compiler.tcc
 /usr/include/c++/12/bits/regex.h
 /usr/include/c++/12/bits/regex.tcc
 /usr/include/c++/12/bits/regex_executor.h
 /usr/include/c++/12/bits/regex_executor.tcc
 /home/<USER>/test_google_cli/test2/utilities/Logger.h
 /usr/include/c++/12/iostream
 /usr/include/c++/12/fstream
 /usr/include/x86_64-linux-gnu/c++/12/bits/basic_file.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++io.h
 /usr/include/c++/12/bits/fstream.tcc
 /usr/include/c++/12/mutex
 /usr/include/c++/12/bits/std_mutex.h
 /usr/include/c++/12/bits/unique_lock.h
 /usr/include/c++/12/iomanip
 /usr/include/c++/12/bits/quoted_string.h
 /usr/include/c++/12/thread
 /usr/include/c++/12/bits/std_thread.h
 /usr/include/c++/12/bits/this_thread_sleep.h
 /usr/include/c++/12/filesystem
 /usr/include/c++/12/bits/fs_fwd.h
 /usr/include/c++/12/bits/fs_path.h
 /usr/include/c++/12/codecvt
 /usr/include/c++/12/bits/fs_dir.h
 /usr/include/c++/12/bits/fs_ops.h
 /home/<USER>/test_google_cli/test2/core/ThreadPool.h
 /usr/include/c++/12/queue
 /usr/include/c++/12/bits/stl_queue.h
 /usr/include/c++/12/condition_variable
 /usr/include/c++/12/future
 /usr/include/c++/12/bits/atomic_futex.h
 /usr/include/c++/12/atomic
 /home/<USER>/test_google_cli/test2/utilities/Logger.h
 /home/<USER>/test_google_cli/test2/core/Application.h
 /home/<USER>/test_google_cli/test2/utilities/Config.h
 /usr/include/c++/12/variant
 /usr/include/c++/12/optional
 /home/<USER>/test_google_cli/test2/utilities/ErrorHandler.h
 /home/<USER>/test_google_cli/test2/utilities/Logger.h
 /home/<USER>/test_google_cli/test2/utilities/ErrorHandler.h
 /home/<USER>/test_google_cli/test2/server/Utils.h
 /usr/include/c++/12/csignal
 /usr/include/signal.h
 /usr/include/x86_64-linux-gnu/bits/signum-generic.h
 /usr/include/x86_64-linux-gnu/bits/signum-arch.h
 /usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h
 /usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h
 /usr/include/x86_64-linux-gnu/bits/siginfo-arch.h
 /usr/include/x86_64-linux-gnu/bits/siginfo-consts.h
 /usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h
 /usr/include/x86_64-linux-gnu/bits/types/sigval_t.h
 /usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h
 /usr/include/x86_64-linux-gnu/bits/sigevent-consts.h
 /usr/include/x86_64-linux-gnu/bits/sigaction.h
 /usr/include/x86_64-linux-gnu/bits/sigcontext.h
 /usr/include/x86_64-linux-gnu/bits/types/stack_t.h
 /usr/include/x86_64-linux-gnu/sys/ucontext.h
 /usr/include/x86_64-linux-gnu/bits/sigstack.h
 /usr/include/x86_64-linux-gnu/bits/sigstksz.h
 /usr/include/unistd.h
 /usr/include/x86_64-linux-gnu/bits/posix_opt.h
 /usr/include/x86_64-linux-gnu/bits/environments.h
 /usr/include/x86_64-linux-gnu/bits/confname.h
 /usr/include/x86_64-linux-gnu/bits/getopt_posix.h
 /usr/include/x86_64-linux-gnu/bits/getopt_core.h
 /usr/include/x86_64-linux-gnu/bits/unistd_ext.h
 /usr/include/linux/close_range.h
 /usr/include/x86_64-linux-gnu/bits/ss_flags.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h
 /usr/include/x86_64-linux-gnu/bits/sigthread.h
 /usr/include/x86_64-linux-gnu/bits/signal_ext.h
 /usr/include/c++/12/algorithm
 /usr/include/c++/12/pstl/glue_algorithm_defs.h
 /usr/include/x86_64-linux-gnu/sys/socket.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h
 /usr/include/x86_64-linux-gnu/bits/socket.h
 /usr/include/x86_64-linux-gnu/bits/socket_type.h
 /usr/include/x86_64-linux-gnu/bits/sockaddr.h
 /usr/include/x86_64-linux-gnu/asm/socket.h
 /usr/include/asm-generic/socket.h
 /usr/include/linux/posix_types.h
 /usr/include/linux/stddef.h
 /usr/include/x86_64-linux-gnu/asm/posix_types.h
 /usr/include/x86_64-linux-gnu/asm/posix_types_64.h
 /usr/include/asm-generic/posix_types.h
 /usr/include/x86_64-linux-gnu/asm/bitsperlong.h
 /usr/include/asm-generic/bitsperlong.h
 /usr/include/x86_64-linux-gnu/asm/sockios.h
 /usr/include/asm-generic/sockios.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_osockaddr.h
 /usr/include/netinet/in.h
 /usr/include/x86_64-linux-gnu/bits/in.h
 /usr/include/c++/12/cstring
 /usr/include/string.h
 /usr/include/strings.h
 /usr/include/arpa/inet.h
 /usr/include/fcntl.h
 /usr/include/x86_64-linux-gnu/bits/fcntl.h
 /usr/include/x86_64-linux-gnu/bits/fcntl-linux.h
 /usr/include/linux/falloc.h
 /usr/include/x86_64-linux-gnu/bits/stat.h
 /usr/include/x86_64-linux-gnu/bits/struct_stat.h
 /usr/include/x86_64-linux-gnu/sys/time.h

CMakeFiles/server.dir/server/Middleware.cpp.o
 /home/<USER>/test_google_cli/test2/server/Middleware.cpp
 /usr/include/stdc-predef.h
 /home/<USER>/test_google_cli/test2/server/Middleware.h
 /home/<USER>/test_google_cli/test2/server/Request.h
 /usr/include/c++/12/string
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 /usr/include/features.h
 /usr/include/features-time64.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 /usr/include/c++/12/pstl/pstl_config.h
 /usr/include/c++/12/bits/stringfwd.h
 /usr/include/c++/12/bits/memoryfwd.h
 /usr/include/c++/12/bits/char_traits.h
 /usr/include/c++/12/bits/postypes.h
 /usr/include/c++/12/cwchar
 /usr/include/wchar.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 /usr/include/x86_64-linux-gnu/bits/wchar.h
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/c++/12/type_traits
 /usr/include/c++/12/cstdint
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdint.h
 /usr/include/stdint.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 /usr/include/c++/12/bits/allocator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 /usr/include/c++/12/bits/new_allocator.h
 /usr/include/c++/12/new
 /usr/include/c++/12/bits/exception.h
 /usr/include/c++/12/bits/functexcept.h
 /usr/include/c++/12/bits/exception_defines.h
 /usr/include/c++/12/bits/move.h
 /usr/include/c++/12/bits/cpp_type_traits.h
 /usr/include/c++/12/bits/localefwd.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 /usr/include/c++/12/clocale
 /usr/include/locale.h
 /usr/include/x86_64-linux-gnu/bits/locale.h
 /usr/include/c++/12/iosfwd
 /usr/include/c++/12/cctype
 /usr/include/ctype.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/c++/12/bits/ostream_insert.h
 /usr/include/c++/12/bits/cxxabi_forced.h
 /usr/include/c++/12/bits/stl_iterator_base_types.h
 /usr/include/c++/12/bits/stl_iterator_base_funcs.h
 /usr/include/c++/12/bits/concept_check.h
 /usr/include/c++/12/debug/assertions.h
 /usr/include/c++/12/bits/stl_iterator.h
 /usr/include/c++/12/ext/type_traits.h
 /usr/include/c++/12/bits/ptr_traits.h
 /usr/include/c++/12/bits/stl_function.h
 /usr/include/c++/12/backward/binders.h
 /usr/include/c++/12/ext/numeric_traits.h
 /usr/include/c++/12/bits/stl_algobase.h
 /usr/include/c++/12/bits/stl_pair.h
 /usr/include/c++/12/bits/utility.h
 /usr/include/c++/12/debug/debug.h
 /usr/include/c++/12/bits/predefined_ops.h
 /usr/include/c++/12/bits/refwrap.h
 /usr/include/c++/12/bits/invoke.h
 /usr/include/c++/12/bits/range_access.h
 /usr/include/c++/12/initializer_list
 /usr/include/c++/12/bits/basic_string.h
 /usr/include/c++/12/ext/alloc_traits.h
 /usr/include/c++/12/bits/alloc_traits.h
 /usr/include/c++/12/bits/stl_construct.h
 /usr/include/c++/12/string_view
 /usr/include/c++/12/bits/functional_hash.h
 /usr/include/c++/12/bits/hash_bytes.h
 /usr/include/c++/12/bits/string_view.tcc
 /usr/include/c++/12/ext/string_conversions.h
 /usr/include/c++/12/cstdlib
 /usr/include/stdlib.h
 /usr/include/x86_64-linux-gnu/bits/waitflags.h
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h
 /usr/include/x86_64-linux-gnu/sys/types.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/endian.h
 /usr/include/x86_64-linux-gnu/bits/byteswap.h
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 /usr/include/x86_64-linux-gnu/sys/select.h
 /usr/include/x86_64-linux-gnu/bits/select.h
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 /usr/include/alloca.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 /usr/include/c++/12/bits/std_abs.h
 /usr/include/c++/12/cstdio
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/stdio.h
 /usr/include/c++/12/cerrno
 /usr/include/errno.h
 /usr/include/x86_64-linux-gnu/bits/errno.h
 /usr/include/linux/errno.h
 /usr/include/x86_64-linux-gnu/asm/errno.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h
 /usr/include/c++/12/bits/charconv.h
 /usr/include/c++/12/bits/basic_string.tcc
 /usr/include/c++/12/map
 /usr/include/c++/12/bits/stl_tree.h
 /usr/include/c++/12/ext/aligned_buffer.h
 /usr/include/c++/12/bits/node_handle.h
 /usr/include/c++/12/bits/stl_map.h
 /usr/include/c++/12/tuple
 /usr/include/c++/12/bits/uses_allocator.h
 /usr/include/c++/12/bits/stl_multimap.h
 /usr/include/c++/12/bits/erase_if.h
 /home/<USER>/test_google_cli/test2/server/Response.h
 /usr/include/c++/12/functional
 /usr/include/c++/12/bits/std_function.h
 /usr/include/c++/12/typeinfo
 /usr/include/c++/12/unordered_map
 /usr/include/c++/12/bits/hashtable.h
 /usr/include/c++/12/bits/hashtable_policy.h
 /usr/include/c++/12/bits/enable_special_members.h
 /usr/include/c++/12/bits/unordered_map.h
 /usr/include/c++/12/vector
 /usr/include/c++/12/bits/stl_uninitialized.h
 /usr/include/c++/12/bits/stl_vector.h
 /usr/include/c++/12/bits/stl_bvector.h
 /usr/include/c++/12/bits/vector.tcc
 /usr/include/c++/12/array
 /usr/include/c++/12/compare
 /usr/include/c++/12/bits/stl_algo.h
 /usr/include/c++/12/bits/algorithmfwd.h
 /usr/include/c++/12/bits/stl_heap.h
 /usr/include/c++/12/bits/stl_tempbuf.h
 /usr/include/c++/12/bits/uniform_int_dist.h
 /usr/include/c++/12/memory
 /usr/include/c++/12/bits/stl_raw_storage_iter.h
 /usr/include/c++/12/bits/align.h
 /usr/include/c++/12/bit
 /usr/include/c++/12/bits/unique_ptr.h
 /usr/include/c++/12/bits/shared_ptr.h
 /usr/include/c++/12/bits/shared_ptr_base.h
 /usr/include/c++/12/bits/allocated_ptr.h
 /usr/include/c++/12/ext/atomicity.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 /usr/include/pthread.h
 /usr/include/sched.h
 /usr/include/x86_64-linux-gnu/bits/sched.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h
 /usr/include/time.h
 /usr/include/x86_64-linux-gnu/bits/time.h
 /usr/include/x86_64-linux-gnu/bits/timex.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 /usr/include/x86_64-linux-gnu/bits/setjmp.h
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h
 /usr/include/c++/12/ext/concurrence.h
 /usr/include/c++/12/exception
 /usr/include/c++/12/bits/exception_ptr.h
 /usr/include/c++/12/bits/cxxabi_init_exception.h
 /usr/include/c++/12/bits/nested_exception.h
 /usr/include/c++/12/bits/shared_ptr_atomic.h
 /usr/include/c++/12/bits/atomic_base.h
 /usr/include/c++/12/bits/atomic_lockfree_defines.h
 /usr/include/c++/12/backward/auto_ptr.h
 /usr/include/c++/12/pstl/glue_memory_defs.h
 /usr/include/c++/12/pstl/execution_defs.h
 /usr/include/c++/12/chrono
 /usr/include/c++/12/bits/chrono.h
 /usr/include/c++/12/ratio
 /usr/include/c++/12/limits
 /usr/include/c++/12/ctime
 /usr/include/c++/12/bits/parse_numbers.h
 /usr/include/c++/12/unordered_set
 /usr/include/c++/12/bits/unordered_set.h
 /usr/include/c++/12/regex
 /usr/include/c++/12/bitset
 /usr/include/c++/12/locale
 /usr/include/c++/12/bits/locale_classes.h
 /usr/include/c++/12/bits/locale_classes.tcc
 /usr/include/c++/12/bits/locale_facets.h
 /usr/include/c++/12/cwctype
 /usr/include/wctype.h
 /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_base.h
 /usr/include/c++/12/bits/ios_base.h
 /usr/include/c++/12/system_error
 /usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 /usr/include/c++/12/stdexcept
 /usr/include/c++/12/streambuf
 /usr/include/c++/12/bits/streambuf.tcc
 /usr/include/c++/12/bits/streambuf_iterator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_inline.h
 /usr/include/c++/12/bits/locale_facets.tcc
 /usr/include/c++/12/bits/locale_facets_nonio.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/time_members.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/messages_members.h
 /usr/include/libintl.h
 /usr/include/c++/12/bits/codecvt.h
 /usr/include/c++/12/bits/locale_facets_nonio.tcc
 /usr/include/c++/12/bits/locale_conv.h
 /usr/include/c++/12/sstream
 /usr/include/c++/12/istream
 /usr/include/c++/12/ios
 /usr/include/c++/12/bits/basic_ios.h
 /usr/include/c++/12/bits/basic_ios.tcc
 /usr/include/c++/12/ostream
 /usr/include/c++/12/bits/ostream.tcc
 /usr/include/c++/12/bits/istream.tcc
 /usr/include/c++/12/bits/sstream.tcc
 /usr/include/c++/12/stack
 /usr/include/c++/12/deque
 /usr/include/c++/12/bits/stl_deque.h
 /usr/include/c++/12/bits/deque.tcc
 /usr/include/c++/12/bits/stl_stack.h
 /usr/include/c++/12/bits/regex_constants.h
 /usr/include/c++/12/bits/regex_error.h
 /usr/include/c++/12/bits/regex_automaton.h
 /usr/include/c++/12/bits/regex_automaton.tcc
 /usr/include/c++/12/bits/regex_scanner.h
 /usr/include/c++/12/bits/regex_scanner.tcc
 /usr/include/c++/12/bits/regex_compiler.h
 /usr/include/c++/12/bits/regex_compiler.tcc
 /usr/include/c++/12/bits/regex.h
 /usr/include/c++/12/bits/regex.tcc
 /usr/include/c++/12/bits/regex_executor.h
 /usr/include/c++/12/bits/regex_executor.tcc
 /home/<USER>/test_google_cli/test2/utilities/Logger.h
 /usr/include/c++/12/iostream
 /usr/include/c++/12/fstream
 /usr/include/x86_64-linux-gnu/c++/12/bits/basic_file.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++io.h
 /usr/include/c++/12/bits/fstream.tcc
 /usr/include/c++/12/mutex
 /usr/include/c++/12/bits/std_mutex.h
 /usr/include/c++/12/bits/unique_lock.h
 /usr/include/c++/12/iomanip
 /usr/include/c++/12/bits/quoted_string.h
 /usr/include/c++/12/thread
 /usr/include/c++/12/bits/std_thread.h
 /usr/include/c++/12/bits/this_thread_sleep.h
 /usr/include/c++/12/filesystem
 /usr/include/c++/12/bits/fs_fwd.h
 /usr/include/c++/12/bits/fs_path.h
 /usr/include/c++/12/codecvt
 /usr/include/c++/12/bits/fs_dir.h
 /usr/include/c++/12/bits/fs_ops.h
 /usr/include/c++/12/algorithm
 /usr/include/c++/12/pstl/glue_algorithm_defs.h

CMakeFiles/server.dir/server/Request.cpp.o
 /home/<USER>/test_google_cli/test2/server/Request.cpp
 /usr/include/stdc-predef.h
 /home/<USER>/test_google_cli/test2/server/Request.h
 /usr/include/c++/12/string
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 /usr/include/features.h
 /usr/include/features-time64.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 /usr/include/c++/12/pstl/pstl_config.h
 /usr/include/c++/12/bits/stringfwd.h
 /usr/include/c++/12/bits/memoryfwd.h
 /usr/include/c++/12/bits/char_traits.h
 /usr/include/c++/12/bits/postypes.h
 /usr/include/c++/12/cwchar
 /usr/include/wchar.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 /usr/include/x86_64-linux-gnu/bits/wchar.h
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/c++/12/type_traits
 /usr/include/c++/12/cstdint
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdint.h
 /usr/include/stdint.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 /usr/include/c++/12/bits/allocator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 /usr/include/c++/12/bits/new_allocator.h
 /usr/include/c++/12/new
 /usr/include/c++/12/bits/exception.h
 /usr/include/c++/12/bits/functexcept.h
 /usr/include/c++/12/bits/exception_defines.h
 /usr/include/c++/12/bits/move.h
 /usr/include/c++/12/bits/cpp_type_traits.h
 /usr/include/c++/12/bits/localefwd.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 /usr/include/c++/12/clocale
 /usr/include/locale.h
 /usr/include/x86_64-linux-gnu/bits/locale.h
 /usr/include/c++/12/iosfwd
 /usr/include/c++/12/cctype
 /usr/include/ctype.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/c++/12/bits/ostream_insert.h
 /usr/include/c++/12/bits/cxxabi_forced.h
 /usr/include/c++/12/bits/stl_iterator_base_types.h
 /usr/include/c++/12/bits/stl_iterator_base_funcs.h
 /usr/include/c++/12/bits/concept_check.h
 /usr/include/c++/12/debug/assertions.h
 /usr/include/c++/12/bits/stl_iterator.h
 /usr/include/c++/12/ext/type_traits.h
 /usr/include/c++/12/bits/ptr_traits.h
 /usr/include/c++/12/bits/stl_function.h
 /usr/include/c++/12/backward/binders.h
 /usr/include/c++/12/ext/numeric_traits.h
 /usr/include/c++/12/bits/stl_algobase.h
 /usr/include/c++/12/bits/stl_pair.h
 /usr/include/c++/12/bits/utility.h
 /usr/include/c++/12/debug/debug.h
 /usr/include/c++/12/bits/predefined_ops.h
 /usr/include/c++/12/bits/refwrap.h
 /usr/include/c++/12/bits/invoke.h
 /usr/include/c++/12/bits/range_access.h
 /usr/include/c++/12/initializer_list
 /usr/include/c++/12/bits/basic_string.h
 /usr/include/c++/12/ext/alloc_traits.h
 /usr/include/c++/12/bits/alloc_traits.h
 /usr/include/c++/12/bits/stl_construct.h
 /usr/include/c++/12/string_view
 /usr/include/c++/12/bits/functional_hash.h
 /usr/include/c++/12/bits/hash_bytes.h
 /usr/include/c++/12/bits/string_view.tcc
 /usr/include/c++/12/ext/string_conversions.h
 /usr/include/c++/12/cstdlib
 /usr/include/stdlib.h
 /usr/include/x86_64-linux-gnu/bits/waitflags.h
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h
 /usr/include/x86_64-linux-gnu/sys/types.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/endian.h
 /usr/include/x86_64-linux-gnu/bits/byteswap.h
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 /usr/include/x86_64-linux-gnu/sys/select.h
 /usr/include/x86_64-linux-gnu/bits/select.h
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 /usr/include/alloca.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 /usr/include/c++/12/bits/std_abs.h
 /usr/include/c++/12/cstdio
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/stdio.h
 /usr/include/c++/12/cerrno
 /usr/include/errno.h
 /usr/include/x86_64-linux-gnu/bits/errno.h
 /usr/include/linux/errno.h
 /usr/include/x86_64-linux-gnu/asm/errno.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h
 /usr/include/c++/12/bits/charconv.h
 /usr/include/c++/12/bits/basic_string.tcc
 /usr/include/c++/12/map
 /usr/include/c++/12/bits/stl_tree.h
 /usr/include/c++/12/ext/aligned_buffer.h
 /usr/include/c++/12/bits/node_handle.h
 /usr/include/c++/12/bits/stl_map.h
 /usr/include/c++/12/tuple
 /usr/include/c++/12/bits/uses_allocator.h
 /usr/include/c++/12/bits/stl_multimap.h
 /usr/include/c++/12/bits/erase_if.h
 /home/<USER>/test_google_cli/test2/server/Utils.h
 /usr/include/c++/12/vector
 /usr/include/c++/12/bits/stl_uninitialized.h
 /usr/include/c++/12/bits/stl_vector.h
 /usr/include/c++/12/bits/stl_bvector.h
 /usr/include/c++/12/bits/vector.tcc
 /usr/include/c++/12/sstream
 /usr/include/c++/12/istream
 /usr/include/c++/12/ios
 /usr/include/c++/12/exception
 /usr/include/c++/12/bits/exception_ptr.h
 /usr/include/c++/12/bits/cxxabi_init_exception.h
 /usr/include/c++/12/typeinfo
 /usr/include/c++/12/bits/nested_exception.h
 /usr/include/c++/12/bits/ios_base.h
 /usr/include/c++/12/ext/atomicity.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 /usr/include/pthread.h
 /usr/include/sched.h
 /usr/include/x86_64-linux-gnu/bits/sched.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h
 /usr/include/time.h
 /usr/include/x86_64-linux-gnu/bits/time.h
 /usr/include/x86_64-linux-gnu/bits/timex.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 /usr/include/x86_64-linux-gnu/bits/setjmp.h
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h
 /usr/include/c++/12/bits/locale_classes.h
 /usr/include/c++/12/bits/locale_classes.tcc
 /usr/include/c++/12/system_error
 /usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 /usr/include/c++/12/stdexcept
 /usr/include/c++/12/streambuf
 /usr/include/c++/12/bits/streambuf.tcc
 /usr/include/c++/12/bits/basic_ios.h
 /usr/include/c++/12/bits/locale_facets.h
 /usr/include/c++/12/cwctype
 /usr/include/wctype.h
 /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_base.h
 /usr/include/c++/12/bits/streambuf_iterator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_inline.h
 /usr/include/c++/12/bits/locale_facets.tcc
 /usr/include/c++/12/bits/basic_ios.tcc
 /usr/include/c++/12/ostream
 /usr/include/c++/12/bits/ostream.tcc
 /usr/include/c++/12/bits/istream.tcc
 /usr/include/c++/12/bits/sstream.tcc

CMakeFiles/server.dir/server/Response.cpp.o
 /home/<USER>/test_google_cli/test2/server/Response.cpp
 /usr/include/stdc-predef.h
 /home/<USER>/test_google_cli/test2/server/Response.h
 /usr/include/c++/12/string
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 /usr/include/features.h
 /usr/include/features-time64.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 /usr/include/c++/12/pstl/pstl_config.h
 /usr/include/c++/12/bits/stringfwd.h
 /usr/include/c++/12/bits/memoryfwd.h
 /usr/include/c++/12/bits/char_traits.h
 /usr/include/c++/12/bits/postypes.h
 /usr/include/c++/12/cwchar
 /usr/include/wchar.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 /usr/include/x86_64-linux-gnu/bits/wchar.h
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/c++/12/type_traits
 /usr/include/c++/12/cstdint
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdint.h
 /usr/include/stdint.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 /usr/include/c++/12/bits/allocator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 /usr/include/c++/12/bits/new_allocator.h
 /usr/include/c++/12/new
 /usr/include/c++/12/bits/exception.h
 /usr/include/c++/12/bits/functexcept.h
 /usr/include/c++/12/bits/exception_defines.h
 /usr/include/c++/12/bits/move.h
 /usr/include/c++/12/bits/cpp_type_traits.h
 /usr/include/c++/12/bits/localefwd.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 /usr/include/c++/12/clocale
 /usr/include/locale.h
 /usr/include/x86_64-linux-gnu/bits/locale.h
 /usr/include/c++/12/iosfwd
 /usr/include/c++/12/cctype
 /usr/include/ctype.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/c++/12/bits/ostream_insert.h
 /usr/include/c++/12/bits/cxxabi_forced.h
 /usr/include/c++/12/bits/stl_iterator_base_types.h
 /usr/include/c++/12/bits/stl_iterator_base_funcs.h
 /usr/include/c++/12/bits/concept_check.h
 /usr/include/c++/12/debug/assertions.h
 /usr/include/c++/12/bits/stl_iterator.h
 /usr/include/c++/12/ext/type_traits.h
 /usr/include/c++/12/bits/ptr_traits.h
 /usr/include/c++/12/bits/stl_function.h
 /usr/include/c++/12/backward/binders.h
 /usr/include/c++/12/ext/numeric_traits.h
 /usr/include/c++/12/bits/stl_algobase.h
 /usr/include/c++/12/bits/stl_pair.h
 /usr/include/c++/12/bits/utility.h
 /usr/include/c++/12/debug/debug.h
 /usr/include/c++/12/bits/predefined_ops.h
 /usr/include/c++/12/bits/refwrap.h
 /usr/include/c++/12/bits/invoke.h
 /usr/include/c++/12/bits/range_access.h
 /usr/include/c++/12/initializer_list
 /usr/include/c++/12/bits/basic_string.h
 /usr/include/c++/12/ext/alloc_traits.h
 /usr/include/c++/12/bits/alloc_traits.h
 /usr/include/c++/12/bits/stl_construct.h
 /usr/include/c++/12/string_view
 /usr/include/c++/12/bits/functional_hash.h
 /usr/include/c++/12/bits/hash_bytes.h
 /usr/include/c++/12/bits/string_view.tcc
 /usr/include/c++/12/ext/string_conversions.h
 /usr/include/c++/12/cstdlib
 /usr/include/stdlib.h
 /usr/include/x86_64-linux-gnu/bits/waitflags.h
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h
 /usr/include/x86_64-linux-gnu/sys/types.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/endian.h
 /usr/include/x86_64-linux-gnu/bits/byteswap.h
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 /usr/include/x86_64-linux-gnu/sys/select.h
 /usr/include/x86_64-linux-gnu/bits/select.h
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 /usr/include/alloca.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 /usr/include/c++/12/bits/std_abs.h
 /usr/include/c++/12/cstdio
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/stdio.h
 /usr/include/c++/12/cerrno
 /usr/include/errno.h
 /usr/include/x86_64-linux-gnu/bits/errno.h
 /usr/include/linux/errno.h
 /usr/include/x86_64-linux-gnu/asm/errno.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h
 /usr/include/c++/12/bits/charconv.h
 /usr/include/c++/12/bits/basic_string.tcc
 /usr/include/c++/12/map
 /usr/include/c++/12/bits/stl_tree.h
 /usr/include/c++/12/ext/aligned_buffer.h
 /usr/include/c++/12/bits/node_handle.h
 /usr/include/c++/12/bits/stl_map.h
 /usr/include/c++/12/tuple
 /usr/include/c++/12/bits/uses_allocator.h
 /usr/include/c++/12/bits/stl_multimap.h
 /usr/include/c++/12/bits/erase_if.h
 /home/<USER>/test_google_cli/test2/server/Utils.h
 /usr/include/c++/12/vector
 /usr/include/c++/12/bits/stl_uninitialized.h
 /usr/include/c++/12/bits/stl_vector.h
 /usr/include/c++/12/bits/stl_bvector.h
 /usr/include/c++/12/bits/vector.tcc
 /usr/include/c++/12/sstream
 /usr/include/c++/12/istream
 /usr/include/c++/12/ios
 /usr/include/c++/12/exception
 /usr/include/c++/12/bits/exception_ptr.h
 /usr/include/c++/12/bits/cxxabi_init_exception.h
 /usr/include/c++/12/typeinfo
 /usr/include/c++/12/bits/nested_exception.h
 /usr/include/c++/12/bits/ios_base.h
 /usr/include/c++/12/ext/atomicity.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 /usr/include/pthread.h
 /usr/include/sched.h
 /usr/include/x86_64-linux-gnu/bits/sched.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h
 /usr/include/time.h
 /usr/include/x86_64-linux-gnu/bits/time.h
 /usr/include/x86_64-linux-gnu/bits/timex.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 /usr/include/x86_64-linux-gnu/bits/setjmp.h
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h
 /usr/include/c++/12/bits/locale_classes.h
 /usr/include/c++/12/bits/locale_classes.tcc
 /usr/include/c++/12/system_error
 /usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 /usr/include/c++/12/stdexcept
 /usr/include/c++/12/streambuf
 /usr/include/c++/12/bits/streambuf.tcc
 /usr/include/c++/12/bits/basic_ios.h
 /usr/include/c++/12/bits/locale_facets.h
 /usr/include/c++/12/cwctype
 /usr/include/wctype.h
 /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_base.h
 /usr/include/c++/12/bits/streambuf_iterator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_inline.h
 /usr/include/c++/12/bits/locale_facets.tcc
 /usr/include/c++/12/bits/basic_ios.tcc
 /usr/include/c++/12/ostream
 /usr/include/c++/12/bits/ostream.tcc
 /usr/include/c++/12/bits/istream.tcc
 /usr/include/c++/12/bits/sstream.tcc

CMakeFiles/server.dir/server/Router.cpp.o
 /home/<USER>/test_google_cli/test2/server/Router.cpp
 /usr/include/stdc-predef.h
 /home/<USER>/test_google_cli/test2/server/Router.h
 /home/<USER>/test_google_cli/test2/server/Request.h
 /usr/include/c++/12/string
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 /usr/include/features.h
 /usr/include/features-time64.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 /usr/include/c++/12/pstl/pstl_config.h
 /usr/include/c++/12/bits/stringfwd.h
 /usr/include/c++/12/bits/memoryfwd.h
 /usr/include/c++/12/bits/char_traits.h
 /usr/include/c++/12/bits/postypes.h
 /usr/include/c++/12/cwchar
 /usr/include/wchar.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 /usr/include/x86_64-linux-gnu/bits/wchar.h
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/c++/12/type_traits
 /usr/include/c++/12/cstdint
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdint.h
 /usr/include/stdint.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 /usr/include/c++/12/bits/allocator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 /usr/include/c++/12/bits/new_allocator.h
 /usr/include/c++/12/new
 /usr/include/c++/12/bits/exception.h
 /usr/include/c++/12/bits/functexcept.h
 /usr/include/c++/12/bits/exception_defines.h
 /usr/include/c++/12/bits/move.h
 /usr/include/c++/12/bits/cpp_type_traits.h
 /usr/include/c++/12/bits/localefwd.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 /usr/include/c++/12/clocale
 /usr/include/locale.h
 /usr/include/x86_64-linux-gnu/bits/locale.h
 /usr/include/c++/12/iosfwd
 /usr/include/c++/12/cctype
 /usr/include/ctype.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/c++/12/bits/ostream_insert.h
 /usr/include/c++/12/bits/cxxabi_forced.h
 /usr/include/c++/12/bits/stl_iterator_base_types.h
 /usr/include/c++/12/bits/stl_iterator_base_funcs.h
 /usr/include/c++/12/bits/concept_check.h
 /usr/include/c++/12/debug/assertions.h
 /usr/include/c++/12/bits/stl_iterator.h
 /usr/include/c++/12/ext/type_traits.h
 /usr/include/c++/12/bits/ptr_traits.h
 /usr/include/c++/12/bits/stl_function.h
 /usr/include/c++/12/backward/binders.h
 /usr/include/c++/12/ext/numeric_traits.h
 /usr/include/c++/12/bits/stl_algobase.h
 /usr/include/c++/12/bits/stl_pair.h
 /usr/include/c++/12/bits/utility.h
 /usr/include/c++/12/debug/debug.h
 /usr/include/c++/12/bits/predefined_ops.h
 /usr/include/c++/12/bits/refwrap.h
 /usr/include/c++/12/bits/invoke.h
 /usr/include/c++/12/bits/range_access.h
 /usr/include/c++/12/initializer_list
 /usr/include/c++/12/bits/basic_string.h
 /usr/include/c++/12/ext/alloc_traits.h
 /usr/include/c++/12/bits/alloc_traits.h
 /usr/include/c++/12/bits/stl_construct.h
 /usr/include/c++/12/string_view
 /usr/include/c++/12/bits/functional_hash.h
 /usr/include/c++/12/bits/hash_bytes.h
 /usr/include/c++/12/bits/string_view.tcc
 /usr/include/c++/12/ext/string_conversions.h
 /usr/include/c++/12/cstdlib
 /usr/include/stdlib.h
 /usr/include/x86_64-linux-gnu/bits/waitflags.h
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h
 /usr/include/x86_64-linux-gnu/sys/types.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/endian.h
 /usr/include/x86_64-linux-gnu/bits/byteswap.h
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 /usr/include/x86_64-linux-gnu/sys/select.h
 /usr/include/x86_64-linux-gnu/bits/select.h
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 /usr/include/alloca.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 /usr/include/c++/12/bits/std_abs.h
 /usr/include/c++/12/cstdio
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/stdio.h
 /usr/include/c++/12/cerrno
 /usr/include/errno.h
 /usr/include/x86_64-linux-gnu/bits/errno.h
 /usr/include/linux/errno.h
 /usr/include/x86_64-linux-gnu/asm/errno.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h
 /usr/include/c++/12/bits/charconv.h
 /usr/include/c++/12/bits/basic_string.tcc
 /usr/include/c++/12/map
 /usr/include/c++/12/bits/stl_tree.h
 /usr/include/c++/12/ext/aligned_buffer.h
 /usr/include/c++/12/bits/node_handle.h
 /usr/include/c++/12/bits/stl_map.h
 /usr/include/c++/12/tuple
 /usr/include/c++/12/bits/uses_allocator.h
 /usr/include/c++/12/bits/stl_multimap.h
 /usr/include/c++/12/bits/erase_if.h
 /home/<USER>/test_google_cli/test2/server/Response.h
 /usr/include/c++/12/functional
 /usr/include/c++/12/bits/std_function.h
 /usr/include/c++/12/typeinfo
 /usr/include/c++/12/unordered_map
 /usr/include/c++/12/bits/hashtable.h
 /usr/include/c++/12/bits/hashtable_policy.h
 /usr/include/c++/12/bits/enable_special_members.h
 /usr/include/c++/12/bits/unordered_map.h
 /usr/include/c++/12/vector
 /usr/include/c++/12/bits/stl_uninitialized.h
 /usr/include/c++/12/bits/stl_vector.h
 /usr/include/c++/12/bits/stl_bvector.h
 /usr/include/c++/12/bits/vector.tcc
 /usr/include/c++/12/array
 /usr/include/c++/12/compare
 /usr/include/c++/12/bits/stl_algo.h
 /usr/include/c++/12/bits/algorithmfwd.h
 /usr/include/c++/12/bits/stl_heap.h
 /usr/include/c++/12/bits/stl_tempbuf.h
 /usr/include/c++/12/bits/uniform_int_dist.h
 /home/<USER>/test_google_cli/test2/server/Utils.h

CMakeFiles/server.dir/server/Utils.cpp.o
 /home/<USER>/test_google_cli/test2/server/Utils.cpp
 /usr/include/stdc-predef.h
 /home/<USER>/test_google_cli/test2/server/Utils.h
 /usr/include/c++/12/string
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 /usr/include/features.h
 /usr/include/features-time64.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 /usr/include/c++/12/pstl/pstl_config.h
 /usr/include/c++/12/bits/stringfwd.h
 /usr/include/c++/12/bits/memoryfwd.h
 /usr/include/c++/12/bits/char_traits.h
 /usr/include/c++/12/bits/postypes.h
 /usr/include/c++/12/cwchar
 /usr/include/wchar.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 /usr/include/x86_64-linux-gnu/bits/wchar.h
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/c++/12/type_traits
 /usr/include/c++/12/cstdint
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdint.h
 /usr/include/stdint.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 /usr/include/c++/12/bits/allocator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 /usr/include/c++/12/bits/new_allocator.h
 /usr/include/c++/12/new
 /usr/include/c++/12/bits/exception.h
 /usr/include/c++/12/bits/functexcept.h
 /usr/include/c++/12/bits/exception_defines.h
 /usr/include/c++/12/bits/move.h
 /usr/include/c++/12/bits/cpp_type_traits.h
 /usr/include/c++/12/bits/localefwd.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 /usr/include/c++/12/clocale
 /usr/include/locale.h
 /usr/include/x86_64-linux-gnu/bits/locale.h
 /usr/include/c++/12/iosfwd
 /usr/include/c++/12/cctype
 /usr/include/ctype.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/c++/12/bits/ostream_insert.h
 /usr/include/c++/12/bits/cxxabi_forced.h
 /usr/include/c++/12/bits/stl_iterator_base_types.h
 /usr/include/c++/12/bits/stl_iterator_base_funcs.h
 /usr/include/c++/12/bits/concept_check.h
 /usr/include/c++/12/debug/assertions.h
 /usr/include/c++/12/bits/stl_iterator.h
 /usr/include/c++/12/ext/type_traits.h
 /usr/include/c++/12/bits/ptr_traits.h
 /usr/include/c++/12/bits/stl_function.h
 /usr/include/c++/12/backward/binders.h
 /usr/include/c++/12/ext/numeric_traits.h
 /usr/include/c++/12/bits/stl_algobase.h
 /usr/include/c++/12/bits/stl_pair.h
 /usr/include/c++/12/bits/utility.h
 /usr/include/c++/12/debug/debug.h
 /usr/include/c++/12/bits/predefined_ops.h
 /usr/include/c++/12/bits/refwrap.h
 /usr/include/c++/12/bits/invoke.h
 /usr/include/c++/12/bits/range_access.h
 /usr/include/c++/12/initializer_list
 /usr/include/c++/12/bits/basic_string.h
 /usr/include/c++/12/ext/alloc_traits.h
 /usr/include/c++/12/bits/alloc_traits.h
 /usr/include/c++/12/bits/stl_construct.h
 /usr/include/c++/12/string_view
 /usr/include/c++/12/bits/functional_hash.h
 /usr/include/c++/12/bits/hash_bytes.h
 /usr/include/c++/12/bits/string_view.tcc
 /usr/include/c++/12/ext/string_conversions.h
 /usr/include/c++/12/cstdlib
 /usr/include/stdlib.h
 /usr/include/x86_64-linux-gnu/bits/waitflags.h
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h
 /usr/include/x86_64-linux-gnu/sys/types.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/endian.h
 /usr/include/x86_64-linux-gnu/bits/byteswap.h
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 /usr/include/x86_64-linux-gnu/sys/select.h
 /usr/include/x86_64-linux-gnu/bits/select.h
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 /usr/include/alloca.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 /usr/include/c++/12/bits/std_abs.h
 /usr/include/c++/12/cstdio
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/stdio.h
 /usr/include/c++/12/cerrno
 /usr/include/errno.h
 /usr/include/x86_64-linux-gnu/bits/errno.h
 /usr/include/linux/errno.h
 /usr/include/x86_64-linux-gnu/asm/errno.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h
 /usr/include/c++/12/bits/charconv.h
 /usr/include/c++/12/bits/basic_string.tcc
 /usr/include/c++/12/map
 /usr/include/c++/12/bits/stl_tree.h
 /usr/include/c++/12/ext/aligned_buffer.h
 /usr/include/c++/12/bits/node_handle.h
 /usr/include/c++/12/bits/stl_map.h
 /usr/include/c++/12/tuple
 /usr/include/c++/12/bits/uses_allocator.h
 /usr/include/c++/12/bits/stl_multimap.h
 /usr/include/c++/12/bits/erase_if.h
 /usr/include/c++/12/vector
 /usr/include/c++/12/bits/stl_uninitialized.h
 /usr/include/c++/12/bits/stl_vector.h
 /usr/include/c++/12/bits/stl_bvector.h
 /usr/include/c++/12/bits/vector.tcc
 /home/<USER>/test_google_cli/test2/server/Response.h
 /usr/include/c++/12/sstream
 /usr/include/c++/12/istream
 /usr/include/c++/12/ios
 /usr/include/c++/12/exception
 /usr/include/c++/12/bits/exception_ptr.h
 /usr/include/c++/12/bits/cxxabi_init_exception.h
 /usr/include/c++/12/typeinfo
 /usr/include/c++/12/bits/nested_exception.h
 /usr/include/c++/12/bits/ios_base.h
 /usr/include/c++/12/ext/atomicity.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 /usr/include/pthread.h
 /usr/include/sched.h
 /usr/include/x86_64-linux-gnu/bits/sched.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h
 /usr/include/time.h
 /usr/include/x86_64-linux-gnu/bits/time.h
 /usr/include/x86_64-linux-gnu/bits/timex.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 /usr/include/x86_64-linux-gnu/bits/setjmp.h
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h
 /usr/include/c++/12/bits/locale_classes.h
 /usr/include/c++/12/bits/locale_classes.tcc
 /usr/include/c++/12/system_error
 /usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 /usr/include/c++/12/stdexcept
 /usr/include/c++/12/streambuf
 /usr/include/c++/12/bits/streambuf.tcc
 /usr/include/c++/12/bits/basic_ios.h
 /usr/include/c++/12/bits/locale_facets.h
 /usr/include/c++/12/cwctype
 /usr/include/wctype.h
 /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_base.h
 /usr/include/c++/12/bits/streambuf_iterator.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_inline.h
 /usr/include/c++/12/bits/locale_facets.tcc
 /usr/include/c++/12/bits/basic_ios.tcc
 /usr/include/c++/12/ostream
 /usr/include/c++/12/bits/ostream.tcc
 /usr/include/c++/12/bits/istream.tcc
 /usr/include/c++/12/bits/sstream.tcc
 /usr/include/c++/12/chrono
 /usr/include/c++/12/bits/chrono.h
 /usr/include/c++/12/ratio
 /usr/include/c++/12/limits
 /usr/include/c++/12/ctime
 /usr/include/c++/12/bits/parse_numbers.h
 /usr/include/c++/12/fstream
 /usr/include/c++/12/bits/codecvt.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/basic_file.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++io.h
 /usr/include/c++/12/bits/fstream.tcc
 /usr/include/c++/12/filesystem
 /usr/include/c++/12/bits/fs_fwd.h
 /usr/include/c++/12/bits/fs_path.h
 /usr/include/c++/12/locale
 /usr/include/c++/12/bits/locale_facets_nonio.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/time_members.h
 /usr/include/x86_64-linux-gnu/c++/12/bits/messages_members.h
 /usr/include/libintl.h
 /usr/include/c++/12/bits/locale_facets_nonio.tcc
 /usr/include/c++/12/bits/locale_conv.h
 /usr/include/c++/12/iomanip
 /usr/include/c++/12/bits/quoted_string.h
 /usr/include/c++/12/codecvt
 /usr/include/c++/12/ext/concurrence.h
 /usr/include/c++/12/bits/shared_ptr.h
 /usr/include/c++/12/bits/shared_ptr_base.h
 /usr/include/c++/12/bits/allocated_ptr.h
 /usr/include/c++/12/bits/unique_ptr.h
 /usr/include/c++/12/bits/fs_dir.h
 /usr/include/c++/12/bits/fs_ops.h

