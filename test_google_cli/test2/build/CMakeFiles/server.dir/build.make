# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/build

# Include any dependencies generated for this target.
include CMakeFiles/server.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/server.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/server.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/server.dir/flags.make

CMakeFiles/server.dir/server/HttpServer.cpp.o: CMakeFiles/server.dir/flags.make
CMakeFiles/server.dir/server/HttpServer.cpp.o: /home/<USER>/test_google_cli/test2/server/HttpServer.cpp
CMakeFiles/server.dir/server/HttpServer.cpp.o: CMakeFiles/server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/server.dir/server/HttpServer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/server.dir/server/HttpServer.cpp.o -MF CMakeFiles/server.dir/server/HttpServer.cpp.o.d -o CMakeFiles/server.dir/server/HttpServer.cpp.o -c /home/<USER>/test_google_cli/test2/server/HttpServer.cpp

CMakeFiles/server.dir/server/HttpServer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/server.dir/server/HttpServer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/server/HttpServer.cpp > CMakeFiles/server.dir/server/HttpServer.cpp.i

CMakeFiles/server.dir/server/HttpServer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/server.dir/server/HttpServer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/server/HttpServer.cpp -o CMakeFiles/server.dir/server/HttpServer.cpp.s

CMakeFiles/server.dir/server/Router.cpp.o: CMakeFiles/server.dir/flags.make
CMakeFiles/server.dir/server/Router.cpp.o: /home/<USER>/test_google_cli/test2/server/Router.cpp
CMakeFiles/server.dir/server/Router.cpp.o: CMakeFiles/server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/server.dir/server/Router.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/server.dir/server/Router.cpp.o -MF CMakeFiles/server.dir/server/Router.cpp.o.d -o CMakeFiles/server.dir/server/Router.cpp.o -c /home/<USER>/test_google_cli/test2/server/Router.cpp

CMakeFiles/server.dir/server/Router.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/server.dir/server/Router.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/server/Router.cpp > CMakeFiles/server.dir/server/Router.cpp.i

CMakeFiles/server.dir/server/Router.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/server.dir/server/Router.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/server/Router.cpp -o CMakeFiles/server.dir/server/Router.cpp.s

CMakeFiles/server.dir/server/Middleware.cpp.o: CMakeFiles/server.dir/flags.make
CMakeFiles/server.dir/server/Middleware.cpp.o: /home/<USER>/test_google_cli/test2/server/Middleware.cpp
CMakeFiles/server.dir/server/Middleware.cpp.o: CMakeFiles/server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/server.dir/server/Middleware.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/server.dir/server/Middleware.cpp.o -MF CMakeFiles/server.dir/server/Middleware.cpp.o.d -o CMakeFiles/server.dir/server/Middleware.cpp.o -c /home/<USER>/test_google_cli/test2/server/Middleware.cpp

CMakeFiles/server.dir/server/Middleware.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/server.dir/server/Middleware.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/server/Middleware.cpp > CMakeFiles/server.dir/server/Middleware.cpp.i

CMakeFiles/server.dir/server/Middleware.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/server.dir/server/Middleware.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/server/Middleware.cpp -o CMakeFiles/server.dir/server/Middleware.cpp.s

CMakeFiles/server.dir/server/Request.cpp.o: CMakeFiles/server.dir/flags.make
CMakeFiles/server.dir/server/Request.cpp.o: /home/<USER>/test_google_cli/test2/server/Request.cpp
CMakeFiles/server.dir/server/Request.cpp.o: CMakeFiles/server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/server.dir/server/Request.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/server.dir/server/Request.cpp.o -MF CMakeFiles/server.dir/server/Request.cpp.o.d -o CMakeFiles/server.dir/server/Request.cpp.o -c /home/<USER>/test_google_cli/test2/server/Request.cpp

CMakeFiles/server.dir/server/Request.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/server.dir/server/Request.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/server/Request.cpp > CMakeFiles/server.dir/server/Request.cpp.i

CMakeFiles/server.dir/server/Request.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/server.dir/server/Request.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/server/Request.cpp -o CMakeFiles/server.dir/server/Request.cpp.s

CMakeFiles/server.dir/server/Response.cpp.o: CMakeFiles/server.dir/flags.make
CMakeFiles/server.dir/server/Response.cpp.o: /home/<USER>/test_google_cli/test2/server/Response.cpp
CMakeFiles/server.dir/server/Response.cpp.o: CMakeFiles/server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/server.dir/server/Response.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/server.dir/server/Response.cpp.o -MF CMakeFiles/server.dir/server/Response.cpp.o.d -o CMakeFiles/server.dir/server/Response.cpp.o -c /home/<USER>/test_google_cli/test2/server/Response.cpp

CMakeFiles/server.dir/server/Response.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/server.dir/server/Response.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/server/Response.cpp > CMakeFiles/server.dir/server/Response.cpp.i

CMakeFiles/server.dir/server/Response.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/server.dir/server/Response.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/server/Response.cpp -o CMakeFiles/server.dir/server/Response.cpp.s

CMakeFiles/server.dir/server/Utils.cpp.o: CMakeFiles/server.dir/flags.make
CMakeFiles/server.dir/server/Utils.cpp.o: /home/<USER>/test_google_cli/test2/server/Utils.cpp
CMakeFiles/server.dir/server/Utils.cpp.o: CMakeFiles/server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/server.dir/server/Utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/server.dir/server/Utils.cpp.o -MF CMakeFiles/server.dir/server/Utils.cpp.o.d -o CMakeFiles/server.dir/server/Utils.cpp.o -c /home/<USER>/test_google_cli/test2/server/Utils.cpp

CMakeFiles/server.dir/server/Utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/server.dir/server/Utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test2/server/Utils.cpp > CMakeFiles/server.dir/server/Utils.cpp.i

CMakeFiles/server.dir/server/Utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/server.dir/server/Utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test2/server/Utils.cpp -o CMakeFiles/server.dir/server/Utils.cpp.s

# Object files for target server
server_OBJECTS = \
"CMakeFiles/server.dir/server/HttpServer.cpp.o" \
"CMakeFiles/server.dir/server/Router.cpp.o" \
"CMakeFiles/server.dir/server/Middleware.cpp.o" \
"CMakeFiles/server.dir/server/Request.cpp.o" \
"CMakeFiles/server.dir/server/Response.cpp.o" \
"CMakeFiles/server.dir/server/Utils.cpp.o"

# External object files for target server
server_EXTERNAL_OBJECTS =

libserver.a: CMakeFiles/server.dir/server/HttpServer.cpp.o
libserver.a: CMakeFiles/server.dir/server/Router.cpp.o
libserver.a: CMakeFiles/server.dir/server/Middleware.cpp.o
libserver.a: CMakeFiles/server.dir/server/Request.cpp.o
libserver.a: CMakeFiles/server.dir/server/Response.cpp.o
libserver.a: CMakeFiles/server.dir/server/Utils.cpp.o
libserver.a: CMakeFiles/server.dir/build.make
libserver.a: CMakeFiles/server.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX static library libserver.a"
	$(CMAKE_COMMAND) -P CMakeFiles/server.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/server.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/server.dir/build: libserver.a
.PHONY : CMakeFiles/server.dir/build

CMakeFiles/server.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/server.dir/cmake_clean.cmake
.PHONY : CMakeFiles/server.dir/clean

CMakeFiles/server.dir/depend:
	cd /home/<USER>/test_google_cli/test2/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test2 /home/<USER>/test_google_cli/test2 /home/<USER>/test_google_cli/test2/build /home/<USER>/test_google_cli/test2/build /home/<USER>/test_google_cli/test2/build/CMakeFiles/server.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/server.dir/depend

