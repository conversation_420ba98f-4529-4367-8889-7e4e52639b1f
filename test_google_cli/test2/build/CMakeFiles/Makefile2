# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test2/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/utilities.dir/all
all: CMakeFiles/core.dir/all
all: CMakeFiles/server.dir/all
all: CMakeFiles/js_engine.dir/all
all: CMakeFiles/security.dir/all
all: CMakeFiles/webserver.dir/all
all: CMakeFiles/test_server.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/utilities.dir/clean
clean: CMakeFiles/core.dir/clean
clean: CMakeFiles/server.dir/clean
clean: CMakeFiles/js_engine.dir/clean
clean: CMakeFiles/security.dir/clean
clean: CMakeFiles/webserver.dir/clean
clean: CMakeFiles/test_server.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/utilities.dir

# All Build rule for target.
CMakeFiles/utilities.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=20,21,22,23 "Built target utilities"
.PHONY : CMakeFiles/utilities.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/utilities.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/utilities.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 0
.PHONY : CMakeFiles/utilities.dir/rule

# Convenience name for target.
utilities: CMakeFiles/utilities.dir/rule
.PHONY : utilities

# clean rule for target.
CMakeFiles/utilities.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utilities.dir/build.make CMakeFiles/utilities.dir/clean
.PHONY : CMakeFiles/utilities.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/core.dir

# All Build rule for target.
CMakeFiles/core.dir/all: CMakeFiles/utilities.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=1,2,3 "Built target core"
.PHONY : CMakeFiles/core.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 0
.PHONY : CMakeFiles/core.dir/rule

# Convenience name for target.
core: CMakeFiles/core.dir/rule
.PHONY : core

# clean rule for target.
CMakeFiles/core.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/clean
.PHONY : CMakeFiles/core.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/server.dir

# All Build rule for target.
CMakeFiles/server.dir/all: CMakeFiles/utilities.dir/all
CMakeFiles/server.dir/all: CMakeFiles/core.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=11,12,13,14,15,16,17 "Built target server"
.PHONY : CMakeFiles/server.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 0
.PHONY : CMakeFiles/server.dir/rule

# Convenience name for target.
server: CMakeFiles/server.dir/rule
.PHONY : server

# clean rule for target.
CMakeFiles/server.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/clean
.PHONY : CMakeFiles/server.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/js_engine.dir

# All Build rule for target.
CMakeFiles/js_engine.dir/all: CMakeFiles/utilities.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=4,5,6 "Built target js_engine"
.PHONY : CMakeFiles/js_engine.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/js_engine.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/js_engine.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 0
.PHONY : CMakeFiles/js_engine.dir/rule

# Convenience name for target.
js_engine: CMakeFiles/js_engine.dir/rule
.PHONY : js_engine

# clean rule for target.
CMakeFiles/js_engine.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/js_engine.dir/build.make CMakeFiles/js_engine.dir/clean
.PHONY : CMakeFiles/js_engine.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/security.dir

# All Build rule for target.
CMakeFiles/security.dir/all: CMakeFiles/js_engine.dir/all
CMakeFiles/security.dir/all: CMakeFiles/utilities.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=7,8,9,10 "Built target security"
.PHONY : CMakeFiles/security.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/security.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/security.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 0
.PHONY : CMakeFiles/security.dir/rule

# Convenience name for target.
security: CMakeFiles/security.dir/rule
.PHONY : security

# clean rule for target.
CMakeFiles/security.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security.dir/build.make CMakeFiles/security.dir/clean
.PHONY : CMakeFiles/security.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/webserver.dir

# All Build rule for target.
CMakeFiles/webserver.dir/all: CMakeFiles/server.dir/all
CMakeFiles/webserver.dir/all: CMakeFiles/js_engine.dir/all
CMakeFiles/webserver.dir/all: CMakeFiles/utilities.dir/all
CMakeFiles/webserver.dir/all: CMakeFiles/core.dir/all
CMakeFiles/webserver.dir/all: CMakeFiles/security.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/webserver.dir/build.make CMakeFiles/webserver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/webserver.dir/build.make CMakeFiles/webserver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=24,25 "Built target webserver"
.PHONY : CMakeFiles/webserver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/webserver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/webserver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 0
.PHONY : CMakeFiles/webserver.dir/rule

# Convenience name for target.
webserver: CMakeFiles/webserver.dir/rule
.PHONY : webserver

# clean rule for target.
CMakeFiles/webserver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/webserver.dir/build.make CMakeFiles/webserver.dir/clean
.PHONY : CMakeFiles/webserver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_server.dir

# All Build rule for target.
CMakeFiles/test_server.dir/all: CMakeFiles/server.dir/all
CMakeFiles/test_server.dir/all: CMakeFiles/utilities.dir/all
CMakeFiles/test_server.dir/all: CMakeFiles/core.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_server.dir/build.make CMakeFiles/test_server.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_server.dir/build.make CMakeFiles/test_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/test_google_cli/test2/build/CMakeFiles --progress-num=18,19 "Built target test_server"
.PHONY : CMakeFiles/test_server.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test2/build/CMakeFiles 0
.PHONY : CMakeFiles/test_server.dir/rule

# Convenience name for target.
test_server: CMakeFiles/test_server.dir/rule
.PHONY : test_server

# clean rule for target.
CMakeFiles/test_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_server.dir/build.make CMakeFiles/test_server.dir/clean
.PHONY : CMakeFiles/test_server.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

