<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure JS Interpreter Tester</title>
    <link rel="stylesheet" href="/style.css">
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .mode-selector {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .mode-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .mode-tab {
            padding: 10px 20px;
            border: 1px solid #007bff;
            background-color: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .mode-tab.active {
            background-color: #007bff;
            color: white;
        }
        .mode-tab:hover {
            background-color: #0056b3;
            color: white;
        }
        .credentials-section {
            display: none;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .credentials-section.show {
            display: block;
        }
        .credential-input {
            margin: 10px 0;
        }
        .credential-input label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        .credential-input input {
            width: 300px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        textarea {
            width: 100%;
            height: 300px;
            margin-bottom: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        #output {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            min-height: 100px;
        }
        .security-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .security-info.enterprise {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .security-info.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .examples-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .example-button {
            margin: 5px;
            padding: 8px 12px;
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .example-button:hover {
            background-color: #5a6268;
        }
        .dom-demo {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .demo-element {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        #myDiv {
            border: 2px solid #007bff;
            background-color: #e7f3ff;
            min-height: 40px;
        }
        #myButton {
            padding: 8px 16px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #myButton:hover {
            background-color: #218838;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-standard {
            background-color: #007bff;
        }
        .status-enterprise {
            background-color: #28a745;
        }
        .status-error {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Secure JavaScript Interpreter Tester</h1>
        <p>Test both standard and enterprise JavaScript execution with multi-layer security.</p>

        <!-- Mode Selection -->
        <div class="mode-selector">
            <h3>Execution Mode</h3>
            <div class="mode-tabs">
                <div class="mode-tab active" data-mode="standard">
                    <span class="status-indicator status-standard"></span>
                    Standard Mode
                </div>
                <div class="mode-tab" data-mode="enterprise">
                    <span class="status-indicator status-enterprise"></span>
                    Enterprise Mode
                </div>
            </div>
            
            <div id="mode-description">
                <strong>Standard Mode:</strong> Regular JavaScript execution without security restrictions. 
                Compatible with existing web content.
            </div>

            <!-- Enterprise Credentials Section -->
            <div class="credentials-section" id="credentials-section">
                <h4>🔐 Enterprise Security Credentials</h4>
                <p>Both credentials are required for enterprise content access:</p>
                <div class="credential-input">
                    <label for="secret-key">Secret Key:</label>
                    <input type="password" id="secret-key" placeholder="Enter secret key" value="test_secret">
                </div>
                <div class="credential-input">
                    <label for="enterprise-token">Enterprise Token:</label>
                    <input type="password" id="enterprise-token" placeholder="Enter enterprise token" value="test_token">
                </div>
                <div class="credential-input">
                    <label for="client-id">Client ID:</label>
                    <input type="text" id="client-id" placeholder="Optional client identifier" value="web_client">
                </div>
            </div>
        </div>

        <!-- Code Examples -->
        <div class="examples-section">
            <h3>📝 Code Examples</h3>
            <div>
                <strong>Standard Examples:</strong>
                <button class="example-button" onclick="loadExample('basic')">Basic Math</button>
                <button class="example-button" onclick="loadExample('dom')">DOM Manipulation</button>
                <button class="example-button" onclick="loadExample('functions')">Functions</button>
            </div>
            <div style="margin-top: 10px;">
                <strong>Enterprise Examples:</strong>
                <button class="example-button" onclick="loadExample('secure-data')">Secure Data Access</button>
                <button class="example-button" onclick="loadExample('org-config')">Organization Config</button>
                <button class="example-button" onclick="loadExample('enterprise-functions')">Enterprise Functions</button>
            </div>
        </div>

        <!-- Code Input -->
        <div>
            <h3>JavaScript Code</h3>
            <textarea id="jsCodeInput" placeholder="Enter your JavaScript code here...">// Welcome to the Secure JavaScript Interpreter!
// Try switching between Standard and Enterprise modes

let message = "Hello from ";
let mode = "Standard Mode";
print(message + mode);

// DOM manipulation example
let element = document.getElementById('myDiv');
element.innerHTML = 'Updated by JavaScript!';
element.style.color = 'blue';

print("Code executed successfully!");
</textarea>
        </div>

        <!-- Control Buttons -->
        <div class="button-group">
            <button id="runButton" class="btn-primary">▶️ Run Code</button>
            <button id="clearButton" class="btn-warning">🗑️ Clear Output</button>
            <button onclick="loadExample('demo')" class="btn-success">📋 Load Demo</button>
        </div>

        <!-- Security Status -->
        <div id="security-status" class="security-info">
            <strong>Status:</strong> <span id="status-text">Ready - Standard Mode</span>
        </div>

        <!-- DOM Elements for Testing -->
        <div class="dom-demo">
            <h3>🎯 DOM Elements for Testing</h3>
            <p><strong>These elements can be manipulated by your JavaScript code:</strong></p>
            <div id="myDiv" class="demo-element">
                Original content of myDiv - ready for manipulation
            </div>
            <button id="myButton" class="demo-element">
                Test Button - Click me!
            </button>
            <div style="margin-top: 10px; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;">
                <strong>🎉 Real DOM Manipulation!</strong>
                <p>The interpreter manipulates actual DOM elements on this page in real-time.</p>
            </div>
        </div>

        <!-- Output -->
        <div>
            <h3>📤 Output</h3>
            <div id="output">Ready to execute JavaScript code...</div>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <p><a href="/">← Back to Home</a> | <a href="/js_tester.html">Standard JS Tester</a></p>
        </div>
    </div>

    <script>
        let currentMode = 'standard';
        let sessionId = null;

        // Mode switching
        document.querySelectorAll('.mode-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const mode = tab.dataset.mode;
                switchMode(mode);
            });
        });

        function switchMode(mode) {
            currentMode = mode;
            
            // Update tab appearance
            document.querySelectorAll('.mode-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-mode="${mode}"]`).classList.add('active');
            
            // Update description and credentials visibility
            const description = document.getElementById('mode-description');
            const credentials = document.getElementById('credentials-section');
            const statusText = document.getElementById('status-text');
            
            if (mode === 'enterprise') {
                description.innerHTML = '<strong>Enterprise Mode:</strong> Secure execution with enterprise-specific functions and data access. Requires valid credentials.';
                credentials.classList.add('show');
                statusText.textContent = 'Ready - Enterprise Mode (Credentials Required)';
            } else {
                description.innerHTML = '<strong>Standard Mode:</strong> Regular JavaScript execution without security restrictions. Compatible with existing web content.';
                credentials.classList.remove('show');
                statusText.textContent = 'Ready - Standard Mode';
            }
        }

        // Example loading
        function loadExample(type) {
            const textarea = document.getElementById('jsCodeInput');
            
            const examples = {
                'basic': `// Basic arithmetic and variables
let a = 10;
let b = 20;
let sum = a + b;
print("Sum: " + sum);

// Boolean operations
let isGreater = a > 5;
print("Is a > 5? " + isGreater);`,

                'dom': `// DOM manipulation examples
let div = document.getElementById('myDiv');
div.innerHTML = 'Hello from JavaScript!';
div.style.backgroundColor = 'lightblue';
div.style.padding = '10px';

let button = document.getElementById('myButton');
button.innerHTML = 'Updated Button';
button.style.backgroundColor = 'orange';

print("DOM elements updated!");`,

                'functions': `// Function examples
function greet(name) {
    return "Hello, " + name + "!";
}

function calculate(x, y) {
    return x * y + 10;
}

print(greet("Enterprise User"));
print("Calculation result: " + calculate(5, 3));`,

                'secure-data': `// Enterprise secure data access
// Note: This requires enterprise credentials
let userData = getSecureData('user_profile');
print("User data: " + userData);

let companyInfo = getSecureData('company_info', 'confidential');
print("Company info: " + companyInfo);

// Set secure data
setSecureData('session_data', 'active_session_123');
print("Secure data stored successfully");`,

                'org-config': `// Organization configuration access
let orgName = getOrgConfig('organization_name');
print("Organization: " + orgName);

let securityLevel = getOrgConfig('security_level');
print("Security Level: " + securityLevel);

let maxUsers = getOrgConfig('max_users', 'DEFAULT_ORG');
print("Max Users: " + maxUsers);`,

                'enterprise-functions': `// Enterprise-specific functions
let currentUser = getCurrentUser();
print("Current User: " + currentUser);

// Log a secure event
logSecureEvent('user_action', 'Accessed enterprise dashboard');

// Validate access to a resource
let hasAccess = validateAccess('financial_reports', 'read');
print("Has access to financial reports: " + hasAccess);

// Encrypt sensitive data
let encrypted = encryptData('sensitive information');
print("Encrypted data: " + encrypted);`,

                'demo': `// Comprehensive demo - works in both modes
print("=== Secure JavaScript Interpreter Demo ===");

// Basic operations
let x = 15;
let y = 25;
print("Basic math: " + x + " + " + y + " = " + (x + y));

// DOM manipulation
let element = document.getElementById('myDiv');
element.innerHTML = 'Demo is running...';
element.style.color = 'green';
element.style.fontWeight = 'bold';

// Conditional logic
if (x < y) {
    print("x is less than y");
    element.style.backgroundColor = 'lightgreen';
} else {
    print("x is greater than or equal to y");
    element.style.backgroundColor = 'lightcoral';
}

print("Demo completed successfully!");`
            };
            
            if (examples[type]) {
                textarea.value = examples[type];
                
                // Switch to enterprise mode for enterprise examples
                if (['secure-data', 'org-config', 'enterprise-functions'].includes(type)) {
                    switchMode('enterprise');
                }
            }
        }

        // Run button functionality
        document.getElementById('runButton').addEventListener('click', async () => {
            await runCode();
        });

        // Clear button functionality
        document.getElementById('clearButton').addEventListener('click', () => {
            document.getElementById('output').textContent = 'Output cleared.';
            updateSecurityStatus('Ready - ' + (currentMode === 'enterprise' ? 'Enterprise' : 'Standard') + ' Mode', 'info');
        });

        async function runCode() {
            const code = document.getElementById('jsCodeInput').value;
            const outputDiv = document.getElementById('output');
            
            if (!code.trim()) {
                outputDiv.textContent = 'Please enter some code to execute.';
                return;
            }
            
            outputDiv.textContent = 'Executing...';
            updateSecurityStatus('Executing code...', 'info');

            try {
                let result;
                
                if (currentMode === 'enterprise') {
                    result = await runEnterpriseCode(code);
                } else {
                    result = await runStandardCode(code);
                }
                
                // Display output
                outputDiv.textContent = result.output || 'Code executed successfully (no output)';
                
                // Execute DOM operations
                if (result.dom_operations && result.dom_operations.length > 0) {
                    console.log('Executing DOM operations:', result.dom_operations);
                    
                    for (const operation of result.dom_operations) {
                        try {
                            eval(operation);
                            console.log('Executed:', operation);
                        } catch (domError) {
                            console.error('DOM operation failed:', operation, domError);
                        }
                    }
                }
                
                // Update security status
                if (result.error) {
                    updateSecurityStatus('Execution failed: ' + (result.output || 'Unknown error'), 'error');
                } else {
                    const statusMsg = currentMode === 'enterprise' 
                        ? `Enterprise execution successful (Security Level: ${result.security_level || 'N/A'})`
                        : 'Standard execution successful';
                    updateSecurityStatus(statusMsg, currentMode === 'enterprise' ? 'enterprise' : 'info');
                    
                    if (result.session_id) {
                        sessionId = result.session_id;
                    }
                }
                
            } catch (error) {
                outputDiv.textContent = `Error: ${error.message}`;
                updateSecurityStatus('Execution error: ' + error.message, 'error');
                console.error('Error running code:', error);
            }
        }

        async function runStandardCode(code) {
            const encodedCode = encodeURIComponent(code);
            const response = await fetch(`/run_js?code=${encodedCode}&real_dom=true`);
            return await response.json();
        }

        async function runEnterpriseCode(code) {
            const secretKey = document.getElementById('secret-key').value;
            const enterpriseToken = document.getElementById('enterprise-token').value;
            const clientId = document.getElementById('client-id').value || 'web_client';
            
            if (!secretKey || !enterpriseToken) {
                throw new Error('Enterprise credentials are required');
            }
            
            const encodedCode = encodeURIComponent(code);
            const url = `/run_js_secure?code=${encodedCode}&secret_key=${encodeURIComponent(secretKey)}&enterprise_token=${encodeURIComponent(enterpriseToken)}&client_id=${encodeURIComponent(clientId)}&real_dom=true`;
            
            const response = await fetch(url);
            return await response.json();
        }

        function updateSecurityStatus(message, type) {
            const statusDiv = document.getElementById('security-status');
            const statusText = document.getElementById('status-text');
            
            statusText.textContent = message;
            
            // Reset classes
            statusDiv.className = 'security-info';
            
            // Add appropriate class
            if (type === 'enterprise') {
                statusDiv.classList.add('enterprise');
            } else if (type === 'error') {
                statusDiv.classList.add('error');
            }
        }

        // Initialize
        switchMode('standard');
    </script>
</body>
</html>
