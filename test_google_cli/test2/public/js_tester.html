<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JS Interpreter Tester</title>
    <link rel="stylesheet" href="/style.css">
    <style>
        textarea {
            width: 80%;
            height: 200px;
            margin-bottom: 10px;
            padding: 10px;
            font-family: monospace;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        #output {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            white-space: pre-wrap; /* Preserve whitespace and wrap text */
            font-family: monospace;
        }
        .dom-demo {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .demo-element {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        #myDiv {
            border: 2px solid #007bff;
            background-color: #e7f3ff;
            min-height: 40px;
        }
        #myButton {
            padding: 8px 16px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #myButton:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <h1>Rudimentary JS Interpreter Tester</h1>
    <p>Enter your JavaScript-like code below and click "Run".</p>
    <p>Supported features: <code>let</code>, <code>print()</code>, basic arithmetic, boolean literals (<code>true</code>, <code>false</code>), comparisons (<code>==</code>, <code>!=</code>, <code><</code>, <code>></code>, <code><=</code>, <code>>=</code>), <code>if-else</code> statements, <code>while</code> loops, string literals, string concatenation, functions, logical operators (<code>&&</code>, <code>||</code>, <code>!</code>), comments (<code>//</code>, <code>/* */</code>), unary minus (<code>-</code>), and <b>DOM manipulation</b>.</p>
    <p>Example: <code>let x = 10 + 5; if (x > 12) { print(true); } else { print(false); }</code></p>
    <p>While loop example: <code>let i = 0; while (i < 3) { print(i); i = i + 1; }</code></p>
    <p>String example: <code>let greeting = 'Hello'; let name = 'World'; print(greeting + ', ' + name + '!');</code></p>
    <p>Function example: <code>function add(a, b) { return a + b; } print(add(5, 3));</code></p>
    <p>Logical operator example: <code>print(true && false); print(!(10 > 5));</code></p>
    <p>Unary minus example: <code>let x = 10; print(-x); print(-(5 + 3));</code></p>
    <p><b>DOM manipulation examples:</b></p>
    <p>Get element: <code>let element = document.getElementById('myDiv'); print(element);</code></p>
    <p>Set content: <code>element.innerHTML = 'Hello World'; print(element.innerHTML);</code></p>
    <p>Set styles: <code>element.style.color = 'red'; element.style.fontSize = '20px';</code></p>
    <p>Create element: <code>let newDiv = document.createElement('div'); newDiv.innerHTML = 'New content';</code></p>
    <p><strong>💡 Try this simple example:</strong> <code>let div = document.getElementById('myDiv'); div.innerHTML = 'Hello!'; print(div.innerHTML);</code></p>

    <textarea id="jsCodeInput">// This is a single-line comment
/* This is a
   multi-line comment */
let a = 5;
let b = 10;
if (a < b) {
    print(a + b);
} else {
    print(a * b);
}
print(true == false);
print(10 >= 10);

let i = 0;
while (i < 3) {
    print("Loop iteration: " + i);
    i = i + 1;
}

let firstName = 'John';
let lastName = 'Doe';
print('Full Name: ' + firstName + ' ' + lastName);
print('The answer is: ' + (10 * 5));

function multiply(x, y) {
    return x * y;
}
print("Result of multiply(4, 6): " + multiply(4, 6));

function greet(name) {
    print('Hello, ' + name + '!');
}
greet('Alice');

function factorial(n) {
    if (n == 0) {
        return 1;
    } else {
        return n * factorial(n - 1);
    }
}
print("Factorial of 5: " + factorial(5));

// Logical operators
print("Logical AND (true && false): " + (true && false));
print("Logical OR (true || false): " + (true || false));
print("Logical NOT (!(10 > 5)): " + (!(10 > 5)));
print("Complex logical: " + ((5 > 3) && (10 < 20) || false));

// Unary minus
let num = 25;
print("Unary minus of num: " + (-num));
print("Unary minus of expression: " + (-(10 + 5)));

// DOM manipulation examples
print("=== DOM Examples ===");

// Get existing elements
let element = document.getElementById('myDiv');
print("Found element: " + element);
print("Original content: " + element.innerHTML);

// Modify content and styles
element.innerHTML = 'Hello from JavaScript!';
element.style.color = 'blue';
element.style.fontSize = '18px';
print("Updated content: " + element.innerHTML);
print("Color set to: " + element.style.color);

// Create new elements
let newElement = document.createElement('span');
newElement.innerHTML = 'Dynamically created content';
newElement.style.backgroundColor = 'yellow';
newElement.style.padding = '5px';
print("New element content: " + newElement.innerHTML);
print("Background color: " + newElement.style.backgroundColor);

// Test with button
let button = document.getElementById('myButton');
print("Button found: " + button);
print("Button text: " + button.innerHTML);
button.innerHTML = 'Updated Button';
button.style.backgroundColor = 'green';
print("Button updated to: " + button.innerHTML);
</textarea><br>
    <button id="runButton">Run Code</button>

    <h2>DOM Elements for Testing:</h2>
    <div class="dom-demo">
        <p><strong>These elements will be manipulated by your JavaScript code in real-time:</strong></p>
        <div id="myDiv" class="demo-element">
            Original content of myDiv
        </div>
        <button id="myButton" class="demo-element">
            Click me
        </button>
        <div style="margin-top: 10px; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;">
            <strong>🎉 Real DOM Manipulation!</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li>The interpreter now manipulates <strong>real DOM elements</strong> on this page!</li>
                <li>When you run <code>document.getElementById('myDiv')</code>, it finds the actual element above</li>
                <li>Changes like <code>element.innerHTML = 'new text'</code> will <strong>visually update</strong> the elements</li>
                <li>The interpreter generates JavaScript code that executes in your browser</li>
                <li><strong>Try it!</strong> Run DOM manipulation code and watch the elements change in real-time</li>
            </ul>
        </div>
    </div>

    <h2>Output:</h2>
    <div id="output"></div>

    <p>Go back to <a href="/">home</a>.</p>

    <script>
        document.getElementById('runButton').addEventListener('click', async () => {
            const code = document.getElementById('jsCodeInput').value;
            const outputDiv = document.getElementById('output');
            outputDiv.textContent = 'Running...';

            try {
                // URL-encode the code to safely pass it as a query parameter
                const encodedCode = encodeURIComponent(code);

                // Request real DOM manipulation
                const response = await fetch(`/run_js?code=${encodedCode}&real_dom=true`);
                const result = await response.json();

                // Display the output
                outputDiv.textContent = result.output;

                // Execute DOM operations on the real page elements
                if (result.dom_operations && result.dom_operations.length > 0) {
                    console.log('Executing DOM operations:', result.dom_operations);

                    for (const operation of result.dom_operations) {
                        try {
                            // Execute the JavaScript DOM operation
                            eval(operation);
                            console.log('Executed:', operation);
                        } catch (domError) {
                            console.error('DOM operation failed:', operation, domError);
                        }
                    }
                }

            } catch (error) {
                outputDiv.textContent = `Error: ${error.message}`;
                console.error('Error running JS code:', error);
            }
        });
    </script>
</body>
</html>