#include "Tokenizer.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <sstream>

std::string Token::to_string() const {
    std::string type_str;
    switch (type) {
        case END_OF_FILE: type_str = "END_OF_FILE"; break;
        case IDENTIFIER: type_str = "IDENTIFIER"; break;
        case NUMBER: type_str = "NUMBER"; break;
        case PLUS: type_str = "PLUS"; break;
        case MINUS: type_str = "MINUS"; break;
        case MULTIPLY: type_str = "MULTIPLY"; break;
        case DIVIDE: type_str = "DIVIDE"; break;
        case ASSIGN: type_str = "ASSIGN"; break;
        case SEMICOLON: type_str = "SEMICOLON"; break;
        case LPAREN: type_str = "LPAREN"; break;
        case RPAREN: type_str = "RPAREN"; break;
        case LBRACE: type_str = "LBRACE"; break;
        case RBRACE: type_str = "RBRACE"; break;
        case KEYWORD_LET: type_str = "KEYWORD_LET"; break;
        case KEYWORD_PRINT: type_str = "KEYWORD_PRINT"; break;
        case KEYWORD_TRUE: type_str = "KEYWORD_TRUE"; break;
        case KEYWORD_FALSE: type_str = "KEYWORD_FALSE"; break;
        case EQ_EQ: type_str = "EQ_EQ"; break;
        case NEQ: type_str = "NEQ"; break;
        case LT: type_str = "LT"; break;
        case GT: type_str = "GT"; break;
        case LTE: type_str = "LTE"; break;
        case GTE: type_str = "GTE"; break;
        case KEYWORD_IF: type_str = "KEYWORD_IF"; break;
        case KEYWORD_ELSE: type_str = "KEYWORD_ELSE"; break;
        case KEYWORD_WHILE: type_str = "KEYWORD_WHILE"; break;
        case STRING_LITERAL: type_str = "STRING_LITERAL"; break;
        case KEYWORD_FUNCTION: type_str = "KEYWORD_FUNCTION"; break;
        case COMMA: type_str = "COMMA"; break;
        case KEYWORD_RETURN: type_str = "KEYWORD_RETURN"; break;
        case AND_AND: type_str = "AND_AND"; break;
        case OR_OR: type_str = "OR_OR"; break;
        case NOT: type_str = "NOT"; break;
        // DOM-related tokens
        case DOT: type_str = "DOT"; break;
        case KEYWORD_DOCUMENT: type_str = "KEYWORD_DOCUMENT"; break;
        case KEYWORD_GETELEMENTBYID: type_str = "KEYWORD_GETELEMENTBYID"; break;
        case KEYWORD_INNERHTML: type_str = "KEYWORD_INNERHTML"; break;
        case KEYWORD_STYLE: type_str = "KEYWORD_STYLE"; break;
        case KEYWORD_ADDEVENTLISTENER: type_str = "KEYWORD_ADDEVENTLISTENER"; break;
        case KEYWORD_CREATEELEMENT: type_str = "KEYWORD_CREATEELEMENT"; break;
        case KEYWORD_APPENDCHILD: type_str = "KEYWORD_APPENDCHILD"; break;
    }
    return "Token(" + type_str + ", \"" + value + "\")";
}

Tokenizer::Tokenizer(const std::string& code)
    : code_(code), current_pos_(0), initialized_(false), max_safe_position_(0) {

    // Initialize logging and error handling
    logger_ = utilities::Logger::getLogger("Tokenizer");
    error_handler_ = std::make_shared<utilities::ErrorHandler>(logger_);

    try {
        // Validate input code
        if (code_.empty()) {
            logger_->warning("Tokenizer initialized with empty code string");
        } else if (code_.size() > 1000000) { // 1MB limit for safety
            handle_tokenizer_error("Code string too large", "Size: " + std::to_string(code_.size()));
            return;
        }

        max_safe_position_ = code_.size();
        initialized_ = true;

        logger_->debug("Tokenizer initialized successfully", "Tokenizer", "constructor");
        log_tokenizer_state("initialization");

    } catch (const std::exception& e) {
        handle_tokenizer_error("Failed to initialize tokenizer", e.what());
        initialized_ = false;
    }
}

void Tokenizer::skip_whitespace() {
    try {
        while (is_safe_to_access(current_pos_)) {
            char current_char = safe_char_at(current_pos_);
            if (std::isspace(current_char)) {
                current_pos_++;
            } else if (current_char == '/' && is_safe_to_access(current_pos_ + 1)) {
            char next_char = code_[current_pos_ + 1];
            if (next_char == '/') { // Single-line comment
                while (current_pos_ < code_.length() && code_[current_pos_] != '\n') {
                    current_pos_++;
                }
            } else if (next_char == '*') { // Multi-line comment
                current_pos_ += 2; // Consume /*
                while (current_pos_ + 1 < code_.length() && !(code_[current_pos_] == '*' && code_[current_pos_ + 1] == '/')) {
                    current_pos_++;
                }
                if (current_pos_ + 1 < code_.length()) {
                    current_pos_ += 2; // Consume */
                }
            } else {
                break; // Not a comment, break from whitespace/comment skipping
            }
        } else {
            break; // Not whitespace or comment, stop skipping
        }
    }

    } catch (const std::exception& e) {
        handle_tokenizer_error("Exception in skip_whitespace", e.what());
    }
}

Token Tokenizer::read_number() {
    std::string value;
    while (current_pos_ < code_.length() && std::isdigit(code_[current_pos_])) {
        value += code_[current_pos_];
        current_pos_++;
    }
    return {NUMBER, value};
}

Token Tokenizer::read_identifier_or_keyword() {
    std::string value;
    while (current_pos_ < code_.length() && (std::isalnum(code_[current_pos_]) || code_[current_pos_] == '_')) {
        value += code_[current_pos_];
        current_pos_++;
    }
    if (value == "let") return {KEYWORD_LET, value};
    if (value == "print") return {KEYWORD_PRINT, value};
    if (value == "true") return {KEYWORD_TRUE, value};
    if (value == "false") return {KEYWORD_FALSE, value};
    if (value == "if") return {KEYWORD_IF, value};
    if (value == "else") return {KEYWORD_ELSE, value};
    if (value == "while") return {KEYWORD_WHILE, value};
    if (value == "function") return {KEYWORD_FUNCTION, value};
    if (value == "return") return {KEYWORD_RETURN, value};
    // DOM-related keywords
    if (value == "document") return {KEYWORD_DOCUMENT, value};
    if (value == "getElementById") return {KEYWORD_GETELEMENTBYID, value};
    if (value == "innerHTML") return {KEYWORD_INNERHTML, value};
    if (value == "style") return {KEYWORD_STYLE, value};
    if (value == "addEventListener") return {KEYWORD_ADDEVENTLISTENER, value};
    if (value == "createElement") return {KEYWORD_CREATEELEMENT, value};
    if (value == "appendChild") return {KEYWORD_APPENDCHILD, value};
    return {IDENTIFIER, value};
}

Token Tokenizer::read_string_literal() {
    std::string value;
    char quote_char = code_[current_pos_]; // Remember the opening quote (either ' or ")
    current_pos_++; // Consume the opening quote
    while (current_pos_ < code_.length() && code_[current_pos_] != quote_char) {
        // Basic escape sequence handling (e.g., \n, \t, \" could be added)
        if (code_[current_pos_] == '\\' && current_pos_ + 1 < code_.length()) {
            char escaped_char = code_[current_pos_ + 1];
            switch (escaped_char) {
                case 'n': value += '\n'; break;
                case 't': value += '\t'; break;
                case '\'': value += '\''; break;
                case '"': value += '"'; break;
                case '\\': value += '\\'; break;
                default: value += escaped_char; // For unsupported escapes, just add the char
            }
            current_pos_ += 2; // Consume backslash and escaped char
        } else {
            value += code_[current_pos_];
            current_pos_++;
        }
    }
    if (current_pos_ >= code_.length()) {
        throw std::runtime_error("Syntax Error: Unterminated string literal");
    }
    current_pos_++; // Consume the closing quote
    return {STRING_LITERAL, value};
}

Token Tokenizer::read_operator() {
    char current_char = code_[current_pos_];
    char next_char = (current_pos_ + 1 < code_.length()) ? code_[current_pos_ + 1] : '\0';

    if (current_char == '=' && next_char == '=') { current_pos_ += 2; return {EQ_EQ, "=="}; }
    if (current_char == '!' && next_char == '=') { current_pos_ += 2; return {NEQ, "!="}; }
    if (current_char == '<' && next_char == '=') { current_pos_ += 2; return {LTE, "<="}; }
    if (current_char == '>' && next_char == '=') { current_pos_ += 2; return {GTE, ">="}; }
    if (current_char == '&' && next_char == '&') { current_pos_ += 2; return {AND_AND, "&&"}; }
    if (current_char == '|' && next_char == '|') { current_pos_ += 2; return {OR_OR, "||"}; }

    // Single character operators
    switch (current_char) {
        case '+': current_pos_++; return {PLUS, "+"};
        case '-': current_pos_++; return {MINUS, "-"};
        case '*': current_pos_++; return {MULTIPLY, "*"};
        case '/': current_pos_++; return {DIVIDE, "/"};
        case '=': current_pos_++; return {ASSIGN, "="};
        case ';': current_pos_++; return {SEMICOLON, ";"};
        case '(': current_pos_++; return {LPAREN, "("};
        case ')': current_pos_++; return {RPAREN, ")"};
        case '{': current_pos_++; return {LBRACE, "{"};
        case '}': current_pos_++; return {RBRACE, "}"};
        case '<': current_pos_++; return {LT, "<"};
        case '>': current_pos_++; return {GT, ">"};
        case '!': current_pos_++; return {NOT, "!"};
        case '.': current_pos_++; return {DOT, "."};
        default: return {END_OF_FILE, ""}; // Should not be reached if called correctly
    }
}

Token Tokenizer::get_next_token() {
    try {
        // Validate tokenizer state before processing
        if (!is_valid_state()) {
            handle_tokenizer_error("Invalid tokenizer state", "Position: " + std::to_string(current_pos_));
            return {END_OF_FILE, ""};
        }

        logger_->debug("Getting next token", "Tokenizer", "get_next_token", current_pos_);

        skip_whitespace();

        if (current_pos_ >= code_.length()) {
            logger_->debug("Reached end of code", "Tokenizer", "get_next_token", current_pos_);
            return {END_OF_FILE, ""};
        }

        char current_char = safe_char_at(current_pos_);
        if (current_char == '\0') {
            handle_tokenizer_error("Null character encountered", "Position: " + std::to_string(current_pos_));
            return {END_OF_FILE, ""};
        }

    if (std::isdigit(current_char)) {
        return read_number();
    }

    if (current_char == '\'' || current_char == '"') {
        return read_string_literal();
    }

    if (std::isalpha(current_char) || current_char == '_') {
        return read_identifier_or_keyword();
    }

    // Check for multi-character operators first
    if (current_char == '=' || current_char == '!' || current_char == '<' || current_char == '>' || current_char == '&' || current_char == '|') {
        if (current_pos_ + 1 < code_.length() && 
           ((current_char == '=' && code_[current_pos_ + 1] == '=') ||
            (current_char == '!' && code_[current_pos_ + 1] == '=') ||
            (current_char == '<' && code_[current_pos_ + 1] == '=') ||
            (current_char == '>' && code_[current_pos_ + 1] == '=') ||
            (current_char == '&' && code_[current_pos_ + 1] == '&') ||
            (current_char == '|' && code_[current_pos_ + 1] == '|'))) {
            return read_operator();
        }
    }

    // Single character operators and braces
    switch (current_char) {
        case '+': current_pos_++; return {PLUS, "+"};
        case '-': current_pos_++; return {MINUS, "-"};
        case '*': current_pos_++; return {MULTIPLY, "*"};
        case '/': current_pos_++; return {DIVIDE, "/"};
        case '=': current_pos_++; return {ASSIGN, "="};
        case ';': current_pos_++; return {SEMICOLON, ";"};
        case '(': current_pos_++; return {LPAREN, "("};
        case ')': current_pos_++; return {RPAREN, ")"};
        case '{': current_pos_++; return {LBRACE, "{"};
        case '}': current_pos_++; return {RBRACE, "}"};
        case '<': current_pos_++; return {LT, "<"};
        case '>': current_pos_++; return {GT, ">"};
        case '!': current_pos_++; return {NOT, "!"};
        case ',': current_pos_++; return {COMMA, ","};
        case '.': current_pos_++; return {DOT, "."};
        default:
            handle_tokenizer_error("Unexpected character",
                                 "Character: '" + std::string(1, current_char) +
                                 "' at position " + std::to_string(current_pos_));
            current_pos_++;
            return {END_OF_FILE, ""};
    }

    } catch (const std::exception& e) {
        handle_tokenizer_error("Exception in get_next_token", e.what());
        return {END_OF_FILE, ""};
    }
}

void Tokenizer::set_current_pos(size_t pos) {
    try {
        validate_position(pos);
        logger_->debug("Setting position", "Tokenizer", "set_current_pos",
                      static_cast<int>(pos));
        current_pos_ = pos;
        log_tokenizer_state("position_set");
    } catch (const std::exception& e) {
        handle_tokenizer_error("Failed to set position", e.what());
        // Don't change position on error
    }
}

size_t Tokenizer::get_current_pos() const {
    return current_pos_;
}

const std::string& Tokenizer::get_code() const {
    return code_;
}

// Enhanced error handling and validation methods
bool Tokenizer::is_valid_state() const {
    return initialized_ && current_pos_ <= max_safe_position_;
}

void Tokenizer::validate_position(size_t pos) const {
    if (!initialized_) {
        utilities::ValidationError error("Tokenizer not properly initialized");
        error.setContext("Tokenizer", "validate_position", __LINE__);
        throw error;
    }
    if (pos > max_safe_position_) {
        utilities::ValidationError error("Position out of bounds: " + std::to_string(pos) +
                                       " > " + std::to_string(max_safe_position_));
        error.setContext("Tokenizer", "validate_position", __LINE__);
        throw error;
    }
}

void Tokenizer::reset_state() {
    try {
        logger_->debug("Resetting tokenizer state", "Tokenizer", "reset_state");
        current_pos_ = 0;
        if (initialized_) {
            log_tokenizer_state("reset");
        }
    } catch (const std::exception& e) {
        handle_tokenizer_error("Failed to reset state", e.what());
    }
}

void Tokenizer::log_tokenizer_state(const std::string& operation) const {
    if (!logger_) return;

    try {
        std::ostringstream oss;
        oss << "Tokenizer state - Operation: " << operation
            << ", Position: " << current_pos_
            << ", Code length: " << code_.size()
            << ", Initialized: " << (initialized_ ? "true" : "false");

        logger_->debug(oss.str(), "Tokenizer", "log_tokenizer_state");
    } catch (const std::exception& e) {
        // Avoid recursive error handling in logging
        std::cerr << "Failed to log tokenizer state: " << e.what() << std::endl;
    }
}

void Tokenizer::handle_tokenizer_error(const std::string& error_msg, const std::string& context) const {
    try {
        if (logger_) {
            logger_->error("Tokenizer error: " + error_msg +
                          (context.empty() ? "" : " - Context: " + context),
                          "Tokenizer", "handle_tokenizer_error");
        }

        if (error_handler_) {
            utilities::ProcessingError exception(error_msg, "TOKENIZER_ERROR");
            exception.setContext("Tokenizer", "handle_tokenizer_error", __LINE__);
            error_handler_->handleError(exception);
        }
    } catch (const std::exception& e) {
        // Fallback error handling
        std::cerr << "Critical tokenizer error: " << error_msg << " - " << e.what() << std::endl;
    }
}

bool Tokenizer::is_safe_to_access(size_t pos) const {
    return initialized_ && pos < max_safe_position_;
}

char Tokenizer::safe_char_at(size_t pos) const {
    if (!is_safe_to_access(pos)) {
        handle_tokenizer_error("Unsafe character access", "Position: " + std::to_string(pos));
        return '\0';
    }
    return code_[pos];
}
