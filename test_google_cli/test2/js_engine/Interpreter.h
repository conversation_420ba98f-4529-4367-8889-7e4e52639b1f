#ifndef INTERPRETER_H
#define INTERPRETER_H

#include "Tokenizer.h"
#include <string>
#include <map>
#include <vector>
#include <stdexcept>
#include <variant> // For std::variant (C++17) - if not available, will use custom union/enum
#include <functional> // For std::function
#include <memory>
#include "../utilities/Logger.h"
#include "../utilities/ErrorHandler.h"

// Custom Value type to hold either int or string
enum ValueType {
    INT_TYPE,
    STRING_TYPE,
    BOOL_TYPE, // Booleans are still ints (0 or 1) but conceptually distinct
    VOID_TYPE, // For functions that don't explicitly return a value
    DOM_ELEMENT_TYPE, // For DOM elements
    DOM_DOCUMENT_TYPE // For the document object
};

// Forward declarations
struct DOMElement;
struct DOMDocument;

// DOM Element structure
struct DOMElement {
    std::string id;
    std::string tagName;
    std::string innerHTML;
    std::map<std::string, std::string> style; // CSS properties
    std::vector<std::shared_ptr<DOMElement>> children;
    std::weak_ptr<DOMElement> parent;

    DOMElement(const std::string& tag = "div", const std::string& element_id = "")
        : id(element_id), tagName(tag), innerHTML("") {}
};

// DOM Document structure
struct DOMDocument {
    std::map<std::string, std::shared_ptr<DOMElement>> elements_by_id;
    std::shared_ptr<DOMElement> body;

    DOMDocument() {
        body = std::make_shared<DOMElement>("body", "");
    }

    std::shared_ptr<DOMElement> getElementById(const std::string& id) {
        auto it = elements_by_id.find(id);
        return (it != elements_by_id.end()) ? it->second : nullptr;
    }

    std::shared_ptr<DOMElement> createElement(const std::string& tagName) {
        return std::make_shared<DOMElement>(tagName);
    }
};

struct Value {
    ValueType type;
    int int_value;
    std::string string_value;
    std::shared_ptr<DOMElement> dom_element;
    std::shared_ptr<DOMDocument> dom_document;

    // Constructors
    Value() : type(VOID_TYPE), int_value(0) {} // Default to VOID
    Value(int val) : type(INT_TYPE), int_value(val) {}
    Value(std::string val) : type(STRING_TYPE), string_value(std::move(val)) {}
    Value(bool val) : type(BOOL_TYPE), int_value(val ? 1 : 0) {}
    Value(std::shared_ptr<DOMElement> elem) : type(DOM_ELEMENT_TYPE), dom_element(elem) {}
    Value(std::shared_ptr<DOMDocument> doc) : type(DOM_DOCUMENT_TYPE), dom_document(doc) {}

    // Helper to convert to int (for boolean/arithmetic contexts)
    int as_int() const {
        if (type == INT_TYPE || type == BOOL_TYPE) return int_value;
        throw std::runtime_error("Type Error: Cannot convert " + std::to_string(type) + " to int");
    }

    // Helper to convert to string
    std::string as_string() const {
        if (type == STRING_TYPE) return string_value;
        if (type == INT_TYPE) return std::to_string(int_value);
        if (type == BOOL_TYPE) return (int_value == 1 ? "true" : "false");
        if (type == VOID_TYPE) return "undefined"; // JavaScript's undefined
        if (type == DOM_ELEMENT_TYPE) return "[object HTMLElement]";
        if (type == DOM_DOCUMENT_TYPE) return "[object Document]";
        return ""; // Should not happen
    }

    // Alias for as_string() for compatibility
    std::string to_string() const {
        return as_string();
    }

    // Equality comparison for Value (simplified)
    bool operator==(const Value& other) const {
        if (type != other.type) {
            // Simplified type coercion for comparison (e.g., 0 == false)
            if ((type == INT_TYPE || type == BOOL_TYPE) && (other.type == INT_TYPE || other.type == BOOL_TYPE)) {
                return int_value == other.int_value;
            }
            return false;
        }
        if (type == INT_TYPE || type == BOOL_TYPE) return int_value == other.int_value;
        if (type == STRING_TYPE) return string_value == other.string_value;
        return false;
    }
};

// Structure to hold function definition
struct FunctionDefinition {
    std::vector<std::string> parameters;
    size_t body_start_pos; // Position in the code string where the function body starts
    size_t body_end_pos;   // Position in the code string where the function body ends
};

class Interpreter {
public:
    Interpreter();
    std::string interpret(const std::string& code, std::function<void(const std::string&)> print_callback);

    // Error handling and validation methods
    bool is_valid_state() const;
    void reset_interpreter_state();
    void validate_interpreter_state() const;

    // Real DOM bridge methods (public interface)
    void enable_real_dom(bool enable = true); // Enable/disable real DOM manipulation
    std::vector<std::string> get_dom_operations() const; // Get queued DOM operations
    void clear_dom_operations(); // Clear the DOM operations queue

private:
    // Stack of variable scopes (each map represents a scope)
    std::vector<std::map<std::string, Value>> call_stack_;
    std::map<std::string, FunctionDefinition> function_definitions_;

    Tokenizer* tokenizer_;
    Token current_token_;
    std::function<void(const std::string&)> print_callback_;
    bool returned_; // Flag to indicate if a return statement was executed
    Value return_value_; // Value returned by a function

    // DOM-related members
    std::shared_ptr<DOMDocument> document_; // The global document object

    // Real DOM bridge
    std::vector<std::string> dom_operations_queue_; // Queue of JavaScript DOM operations to execute in browser
    bool use_real_dom_; // Flag to enable real DOM manipulation

    // Error handling and logging
    std::shared_ptr<utilities::Logger> logger_;
    std::shared_ptr<utilities::ErrorHandler> error_handler_;
    bool initialized_;
    size_t max_call_stack_depth_;
    size_t current_recursion_depth_;

    void eat(TokenType type);
    Value parse_expression();
    Value parse_term();
    Value parse_factor();
    Value parse_boolean_expression();
    void parse_statement();
    void parse_block();
    void parse_program();
    void handle_print_statement();
    void handle_if_statement();
    void handle_while_statement();
    void parse_function_declaration(); // New: for function declarations
    Value parse_function_call(const std::string& function_name); // New: for function calls
    void handle_return_statement(); // New: for return statements
    void consume_block(); // New: to consume a block without executing it

    // DOM-related parsing methods
    Value parse_dom_expression(); // Parse DOM operations like document.getElementById
    Value parse_property_access(const Value& object); // Parse .property access
    void handle_dom_assignment(const Value& object, const std::string& property, const Value& value); // Handle property assignments

    // DOM utility methods
    void initialize_dom(); // Initialize the global document object
    std::shared_ptr<DOMElement> create_mock_element(const std::string& id, const std::string& tagName = "div"); // Create mock DOM elements for testing



    // Helper for variable lookup in current and outer scopes
    Value& get_variable(const std::string& name);
    bool has_variable(const std::string& name) const;
    void set_variable(const std::string& name, const Value& value);

    // Enhanced error handling and safety methods
    void log_interpreter_state(const std::string& operation) const;
    void handle_interpreter_error(const std::string& error_msg, const std::string& context = "") const;
    void validate_call_stack() const;
    void validate_tokenizer_state() const;
    void safe_push_scope();
    void safe_pop_scope();
    bool is_safe_recursion_depth() const;
    void increment_recursion_depth();
    void decrement_recursion_depth();
    Value safe_parse_expression();
    void safe_parse_statement();

    // Private DOM bridge helper methods
    std::string generate_dom_js_code(const std::string& operation, const std::vector<std::string>& params); // Generate JavaScript code for DOM operations
    void queue_dom_operation(const std::string& js_code); // Queue DOM operations to be executed in browser
};

#endif // INTERPRETER_H
